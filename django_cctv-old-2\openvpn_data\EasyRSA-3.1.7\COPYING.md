Easy-RSA -- A Shell-based CA Utility
====================================

Copyright (C) 2013 by the Open-Source OpenVPN development community

Easy-RSA 3 license: GPLv2
-------------------------

All the Easy-RSA code contained in this project falls under a GPLv2 license with
full text available in the Licensing/ directory. Additional components used by
this project fall under additional licenses:

Additional licenses for external components
-------------------------------------------

The following components are under different licenses; while not part of the
Easy-RSA source code, these components are used by Easy-RSA or provided in
platform distributions as described below:

### OpenSSL

  OpenSSL is not linked by Easy-RSA, nor is it currently provided in any release
  package by Easy-RSA. However, Easy-RSA is tightly coupled with OpenSSL, so
  effective use of this code will require your acceptance and installation of
  OpenSSL.

### Additional Windows Components

  The Windows binary package includes mksh/Win32 and unxutils binary components,
  with full licensing details available in the distro/windows/Licensing/
  subdirectory of this project. mksh/Win32 is under a MirOS license (with some
  additional component licenses present there) and unxutils is under a GPLv2
  license.
