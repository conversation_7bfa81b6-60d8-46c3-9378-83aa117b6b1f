<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎖️ Face Recognition Deletion Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .endpoint-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .method {
            background: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        .url {
            background: #343a40;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .example {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .delete-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .delete-button:hover {
            background: #c82333;
        }
        #results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎖️ Face Recognition Deletion Guide</h1>
        
        <div class="warning">
            <strong>⚠️ WARNING:</strong> All deletion operations are PERMANENT and cannot be undone. 
            Always backup your data before performing deletions.
        </div>

        <!-- Delete by ID -->
        <div class="endpoint-section">
            <h2>🗑️ Delete Person by ID</h2>
            <div class="method">DELETE</div>
            <div class="url">http://localhost:8090/api/v1/persons/{person_id}?confirm=true</div>
            
            <div class="example">
                <strong>Example:</strong><br>
                DELETE http://localhost:8090/api/v1/persons/1?confirm=true
            </div>
            
            <div>
                <input type="number" id="deleteId" placeholder="Enter Person ID" min="1">
                <button class="delete-button" onclick="deletePersonById()">🗑️ Delete by ID</button>
            </div>
        </div>

        <!-- Delete by Name -->
        <div class="endpoint-section">
            <h2>🗑️ Delete Person by Name</h2>
            <div class="method">DELETE</div>
            <div class="url">http://localhost:8090/api/v1/persons/by-name/{person_name}?confirm=true</div>
            
            <div class="example">
                <strong>Example:</strong><br>
                DELETE http://localhost:8090/api/v1/persons/by-name/John%20Doe?confirm=true
            </div>
            
            <div>
                <input type="text" id="deleteName" placeholder="Enter Person Name">
                <button class="delete-button" onclick="deletePersonByName()">🗑️ Delete by Name</button>
            </div>
        </div>

        <!-- Delete by Employee ID -->
        <div class="endpoint-section">
            <h2>🗑️ Delete Person by Employee ID</h2>
            <div class="method">DELETE</div>
            <div class="url">http://localhost:8090/api/v1/persons/by-employee-id/{employee_id}?confirm=true</div>
            
            <div class="example">
                <strong>Example:</strong><br>
                DELETE http://localhost:8090/api/v1/persons/by-employee-id/EMP001?confirm=true
            </div>
            
            <div>
                <input type="text" id="deleteEmployeeId" placeholder="Enter Employee ID">
                <button class="delete-button" onclick="deletePersonByEmployeeId()">🗑️ Delete by Employee ID</button>
            </div>
        </div>

        <!-- Delete Face Features Only -->
        <div class="endpoint-section">
            <h2>🗑️ Delete Face Features Only (Keep Person)</h2>
            <div class="method">DELETE</div>
            <div class="url">http://localhost:8090/api/v1/persons/{person_id}/faces?confirm=true</div>
            
            <div class="example">
                <strong>Example:</strong><br>
                DELETE http://localhost:8090/api/v1/persons/1/faces?confirm=true
            </div>
            
            <div>
                <input type="number" id="deleteFacesId" placeholder="Enter Person ID" min="1">
                <button class="delete-button" onclick="deletePersonFaces()">🗑️ Delete Face Features Only</button>
            </div>
        </div>

        <!-- List Persons (for reference) -->
        <div class="endpoint-section">
            <h2>👥 List All Persons (for reference)</h2>
            <div class="method" style="background: #28a745;">GET</div>
            <div class="url">http://localhost:8090/api/v1/persons/</div>
            
            <div>
                <button class="test-button" onclick="listPersons()">📋 List All Persons</button>
            </div>
        </div>

        <!-- Results Area -->
        <div class="endpoint-section">
            <h2>📊 Results</h2>
            <div id="results">Click any button above to see results here...</div>
        </div>

        <div class="success">
            <strong>✅ Safety Features:</strong>
            <ul>
                <li>All deletions require <code>?confirm=true</code> parameter</li>
                <li>Returns detailed information about what was deleted</li>
                <li>Cascade deletion automatically removes associated face records</li>
                <li>Cache is automatically cleared after deletions</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8090/api/v1/persons';
        
        function updateResults(data) {
            document.getElementById('results').textContent = JSON.stringify(data, null, 2);
        }
        
        function showError(error) {
            document.getElementById('results').textContent = `❌ Error: ${error}`;
        }
        
        async function deletePersonById() {
            const personId = document.getElementById('deleteId').value;
            if (!personId) {
                showError('Please enter a Person ID');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete person ID ${personId} and ALL associated data?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/${personId}?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function deletePersonByName() {
            const personName = document.getElementById('deleteName').value;
            if (!personName) {
                showError('Please enter a Person Name');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete person "${personName}" and ALL associated data?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/by-name/${encodeURIComponent(personName)}?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function deletePersonByEmployeeId() {
            const employeeId = document.getElementById('deleteEmployeeId').value;
            if (!employeeId) {
                showError('Please enter an Employee ID');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete person with employee ID "${employeeId}" and ALL associated data?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/by-employee-id/${encodeURIComponent(employeeId)}?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function deletePersonFaces() {
            const personId = document.getElementById('deleteFacesId').value;
            if (!personId) {
                showError('Please enter a Person ID');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete face features for person ID ${personId}? (Person record will be kept)`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/${personId}/faces?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function listPersons() {
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                updateResults(data);
            } catch (error) {
                showError(error.message);
            }
        }
    </script>
</body>
</html>
