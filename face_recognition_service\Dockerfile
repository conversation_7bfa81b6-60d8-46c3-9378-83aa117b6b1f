# 🎖️ FACE RECOGNITION MICROSERVICE DOCKERFILE
# 🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 3
# ⚔️ TACTICAL DOCKER INTEGRATION

FROM python:3.12-slim

# 🏷️ Set environment variables to match project pattern
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# 🏗️ Set working directory
WORKDIR /app

# 🔧 Install system dependencies (matching project pattern + PostgreSQL headers)
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    cmake \
    pkg-config \
    libopencv-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    wget \
    curl \
    libpq-dev \
    postgresql-client \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 📦 Copy requirements first to leverage Docker cache
COPY requirements.txt requirements.txt

# 🚀 Install Python dependencies with retry mechanism (FULL AI DEPENDENCIES)
RUN pip install --upgrade pip && \
    pip install --no-cache-dir --retries 3 --timeout 120 -r requirements.txt

# 📁 Copy application code
COPY . .

# 📁 Create necessary directories
RUN mkdir -p /app/uploads /app/faces /app/weights /app/logs /app/static /app/media

# 🔧 Set proper permissions (matching project pattern)
RUN chmod +x /app/entrypoint.sh /app/healthcheck.sh

# 🌐 Expose port 8090 for Face Recognition service
EXPOSE 8090

# 🩺 Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8090/health || exit 1

# 🚀 Run the application (matching project pattern)
ENTRYPOINT ["/app/entrypoint.sh"]
