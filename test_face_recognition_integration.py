#!/usr/bin/env python3
"""
🎖️ FACE RECOGNITION INTEGRATION TEST
Silent operation test for camera stream integration
"""

import requests
import time
import io
from PIL import Image
import numpy as np

# Service URLs
FACE_RECOGNITION_URL = "http://localhost:8090"
SHINOBI_CCTV_URL = "http://localhost:5000"

def test_face_recognition_service():
    """Test if face recognition service is operational"""
    print("🎯 Testing Face Recognition Service...")
    
    try:
        response = requests.get(f"{FACE_RECOGNITION_URL}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Face Recognition Service: {health_data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Face Recognition Service: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Face Recognition Service: {str(e)}")
        return False

def test_face_detection_api():
    """Test face detection API with synthetic image"""
    print("🎯 Testing Face Detection API...")
    
    try:
        # Create a simple test image
        test_image = Image.new('RGB', (640, 480), color='blue')
        image_bytes = io.BytesIO()
        test_image.save(image_bytes, format='JPEG')
        image_bytes.seek(0)
        
        files = {'image': ('test.jpg', image_bytes, 'image/jpeg')}

        response = requests.post(
            f"{FACE_RECOGNITION_URL}/recognize",
            files=files,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            is_match = result.get('recognition', {}).get('is_match', False)
            confidence = result.get('recognition', {}).get('confidence', 0)
            print(f"✅ Face Recognition API: Match={is_match}, Confidence={confidence:.2f}")
            return True
        else:
            print(f"❌ Face Detection API: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Face Detection API: {str(e)}")
        return False

def test_shinobi_frame_processing():
    """Test Shinobi frame processing endpoint"""
    print("🎯 Testing Shinobi Frame Processing...")
    
    try:
        # Create a test frame
        test_image = Image.new('RGB', (640, 480), color='green')
        image_bytes = io.BytesIO()
        test_image.save(image_bytes, format='JPEG')
        image_bytes.seek(0)
        
        # Test data
        data = {
            'camera_id': '1',
            'monitor_id': 'test_monitor'
        }
        files = {'image': ('test_frame.jpg', image_bytes, 'image/jpeg')}
        
        response = requests.post(
            f"{SHINOBI_CCTV_URL}/api/face-recognition/process-shinobi-frame/",
            data=data,
            files=files,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Shinobi Frame Processing: Success={result.get('success', False)}")
            print(f"   Faces Detected: {result.get('faces_detected', 0)}")
            print(f"   Faces Recognized: {result.get('faces_recognized', 0)}")
            return True
        elif response.status_code == 403:
            print("⚠️ Shinobi Frame Processing: Authentication required (expected)")
            return True  # This is expected without login
        else:
            print(f"❌ Shinobi Frame Processing: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Shinobi Frame Processing: {str(e)}")
        return False

def test_integration_endpoints():
    """Test all integration endpoints"""
    print("🎯 Testing Integration Endpoints...")
    
    endpoints = [
        f"{FACE_RECOGNITION_URL}/status",
        f"{FACE_RECOGNITION_URL}/persons",
        f"{SHINOBI_CCTV_URL}/cameras/",
    ]
    
    results = []
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=10)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"{status} {endpoint}: HTTP {response.status_code}")
            results.append(response.status_code == 200)
        except Exception as e:
            print(f"❌ {endpoint}: {str(e)}")
            results.append(False)
    
    return all(results)

def main():
    """Main test function"""
    print("🎖️ FACE RECOGNITION INTEGRATION TEST - SILENT OPERATION")
    print("=" * 60)
    
    # Test individual components
    face_service_ok = test_face_recognition_service()
    detection_api_ok = test_face_detection_api()
    frame_processing_ok = test_shinobi_frame_processing()
    endpoints_ok = test_integration_endpoints()
    
    # Summary
    print(f"\n🎖️ INTEGRATION TEST SUMMARY")
    print("=" * 40)
    print(f"Face Recognition Service: {'✅ OK' if face_service_ok else '❌ FAIL'}")
    print(f"Face Detection API: {'✅ OK' if detection_api_ok else '❌ FAIL'}")
    print(f"Frame Processing: {'✅ OK' if frame_processing_ok else '❌ FAIL'}")
    print(f"Integration Endpoints: {'✅ OK' if endpoints_ok else '❌ FAIL'}")
    
    if face_service_ok and detection_api_ok:
        print("\n🏆 INTEGRATION STATUS: READY FOR DEPLOYMENT")
        print("🎯 Next Steps:")
        print("   1. Login to Shinobi CCTV Django: http://localhost:5000/")
        print("   2. Navigate to Cameras page")
        print("   3. Click 'Start Recognition' on any camera")
        print("   4. Face recognition will process frames every 30 seconds")
        print("   5. Check notifications for recognition results")
    else:
        print("\n⚠️ INTEGRATION STATUS: NEEDS ATTENTION")
        print("🔧 Check service logs:")
        print("   docker-compose logs face-recognition")
        print("   docker-compose logs shinobi_cctv_django")
    
    return 0

if __name__ == "__main__":
    main()
