from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse, HttpResponse, FileResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from .models import VpnClient, VpnLog
from .forms import VpnClientForm
from .openvpn_manager import OpenVPNManager
from users.views import is_admin
import requests
from django.conf import settings
import json
import logging
import os
from datetime import datetime

logger = logging.getLogger(__name__)

@login_required
@user_passes_test(is_admin)
def client_list(request):
    """View for listing all VPN clients."""
    clients = VpnClient.objects.all()
    return render(request, 'vpn/client_list.html', {'clients': clients})

@login_required
@user_passes_test(is_admin)
def client_detail(request, client_id):
    """View for viewing a specific VPN client."""
    client = get_object_or_404(VpnClient, id=client_id)
    # Get recent logs for this client
    logs = VpnLog.objects.filter(client=client).order_by('-timestamp')[:20]
    
    return render(request, 'vpn/client_detail.html', {
        'client': client,
        'logs': logs
    })

@login_required
@user_passes_test(is_admin)
def client_create(request):
    """View for creating a new VPN client."""
    if request.method == 'POST':
        form = VpnClientForm(request.POST)
        if form.is_valid():
            client = form.save(commit=False)
            
            # Generate VPN client in OpenVPN
            success, message, config_data = create_openvpn_client(client.client_id)
            
            if success:
                # Save the client configuration file
                config_path = os.path.join('vpn_configs', f"{client.client_id}.ovpn")
                
                # Ensure the directory exists
                os.makedirs(os.path.join(settings.MEDIA_ROOT, 'vpn_configs'), exist_ok=True)
                
                # Save config to file
                with open(os.path.join(settings.MEDIA_ROOT, config_path), 'w') as f:
                    f.write(config_data)
                
                client.config_file = config_path
                client.save()
                
                # Log the event
                VpnLog.objects.create(
                    client=client,
                    event_type=VpnLog.EventType.INFO,
                    message=f"VPN client created: {client.name}"
                )
                
                messages.success(request, f'VPN client "{client.name}" created successfully!')
                return redirect('vpn:client_detail', client_id=client.id)
            else:
                messages.error(request, f'Error creating VPN client: {message}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = VpnClientForm()
    
    return render(request, 'vpn/client_form.html', {
        'form': form,
        'title': 'Create VPN Client'
    })

@login_required
@user_passes_test(is_admin)
def client_edit(request, client_id):
    """View for editing an existing VPN client."""
    client = get_object_or_404(VpnClient, id=client_id)
    
    if request.method == 'POST':
        form = VpnClientForm(request.POST, instance=client)
        if form.is_valid():
            client = form.save()
            
            # Log the event
            VpnLog.objects.create(
                client=client,
                event_type=VpnLog.EventType.INFO,
                message=f"VPN client updated: {client.name}"
            )
            
            messages.success(request, f'VPN client "{client.name}" updated successfully!')
            return redirect('vpn:client_detail', client_id=client.id)
    else:
        form = VpnClientForm(instance=client)
    
    return render(request, 'vpn/client_form.html', {
        'form': form,
        'client': client,
        'title': f'Edit VPN Client: {client.name}'
    })

@login_required
@user_passes_test(is_admin)
def client_delete(request, client_id):
    """View for deleting a VPN client."""
    client = get_object_or_404(VpnClient, id=client_id)
    
    if request.method == 'POST':
        client_name = client.name
        client_id = client.client_id
        
        # Revoke the client in OpenVPN
        success, message = revoke_openvpn_client(client_id)
        
        if success:
            # Delete the client
            client.delete()
            
            # Log the event (without client since it's deleted)
            VpnLog.objects.create(
                event_type=VpnLog.EventType.INFO,
                message=f"VPN client deleted: {client_name} ({client_id})"
            )
            
            messages.success(request, f'VPN client "{client_name}" deleted successfully!')
            return redirect('vpn:client_list')
        else:
            messages.error(request, f'Error revoking VPN client: {message}')
    
    return render(request, 'vpn/client_delete.html', {'client': client})

@login_required
@user_passes_test(is_admin)
def client_download_config(request, client_id):
    """View for downloading a client's VPN configuration file."""
    client = get_object_or_404(VpnClient, id=client_id)
    
    if not client.config_file:
        messages.error(request, 'No configuration file available for this client.')
        return redirect('vpn:client_detail', client_id=client.id)
    
    try:
        # Log the download
        VpnLog.objects.create(
            client=client,
            event_type=VpnLog.EventType.INFO,
            message=f"VPN configuration file downloaded for {client.name}",
            ip_address=request.META.get('REMOTE_ADDR')
        )
        
        # Serve the file
        response = FileResponse(open(os.path.join(settings.MEDIA_ROOT, client.config_file.name), 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{client.client_id}.ovpn"'
        return response
    except Exception as e:
        logger.error(f"Error serving VPN config file: {str(e)}")
        messages.error(request, 'Error serving configuration file.')
        return redirect('vpn:client_detail', client_id=client.id)

@login_required
@user_passes_test(is_admin)
def vpn_status(request):
    """View for showing the VPN server status."""
    try:
        status = get_openvpn_status()
        connected_clients = VpnClient.objects.filter(status=VpnClient.ClientStatus.ACTIVE)
        
        return render(request, 'vpn/vpn_status.html', {
            'status': status,
            'connected_clients': connected_clients
        })
    except Exception as e:
        logger.error(f"Error getting VPN status: {str(e)}")
        messages.error(request, 'Error retrieving VPN status information.')
        return redirect('dashboard:index')

@login_required
@user_passes_test(is_admin)
def vpn_logs(request):
    """View for showing VPN logs."""
    logs = VpnLog.objects.all().order_by('-timestamp')
    
    # Filter by client if specified
    client_id = request.GET.get('client')
    if client_id:
        logs = logs.filter(client_id=client_id)
    
    # Filter by event type if specified
    event_type = request.GET.get('type')
    if event_type:
        logs = logs.filter(event_type=event_type)
    
    return render(request, 'vpn/vpn_logs.html', {'logs': logs})

@login_required
@user_passes_test(is_admin)
def vpn_status_api(request):
    """API endpoint for getting VPN status (for AJAX updates)."""
    try:
        status = get_openvpn_status()
        connected_clients = list(VpnClient.objects.filter(
            status=VpnClient.ClientStatus.ACTIVE
        ).values('id', 'name', 'client_id', 'virtual_ip', 'connected_since'))
        
        return JsonResponse({
            'status': status,
            'connected_clients': connected_clients
        })
    except Exception as e:
        logger.error(f"Error getting VPN status for API: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
def client_status_update(request):
    """API endpoint for updating VPN client status."""
    if request.method != 'POST':
        return HttpResponse(status=405)
    
    try:
        data = json.loads(request.body)
        
        client_id = data.get('client_id')
        status = data.get('status')
        ip_address = data.get('ip_address')
        
        if not client_id or not status:
            return JsonResponse({'error': 'Missing client_id or status'}, status=400)
        
        try:
            client = VpnClient.objects.get(client_id=client_id)
        except VpnClient.DoesNotExist:
            return JsonResponse({'error': f'Client with ID {client_id} not found'}, status=404)
        
        # Update client status
        if status == 'connected':
            client.status = VpnClient.ClientStatus.ACTIVE
            client.real_ip = ip_address
            client.connected_since = datetime.now()
            
            # Log the connection event
            VpnLog.objects.create(
                client=client,
                event_type=VpnLog.EventType.CONNECT,
                message=f"Client connected from {ip_address}",
                ip_address=ip_address
            )
            
        elif status == 'disconnected':
            client.status = VpnClient.ClientStatus.INACTIVE
            
            # Log the disconnection event
            VpnLog.objects.create(
                client=client,
                event_type=VpnLog.EventType.DISCONNECT,
                message=f"Client disconnected",
                ip_address=ip_address
            )
        
        client.last_seen = datetime.now()
        client.save()
        
        return JsonResponse({'status': 'success'})
        
    except json.JSONDecodeError:
        return HttpResponse(status=400)
    except Exception as e:
        logger.error(f"Error updating VPN client status: {str(e)}")
        return HttpResponse(status=500)

# Helper functions for OpenVPN integration

def create_openvpn_client(client_id):
    """
    Create a new OpenVPN client using direct OpenVPN management.
    Returns (success, message, config_data)
    """
    try:
        manager = OpenVPNManager()

        # Create client certificate
        success, message = manager.create_client_certificate(client_id)
        if not success:
            return False, message, None

        # Generate client configuration
        success, message, config_data = manager.generate_client_config(client_id)

        if success:
            return True, "Client created successfully", config_data
        else:
            return False, message, None

    except Exception as e:
        logger.error(f"Error creating OpenVPN client: {str(e)}")
        return False, f"Error: {str(e)}", None


def revoke_openvpn_client(client_id):
    """
    Revoke an OpenVPN client using direct OpenVPN management.
    Returns (success, message)
    """
    try:
        manager = OpenVPNManager()
        success, message = manager.revoke_client_certificate(client_id)
        return success, message
    except Exception as e:
        logger.error(f"Error revoking OpenVPN client: {str(e)}")
        return False, f"Error: {str(e)}"


def get_openvpn_status():
    """
    Get the status of the OpenVPN server using direct management.
    Returns a dictionary with status information.
    """
    try:
        manager = OpenVPNManager()
        status = manager.get_server_status()

        # Get client list
        success, message, clients = manager.list_clients()
        if success:
            status['clients'] = clients
            status['client_count'] = len(clients)
        else:
            status['clients'] = []
            status['client_count'] = 0

        return status
    except Exception as e:
        logger.error(f"Error getting OpenVPN status: {str(e)}")
        raise