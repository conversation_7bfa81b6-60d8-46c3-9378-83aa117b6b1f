services:
  # Django web application
  web:
    build:
      context: ./django_web
      dockerfile: Dockerfile
    container_name: django_web
    command: >
      bash -c "python manage.py migrate &&
              python manage.py collectstatic --noinput &&
              gunicorn --bind 0.0.0.0:8000 cctv_project.wsgi:application" 
    restart: unless-stopped
    volumes:
      - ./django_web:/app
      - media_data:/app/media
      - static_data:/app/staticfiles
      - ./openvpn_data:/openvpn_data  # Mount OpenVPN data for VPN management
    environment:
      - DJANGO_SETTINGS_MODULE=cctv_project.settings
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
      - SHINOBI_API_URL=http://shinobi-nvr:8080
      - SHINOBI_API_KEY=vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx
      - SHINOBI_GROUP_KEY=VqJe1awj1m
      - OPENVPN_API_URL=http://openvpn-admin:8080
      - OPENVPN_USERNAME=admin
      - OPENVPN_PASSWORD=${OPENVPN_PASSWORD:-gaga<PERSON>ush}
      - DJ<PERSON>GO_SECRET_KEY=${DJANGO_SECRET_KEY:-django-insecure-l0p@+4)1_k73%9k!j4nw(i5yb-g5n=s4t+rx)zr8q5y_@!m2vf}
      - DJANGO_DEBUG=1
    env_file:
      - ./django_web/.env
    ports:
      - "8000:8000"
    depends_on:
      - db
      - shinobi-nvr
      - openvpn
    networks:
      - cctv_net            

  # Shinobi CCTV Django Admin
  shinobi_cctv_django:
    build:
      context: ./shinobi_cctv_django
      dockerfile: Dockerfile
    container_name: shinobi_cctv_django
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput --clear &&
             gunicorn --bind 0.0.0.0:8000 shinobi_cctv_django.wsgi"
    volumes:
      - static_data:/app/staticfiles
      - ./shinobi_cctv_django:/app
      - ./openvpn_data:/openvpn_data  # Mount OpenVPN data for VPN management
    env_file:
      - ./shinobi_cctv_django/.env
    depends_on:
      - db
      - redis
      - shinobi-nvr
    networks:
      - cctv_net
    ports:
      - "5000:8000"
    restart: unless-stopped
    
  # PostgreSQL Database for Django
  db:
    image: postgres:15
    container_name: postgres_db_django
    volumes:
      - postgres_data_django:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=warehouse_shinobi
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=admin
    networks:
      - cctv_net
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d warehouse_shinobi"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis for caching
  redis:
    image: redis:6.2
    container_name: redis_cache
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cctv_net
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # DuckDNS for dynamic DNS
  duckdns:
    image: linuxserver/duckdns:latest
    container_name: duckdns
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Istanbul
      - SUBDOMAINS=abinetalemuvpn
      - TOKEN=0085376b-1765-4ff7-924d-aa9d3c4f7a8a
      - LOG_FILE=false
    volumes:
      - duckdns_config:/config
    restart: unless-stopped
    networks:
      - cctv_net

  # OpenVPN Server (using kylemanna/openvpn)
  openvpn:
    image: kylemanna/openvpn
    container_name: openvpn_server
    cap_add:
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.forwarding=1
      - net.ipv6.conf.default.forwarding=1
    dns:
      - 127.0.0.11
    ports:
      - "1194:1194/udp" # OpenVPN server port
    volumes:
      - ./openvpn_data:/etc/openvpn # Mount for persistent OpenVPN data
      - ./scripts/init-openvpn.sh:/usr/local/bin/init-openvpn.sh:ro # Auto-init script
      - /dev/net/tun:/dev/net/tun # Required for TUN device
    environment:
      - TZ=Europe/Istanbul
      - EASYRSA_BATCH=1 # Enable batch mode for non-interactive setup
    command: >
      sh -c "
        if [ ! -f /etc/openvpn/.initialized ]; then
          echo '🔐 Initializing OpenVPN for first time...';
          /usr/local/bin/init-openvpn.sh;
        fi;
        echo '🚀 Starting OpenVPN server...';
        ovpn_run
      "
    networks:
      - cctv_net
    restart: unless-stopped

  # OpenVPN management is now handled directly by Django web service

  # Shinobi NVR
  shinobi-nvr:
    image: shinobisystems/shinobi
    container_name: shinobi-nvr
    ports:
      - "8080:8080"
    networks:
      - cctv_net
    environment:
      - HOME=/home/<USER>
      - DB_HOST=shinobi_db
      - DB_USER=shinobi
      - DB_PASSWORD=shinobi_password
      - DB_DATABASE=shinobi_db
      - TZ=Europe/Istanbul
      - PUID=1000
      - PGID=1000
      # Dual-mode network configuration
      - NETWORK_MODE=auto
      - ENABLE_DUAL_MODE=true
    volumes:
      - ./shinobi_config/conf.json:/home/<USER>/conf.json
      - ./shinobi_config/pm2.yml:/home/<USER>/pm2.yml
      - ./shinobi_config/super.json:/home/<USER>/super.json # Persists superuser credentials
      - ./shinobi_videos:/home/<USER>/videos # For video recordings
      - ./scripts/init-shinobi.sh:/usr/local/bin/init-shinobi.sh:ro # Auto-init script
      - /dev/shm/ShinobiRAM:/dev/shm/streams # As per official example
      - /etc/localtime:/etc/localtime:ro
      # Dual-mode network configuration files
      - ./network_config_manager.py:/app/network_config_manager.py:ro
      - ./dynamic_shinobi_config.py:/app/dynamic_shinobi_config.py:ro
    command: >
      sh -c "
        echo '📹 Waiting for database...';
        sleep 15;
        echo '📹 Initializing Shinobi database...';
        /usr/local/bin/init-shinobi.sh;
        echo '🚀 Starting Shinobi NVR...';
        pm2-docker /home/<USER>/pm2.yml
      "
    # If you need to access Shinobi UI, you'd typically do it via its IP on cctv_net if it also joined that,
    # or consider how to expose it if it's ONLY on the VPN network stack.
    # For now, assuming access via VPN or other means if needed directly.
    # We can expose Shinobi's port (8080) on the openvpn_server service if direct access is needed from host.
    depends_on:
      - shinobi_db # Shinobi still depends on its DB
      # - openvpn # Explicit dependency though network_mode implies it (referencing the 'openvpn' service name)
    restart: unless-stopped

  # MariaDB for Shinobi
  shinobi_db:
    image: mariadb:10.5 # Using 10.5 for consistency
    container_name: shinobi_mariadb_for_shinobi # Clearer name
    environment:
      - MYSQL_ROOT_PASSWORD=a_very_secret_root_password_for_shinobi_db # Set a proper root password
      - MYSQL_DATABASE=shinobi_db
      - MYSQL_USER=shinobi
      - MYSQL_PASSWORD=shinobi_password # Ensure this matches shinobi-nvr's env
    volumes:
      - shinobi_mariadb_data_for_shinobi:/var/lib/mysql # New volume name
    command: mysqld
    networks:
      - cctv_net
    restart: unless-stopped

  # pgAdmin for PostgreSQL management
  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin_service_django
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data_django:/var/lib/pgadmin
    networks:
      - cctv_net
    restart: unless-stopped

networks:
  cctv_net:
    # Docker Compose will now manage this network (e.g., named 'deepseekdjango_cctv_net' by default)
    driver: bridge # Explicitly state bridge, though it's default

volumes:
  # Django App related
  media_data:
  static_data:
  logs_volume:
  postgres_data_django:
  redis_data:
  pgadmin_data_django:
  
  # Shinobi NVR related
  shinobi_mariadb_data_for_shinobi: # For Shinobi's MariaDB
  shinobi_config: # Volume for shinobi config
  shinobi_customAutoLoad: # Volume for shinobi custom auto load
  
  # DuckDNS
  duckdns_config:

  # OpenVPN
  openvpn_data: # Volume for openvpn persistent data

  # Shinobi CCTV Django Admin
  staticfiles_admin:
