"""
Django settings for shinobi_cctv_django project.

Generated by 'django-admin startproject' using Django 5.1.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

"""
Django settings for shinobi_cctv_django project.

Generated by 'django-admin startproject' using Django 4.2.
(Assuming Django 4.2+, adjust if using a different version)
"""

import os
from pathlib import Path
from datetime import timedelta # Keep if used elsewhere, not directly for these settings
from dotenv import load_dotenv
from django.contrib.messages import constants as messages_constants
# Load environment variables from .env file
load_dotenv()  # Load from .env

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Ensure SECRET_KEY is loaded from environment
SECRET_KEY = os.getenv('SECRET_KEY', 'my_development_secret_key')

# Shinobi Settings
SHINOBI_API_KEY = os.getenv("SHINOBI_API_KEY")
SHINOBI_GROUP_KEY = os.getenv("SHINOBI_GROUP_KEY")
SHINOBI_HOST = os.getenv("SHINOBI_HOST", "http://shinobi-nvr:8080")  # For server-to-server API calls
SHINOBI_API_BASE = os.getenv("SHINOBI_HOST", "http://shinobi-nvr:8080")  # Corrected default for server-to-server
SHINOBI_CLIENT_URL = os.getenv("SHINOBI_CLIENT_URL", "http://localhost:8080") # For browser access

DEBUG = os.getenv("DEBUG", "True").lower() == "true"

# Allowed hosts from .env (comma or space separated)
ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost 127.0.0.1 0.0.0.0').replace(',', ' ').split()

# Only enable these security settings in production
if not DEBUG:
    SECURE_HSTS_SECONDS = 3600
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
else:
    SECURE_SSL_REDIRECT = False
    SESSION_COOKIE_SECURE = False
    CSRF_COOKIE_SECURE = False

# Optional: Validate they are loaded correctly
if not all([SHINOBI_API_KEY, SHINOBI_GROUP_KEY]):
    raise ValueError("Missing SHINOBI API credentials in environment.")

FIELD_ENCRYPTION_KEY = os.environ.get("FIELD_ENCRYPTION_KEY")

SITE_ID = 1

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'dashboard.apps.DashboardConfig', # Your app
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'shinobi_cctv_django.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'], # Optional: Project-level templates
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'shinobi_cctv_django.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv("DB_NAME", "shinobi_cctv_db"),
        'USER': os.getenv("DB_USER", "user"),
        'PASSWORD': os.getenv("DB_PASSWORD", "admin"),
        'HOST': os.getenv("DB_HOST", "db"),
        'PORT': os.getenv("DB_PORT", "5432"),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

# Time zone from .env or default
TIME_ZONE = os.getenv('TIME_ZONE', 'UTC')

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.getenv('STATIC_ROOT', '/app/staticfiles')
MEDIA_URL = '/media/'
MEDIA_ROOT = os.getenv('MEDIA_ROOT', '/app/media')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom User Model
AUTH_USER_MODEL = 'dashboard.CustomUser'

# Session Configuration - Separate from django_web
SESSION_COOKIE_NAME = 'shinobi_cctv_sessionid'
SESSION_COOKIE_AGE = 1209600  # 2 weeks
SESSION_SAVE_EVERY_REQUEST = False
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
CSRF_COOKIE_NAME = 'shinobi_cctv_csrftoken'

# Login and Logout Redirect URLs
LOGIN_URL = 'dashboard:login'
LOGIN_REDIRECT_URL = 'dashboard:dashboard'
LOGOUT_REDIRECT_URL = 'dashboard:index'


# Legacy Shinobi URL setting for backward compatibility
SHINOBI_URL = SHINOBI_CLIENT_URL

# Message Tags for Bootstrap Alert styling
# This maps Django's message levels to Bootstrap alert classes
MESSAGE_TAGS = {
    messages_constants.DEBUG: 'secondary',
    messages_constants.INFO: 'info',
    messages_constants.SUCCESS: 'success',
    messages_constants.WARNING: 'warning',
    messages_constants.ERROR: 'danger',
}
