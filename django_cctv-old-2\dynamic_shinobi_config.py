#!/usr/bin/env python3
"""
Dynamic Shinobi Configuration for Dual-Mode Network Support
Automatically configures Shinobi based on network environment
"""
import json
import os
import subprocess
from typing import Dict, List
from network_config_manager import NetworkConfigManager

class DynamicShinobiConfig:
    """Manages dynamic Shinobi configuration for dual-mode networks"""
    
    def __init__(self):
        self.network_manager = NetworkConfigManager()
        self.shinobi_config_path = "/opt/shinobi/conf.json"
        self.django_settings_path = "/app/shinobi_cctv_django/settings.py"
        
    def get_shinobi_config_template(self) -> Dict:
        """Get base Shinobi configuration template"""
        return {
            "port": 8080,
            "addStorage": [
                {
                    "name": "second",
                    "path": "/opt/shinobi/videos"
                }
            ],
            "db": {
                "host": "shinobi_mariadb_for_shinobi",
                "user": "majesticflame",
                "password": "password",
                "database": "ccio",
                "port": 3306
            },
            "mail": {
                "service": "gmail",
                "auth": {
                    "user": "",
                    "pass": ""
                }
            },
            "cron": {
                "key": "fd6c7849-904d-47ea-922b-5143358ba0de"
            },
            "pluginKeys": {
                "Motion": "b7502fd9-506c-4dda-9aa5-b5414ccb575a",
                "OpenCV": "f078bcfe-c39a-4eb5-bd52-9382ca828e8a",
                "FaceDetection": "f9ff758e-beef-4cd7-bd4c-b1c40d5fe0f7"
            },
            "ssl": {
                "key": "",
                "cert": "",
                "ca": ""
            },
            "bindip": "0.0.0.0"  # Dynamic based on network mode
        }
    
    def update_shinobi_config_for_network(self, network_config: Dict):
        """Update Shinobi configuration based on network mode"""
        shinobi_config = self.get_shinobi_config_template()
        
        mode = network_config.get('mode', 'standalone')
        interfaces = network_config.get('interfaces', {})
        
        if mode == 'organization_vpn':
            # Bind to VPN interface if available
            vpn_ip = self._get_vpn_ip(interfaces)
            if vpn_ip:
                shinobi_config['bindip'] = vpn_ip
                print(f"🌐 Configured Shinobi for VPN mode: {vpn_ip}")
        elif mode == 'hybrid':
            # Bind to all interfaces for hybrid mode
            shinobi_config['bindip'] = "0.0.0.0"
            print("🌐 Configured Shinobi for hybrid mode: all interfaces")
        else:
            # Standalone mode - default configuration
            shinobi_config['bindip'] = "0.0.0.0"
            print("🌐 Configured Shinobi for standalone mode")
            
        return shinobi_config
    
    def _get_vpn_ip(self, interfaces: Dict[str, str]) -> str:
        """Get VPN interface IP"""
        for interface, ip in interfaces.items():
            if (interface.startswith(('tun', 'tap', 'vpn')) or
                self._is_vpn_ip(ip)):
                return ip
        return None
    
    def _is_vpn_ip(self, ip: str) -> bool:
        """Check if IP is likely a VPN IP"""
        import ipaddress
        try:
            ip_obj = ipaddress.ip_address(ip)
            return ip_obj in ipaddress.ip_network('**********/12')
        except ValueError:
            return False
    
    def update_django_settings(self, network_config: Dict):
        """Update Django settings based on network configuration"""
        urls = network_config.get('shinobi_urls', {})
        mode = network_config.get('mode', 'standalone')
        
        # Create dynamic settings content
        settings_addition = f"""
# Dynamic Network Configuration
NETWORK_MODE = '{mode}'
SHINOBI_BACKEND_URL = '{urls.get("backend_url", "http://shinobi-nvr:8080")}'
SHINOBI_FRONTEND_URL = '{urls.get("frontend_url", "http://localhost:8080")}'
SHINOBI_API_URL = '{urls.get("api_url", "http://shinobi-nvr:8080")}'

# VPN-specific settings
if '{mode}' in ['organization_vpn', 'hybrid']:
    SHINOBI_VPN_URL = '{urls.get("vpn_frontend_url", "")}'
    CAMERA_DISCOVERY_RANGES = {network_config.get('camera_ranges', [])}
else:
    SHINOBI_VPN_URL = None
    CAMERA_DISCOVERY_RANGES = ['***********/24']

# Dynamic URL selection based on network mode
def get_shinobi_client_url():
    if NETWORK_MODE == 'organization_vpn' and SHINOBI_VPN_URL:
        return SHINOBI_VPN_URL
    return SHINOBI_FRONTEND_URL

SHINOBI_CLIENT_URL = get_shinobi_client_url()
"""
        
        return settings_addition
    
    def apply_configuration(self):
        """Apply dynamic configuration based on current network state"""
        print("🔧 Applying dynamic network configuration...")
        
        # Update network configuration
        network_config = self.network_manager.update_config()
        
        # Update Shinobi configuration
        shinobi_config = self.update_shinobi_config_for_network(network_config)
        
        # Update Django settings
        django_settings = self.update_django_settings(network_config)
        
        print(f"✅ Configuration applied for mode: {network_config['mode']}")
        
        return {
            'network_config': network_config,
            'shinobi_config': shinobi_config,
            'django_settings': django_settings
        }
    
    def create_network_mode_script(self):
        """Create script to switch between network modes"""
        script_content = '''#!/bin/bash
# Network Mode Switcher for CCTV System

case "$1" in
    "standalone")
        echo "🏠 Switching to Standalone Mode..."
        # Disconnect from organization VPN if connected
        # Restart services with local network configuration
        python3 /app/dynamic_shinobi_config.py --mode standalone
        docker-compose restart shinobi-nvr django_web shinobi_cctv_django
        ;;
    "organization")
        echo "🏢 Switching to Organization VPN Mode..."
        # Connect to organization VPN
        # Update configuration for VPN network
        python3 /app/dynamic_shinobi_config.py --mode organization_vpn
        docker-compose restart shinobi-nvr django_web shinobi_cctv_django
        ;;
    "hybrid")
        echo "🔄 Switching to Hybrid Mode..."
        # Enable both networks
        python3 /app/dynamic_shinobi_config.py --mode hybrid
        docker-compose restart shinobi-nvr django_web shinobi_cctv_django
        ;;
    "detect")
        echo "🔍 Auto-detecting network mode..."
        python3 /app/dynamic_shinobi_config.py --mode auto
        ;;
    *)
        echo "Usage: $0 {standalone|organization|hybrid|detect}"
        echo ""
        echo "🏠 standalone    - Use only local network"
        echo "🏢 organization  - Use only organization VPN"
        echo "🔄 hybrid        - Use both networks"
        echo "🔍 detect        - Auto-detect and configure"
        exit 1
        ;;
esac
'''
        
        with open('/app/switch_network_mode.sh', 'w') as f:
            f.write(script_content)
        
        # Make script executable
        os.chmod('/app/switch_network_mode.sh', 0o755)
        
        print("✅ Network mode switcher script created")

def main():
    """Main function for testing and CLI usage"""
    import sys
    
    config_manager = DynamicShinobiConfig()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--mode':
        mode = sys.argv[2] if len(sys.argv) > 2 else 'auto'
        
        if mode == 'auto':
            # Auto-detect and apply
            result = config_manager.apply_configuration()
            print("🎯 Auto-configuration completed")
        else:
            # Force specific mode
            config_manager.network_manager.current_mode = mode
            result = config_manager.apply_configuration()
            print(f"🎯 Configuration applied for mode: {mode}")
    else:
        # Just show current configuration
        result = config_manager.apply_configuration()
        print("🔍 Current network configuration:")
        print(json.dumps(result['network_config'], indent=2))
    
    # Create network mode switcher script
    config_manager.create_network_mode_script()

if __name__ == '__main__':
    main()
