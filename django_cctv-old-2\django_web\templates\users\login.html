{% load static %}
<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - CCTV Monitoring System</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#EBF5FF',
                            100: '#E1EFFE',
                            200: '#C3DDFD',
                            300: '#A4CAFE',
                            400: '#76A9FA',
                            500: '#3F83F8',
                            600: '#1C64F2',
                            700: '#1A56DB',
                            800: '#1E429F',
                            900: '#233876',
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .bg-pattern {
            background-color: #1a1a2e;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22H0v-1.17zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.24H0V3.07zM17.76 40l2.83-2.83 1.41 1.41L19.17 40h-1.41zM17.76 22.24l2.83-2.83 1.41 1.41L19.17 23.66h-1.41v-1.42zM17.76 4.48l2.83-2.83 1.41 1.41-2.83 2.83h-1.41V4.48zM35.52 40l2.83-2.83 1.41 1.41L37.93 40h-2.41zm0-17.76l2.83-2.83 1.41 1.41-2.83 2.83h-1.41v-1.41zm0-17.76l2.83-2.83 1.41 1.41-2.83 2.83h-1.41V4.48zM20 18.14l2.83-2.83 1.41 1.41L21.41 20H20v-1.86zM18.28 20L15.4 17.17l1.41-1.41L20 18.83v1.42l-1.72-1.25zM20 35.9l2.83-2.83 1.41 1.41L21.41 37.66H20v-1.76zm0-17.76l2.83-2.83 1.41 1.41L21.41 19.9H20v-1.76zM37.52 18.83l2.83-2.83 1.41 1.41-2.83 2.83H37.1l.42-1.41zM37.52 1.07l2.83-2.83 1.41 1.41-2.83 2.83H37.1l.42-1.41z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="h-full bg-pattern">
    <div class="min-h-full flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 space-y-8 fade-in">
            <div class="text-center">
                <div class="mx-auto h-14 w-14 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600 dark:text-blue-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="1"></circle>
                        <circle cx="12" cy="12" r="5"></circle>
                        <circle cx="12" cy="12" r="9"></circle>
                    </svg>
                </div>
                <h2 class="mt-6 text-3xl font-bold text-gray-900 dark:text-white">SecureView</h2>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">CCTV Monitoring System</p>
            </div>
            
            {% if form.errors %}
            <div class="bg-red-50 dark:bg-red-900 p-4 rounded-md mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Invalid credentials</h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            <p>Please check your username and password and try again.</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <form class="mt-8 space-y-6" method="post">
                {% csrf_token %}
                <input type="hidden" name="next" value="{{ next }}">
                
                <div class="rounded-md -space-y-px">
                    <div class="mb-4">
                        <label for="id_username" class="sr-only">Username</label>
                        {{ form.username }}
                    </div>
                    <div>
                        <label for="id_password" class="sr-only">Password</label>
                        {{ form.password }}
                    </div>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">Remember me</label>
                    </div>
                    
                    <div class="text-sm">
                        <a href="#" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">Forgot your password?</a>
                    </div>
                </div>
                
                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        Sign in
                    </button>
                </div>
            </form>
            
            <div class="text-center text-sm text-gray-500 dark:text-gray-400 mt-8">
                <p>© {% now "Y" %} SecureView. All rights reserved.</p>
            </div>
        </div>
    </div>

    <script>
        // Check for saved dark mode preference and apply it
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.documentElement.classList.add('dark');
        }
    </script>
</body>
</html>