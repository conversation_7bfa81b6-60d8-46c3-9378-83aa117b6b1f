// Main JavaScript file for CCTV Monitoring Web Application

document.addEventListener('DOMContentLoaded', function() {
  // Initialize components
  initializeSidebar();
  initializeNotifications();
  initializeGridView();
  initializeSystemStatus();
  initializeDarkMode();
  
  // Add smooth scrolling
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      
      document.querySelector(this.getAttribute('href')).scrollIntoView({
        behavior: 'smooth'
      });
    });
  });
});

// Sidebar functionality
function initializeSidebar() {
  const sidebarToggle = document.getElementById('sidebar-toggle');
  const sidebar = document.getElementById('sidebar');
  
  if (sidebarToggle && sidebar) {
    sidebarToggle.addEventListener('click', function() {
      sidebar.classList.toggle('translate-x-0');
      sidebar.classList.toggle('-translate-x-full');
    });
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
      const isSmallScreen = window.innerWidth < 1024;
      const clickedOutsideSidebar = !sidebar.contains(event.target) && event.target !== sidebarToggle;
      
      if (isSmallScreen && clickedOutsideSidebar && sidebar.classList.contains('translate-x-0')) {
        sidebar.classList.remove('translate-x-0');
        sidebar.classList.add('-translate-x-full');
      }
    });
  }
}

// Notifications system
function initializeNotifications() {
  const notificationsBtn = document.getElementById('notifications-btn');
  const notificationsPanel = document.getElementById('notifications-panel');
  
  if (notificationsBtn && notificationsPanel) {
    notificationsBtn.addEventListener('click', function() {
      notificationsPanel.classList.toggle('hidden');
    });
    
    // Close notifications when clicking outside
    document.addEventListener('click', function(event) {
      if (!notificationsPanel.contains(event.target) && event.target !== notificationsBtn && !notificationsPanel.classList.contains('hidden')) {
        notificationsPanel.classList.add('hidden');
      }
    });
  }
}

// Grid view for cameras
function initializeGridView() {
  const layoutSelector = document.getElementById('grid-layout-selector');
  const cameraGrid = document.querySelector('.camera-grid');
  
  if (layoutSelector && cameraGrid) {
    layoutSelector.addEventListener('change', function() {
      // Remove all layout classes
      cameraGrid.classList.remove('layout-1x1', 'layout-2x2', 'layout-3x3', 'layout-4x4');
      
      // Add the selected layout class
      cameraGrid.classList.add(`layout-${this.value}`);
    });
  }
}

// System status updates
function initializeSystemStatus() {
  // Function to update system status via AJAX
  function updateSystemStatus() {
    fetch('/system-status/')
      .then(response => response.json())
      .then(data => {
        // Update status indicators
        document.getElementById('total-cameras').textContent = data.total_cameras;
        document.getElementById('online-cameras').textContent = data.online_cameras;
        document.getElementById('offline-cameras').textContent = data.offline_cameras;
        
        // Update service status indicators
        const vpnStatus = document.getElementById('vpn-status-indicator');
        if (vpnStatus) {
          vpnStatus.className = data.openvpn_status ? 
            'status-indicator status-online' : 
            'status-indicator status-offline';
        }
        
        const shinobiStatus = document.getElementById('shinobi-status-indicator');
        if (shinobiStatus) {
          shinobiStatus.className = data.shinobi_status ? 
            'status-indicator status-online' : 
            'status-indicator status-offline';
        }
      })
      .catch(error => console.error('Error updating system status:', error));
  }
  
  // Update status every 30 seconds
  if (document.getElementById('system-status-panel')) {
    updateSystemStatus(); // Initial update
    setInterval(updateSystemStatus, 30000); // Update every 30 seconds
  }
}

// Dark mode toggle
function initializeDarkMode() {
  const darkModeToggle = document.getElementById('dark-mode-toggle');
  
  if (darkModeToggle) {
    // Check for saved dark mode preference
    const darkModePreference = localStorage.getItem('darkMode') === 'enabled';
    
    // Apply saved preference
    if (darkModePreference) {
      document.documentElement.classList.add('dark');
      darkModeToggle.checked = true;
    }
    
    // Toggle dark mode on change
    darkModeToggle.addEventListener('change', function() {
      if (this.checked) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('darkMode', 'enabled');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('darkMode', 'disabled');
      }
    });
  }
}