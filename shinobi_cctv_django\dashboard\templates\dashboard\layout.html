{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ABC CCTV System Dashboard">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}ABC CCTV System{% endblock title %}</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">

    <link rel="stylesheet" href="{% static 'dashboard/css/custom.css' %}">

    {% block extra_css %}{% endblock extra_css %}

    <style>
        body {
            font-size: .875rem;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100; /* Behind the navbar */
            padding: 56px 0 0; /* Height of navbar */
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            overflow-y: auto; /* Enable vertical scroll */
            height: 100vh; /* Ensure sidebar takes full height */
        }
        @media (max-width: 767.98px) {
            .sidebar {
                top: 56px; /* Adjust for mobile navbar */
                padding-top: 0;
            }
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 56px); /* Adjust if navbar height changes */
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */
        }
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
            background-color: rgba(0, 0, 0, .25);
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
        }
        .navbar .form-control {
            padding: .75rem 1rem;
            border-width: 0;
            border-radius: 0;
        }
        .form-control-dark {
            color: #fff;
            background-color: rgba(255, 255, 255, .1);
            border-color: rgba(255, 255, 255, .1);
        }
        .form-control-dark:focus {
            border-color: transparent;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);
        }
    </style>
</head>
<body
    data-dashboard-stats-api-url="{% if request.user.is_authenticated %}{% url 'dashboard:api_dashboard_stats' %}{% endif %}"
    data-camera-status-api-base-url="{% if request.user.is_authenticated %}/api/camera/{% endif %}" {# JS will append camera_id/action #}
    class="bg-dark text-light"
>

    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3 fs-6" href="{% if request.user.is_authenticated %}{% url 'dashboard:dashboard' %}{% else %}{% url 'dashboard:index' %}{% endif %}">
            <i class="bi bi-camera-video-fill me-2"></i>EAGLE CCTV
        </a>
        {% if request.user.is_authenticated %}
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark w-100">
            <div class="container-fluid">
                <div class="collapse navbar-collapse show" id="navbarMain">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link {% if page_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard:dashboard' %}">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page_name == 'cameras' %}active{% endif %}" href="{% url 'dashboard:cameras' %}">
                                <i class="bi bi-camera-video"></i> Cameras
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if page_name == 'locations' %}active{% endif %}" href="{% url 'dashboard:locations' %}">
                                <i class="bi bi-buildings"></i> Locations
                            </a>
                        </li>
                        {% if request.user.is_superuser or request.user.role and request.user.role.name == "Administrator" %}
                        <li class="nav-item">
                            <a class="nav-link {% if page_name == 'users' %}active{% endif %}" href="{% url 'dashboard:users' %}">
                                <i class="bi bi-people"></i> Users
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link {% if page_name == 'system_health' %}active{% endif %}" href="{% url 'dashboard:system_health' %}">
                                <i class="bi bi-heart-pulse"></i> System Health
                            </a>
                        </li>
                        {% if request.user.is_superuser or request.user.role and request.user.role.name == "Administrator" %}
                        <li class="nav-item">
                            <a class="nav-link {% if page_name == 'settings' %}active{% endif %}" href="{% url 'dashboard:settings' %}">
                                <i class="bi bi-gear"></i> Settings
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
        <div class="navbar-nav"></div>
            <div class="nav-item dropdown text-nowrap">
                 <a class="nav-link dropdown-toggle px-3" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-1"></i> {{ request.user.username }}
                </a>
                <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
                    <li><a class="dropdown-item" href="{% url 'dashboard:settings' %}"><i class="bi bi-gear-fill me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{% url 'dashboard:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>Log out</a></li>
                </ul>
            </div>
        </div>
        {% else %}
         <div class="ms-auto"></div> {# Pushes login to the right if navbar-nav is not present #}
         <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="{% url 'dashboard:login' %}"><i class="bi bi-box-arrow-in-right me-2"></i>Log in</a>
            </div>
         </div>
        {% endif %}
    </header>

    <div class="container-fluid">
        <div class="row">
            {% if request.user.is_authenticated and page_name != 'login' and page_name != 'index' %}
                {% include "dashboard/partials/sidebar.html" %}
            {% endif %}

            <main class="{% if request.user.is_authenticated and page_name != 'login' and page_name != 'index' %}col-md-9 ms-sm-auto col-lg-10 px-md-4{% else %}col-12{% endif %}">
                {% if messages %}
                    <div class="container-fluid mt-3">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                    </div>
                {% endif %}

                {% block auth_content %}{% endblock auth_content %}
                {% block content %}{% endblock content %}
            </main>
        </div>
    </div>

    <div id="notifications-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1100;">
        </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    
    <script>
        window.CSRF_TOKEN = "{{ csrf_token }}";
    </script>

    <script src="{% static 'dashboard/js/utils.js' %}"></script>

    {% block scripts %}{% endblock scripts %}

</body>
</html>