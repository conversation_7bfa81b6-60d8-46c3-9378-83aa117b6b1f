{% load bootstrap4 i18n static %}

{% get_current_language as language_code %}

<!doctype html>
<html lang="{{ language_code }}">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">

    <title>Warehouse IP Camera Monitor</title>
    
</head>

<body>
<nav class="navbar navbar-expand-md navbar-dark bg-dark static-top">

    <a class="navbar-brand" href="{% url 'index' %}">{% translate 'Home' %}</a>

    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarsExampleDefault">
        <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse justify-content-end" id="navbarsExampleDefault">
        <ul class="navbar-nav mr-auto">
            {% if request.user.is_authenticated %}
                {% if request.user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:login' %}">{% translate 'Django administration' %}</a>
                    </li>
                {% endif %}
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:change_password' %}">{% translate 'Change password' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:change_profile' %}">{% translate 'Change profile' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:change_email' %}">{% translate 'Change email' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:log_out_confirm' %}">{% translate 'Log out' %}</a>
                </li>
                {% else %}
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:log_in' %}">{% translate 'Log in' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:sign_up' %}">{% translate 'Create an account' %}</a>
                </li>
                {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'change_language' %}">{% translate 'Change language' %}</a>
                    </li>
                </ul>
            </div>

</nav>
            
<div class="container-fluid mt-3">
    {% bootstrap_messages %}

    {% block content %}
        No content.
    {% endblock content %}

</div>
  
<script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>

</body>
</html>
