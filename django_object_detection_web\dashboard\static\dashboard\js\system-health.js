// dashboard/static/dashboard/js/system-health.js

let cpuGaugeChart, memoryGaugeChart, storageGaugeChart;

document.addEventListener('DOMContentLoaded', function() {
    initSystemHealthGauges();
    fetchAndUpdateSystemHealthStats(); // Initial load

    const refreshButton = document.getElementById('refresh-stats'); // ID from system-health.html
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            fetchAndUpdateSystemHealthStats();
            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            }, 1500);
        });
    }
    setInterval(fetchAndUpdateSystemHealthStats, 30000); // Poll every 30 seconds
});

function initSystemHealthGauges() {
    const cpuCanvas = document.getElementById('cpu-gauge');
    const memoryCanvas = document.getElementById('memory-gauge');
    const storageCanvas = document.getElementById('storage-gauge');

    if (typeof Chart === 'undefined') {
        console.error("Chart.js is not loaded. Gauges cannot be initialized.");
        if(cpuCanvas) cpuCanvas.parentElement.innerHTML = "<p class='text-warning small'>Chart library not loaded.</p>";
        if(memoryCanvas) memoryCanvas.parentElement.innerHTML = "<p class='text-warning small'>Chart library not loaded.</p>";
        if(storageCanvas) storageCanvas.parentElement.innerHTML = "<p class='text-warning small'>Chart library not loaded.</p>";
        return;
    }
    
    const gaugeOptions = {
        cutout: '70%',
        circumference: 180,
        rotation: -90,
        maintainAspectRatio: false,
        responsive: true,
        plugins: {
            legend: { display: false },
            tooltip: { enabled: false },
            centerText: { // Custom plugin to draw text in center
                // Will be defined globally or per chart instance
            }
        }
    };

    // Custom plugin for Chart.js to display text in the center of a doughnut chart
    Chart.register({
        id: 'centerText',
        beforeDraw: function(chart) {
            if (chart.config.type !== 'doughnut' || !chart.config.options.plugins.centerText.display) {
                return;
            }
            const usage = chart.data.datasets[0].data[0];
            const text = `${usage.toFixed(1)}%`;
            
            const { width, height, ctx } = chart;
            ctx.restore();
            const fontSize = (height / 114).toFixed(2); // Adjust font size based on canvas height
            ctx.font = `${fontSize}em Arial, sans-serif`;
            ctx.textBaseline = 'middle';
            ctx.fillStyle = getUsageColor(usage, true); // Get text color based on usage

            const textX = Math.round((width - ctx.measureText(text).width) / 2);
            const textY = height / 2 + (chart.options.circumference === 180 ? height / 5 : 0); // Adjust Y for half doughnut
            
            ctx.fillText(text, textX, textY);
            ctx.save();
        }
    });


    if (cpuCanvas) {
        const initialCpu = parseFloat(cpuCanvas.dataset.usage) || 0;
        cpuGaugeChart = new Chart(cpuCanvas, {
            type: 'doughnut',
            data: getGaugeData(initialCpu),
            options: { ...gaugeOptions, plugins: { ...gaugeOptions.plugins, centerText: { display: true } } }
        });
    }
    if (memoryCanvas) {
        const initialMemory = parseFloat(memoryCanvas.dataset.usage) || 0;
        memoryGaugeChart = new Chart(memoryCanvas, {
            type: 'doughnut',
            data: getGaugeData(initialMemory),
            options: { ...gaugeOptions, plugins: { ...gaugeOptions.plugins, centerText: { display: true } } }
        });
    }
    if (storageCanvas) {
        const initialStorage = parseFloat(storageCanvas.dataset.usage) || 0;
        storageGaugeChart = new Chart(storageCanvas, {
            type: 'doughnut',
            data: getGaugeData(initialStorage),
            options: { ...gaugeOptions, plugins: { ...gaugeOptions.plugins, centerText: { display: true } } }
        });
    }
}

function getGaugeData(value) {
    const usage = parseFloat(value) || 0;
    return {
        datasets: [{
            data: [usage, 100 - usage],
            backgroundColor: [getUsageColor(usage), '#495057'], // Darker background for unused part
            borderColor: [getUsageColor(usage), '#495057'],
            borderWidth: 0, // No border for a cleaner look
            borderRadius: usage > 0 ? 5 : 0, // Rounded edges for the used part
        }]
    };
}

function getUsageColor(usage, forText = false) {
    usage = parseFloat(usage);
    if (usage >= 85) return forText ? '#dc3545' : '#dc3545'; // Red
    if (usage >= 60) return forText ? '#ffc107' : '#ffc107'; // Yellow
    return forText ? '#198754' : '#198754'; // Green
}

function updateGaugeChart(chartInstance, value, textElementId) {
    if (chartInstance) {
        const usage = parseFloat(value) || 0;
        chartInstance.data.datasets[0].data = [usage, 100 - usage];
        chartInstance.data.datasets[0].backgroundColor = [getUsageColor(usage), '#495057'];
        chartInstance.data.datasets[0].borderColor = [getUsageColor(usage), '#495057'];
        chartInstance.update();
    }
    // Update separate text element if provided
    const textEl = document.getElementById(textElementId);
    if (textEl) {
        textEl.textContent = `${(parseFloat(value) || 0).toFixed(1)}%`;
        textEl.style.color = getUsageColor(value, true);
    }
}

function fetchAndUpdateSystemHealthStats() {
    const apiUrl = document.body.dataset.dashboardStatsApiUrl; // Assuming layout puts this on body
     if (!apiUrl) {
        console.error('System Health Stats API URL (dashboard:api_dashboard_stats) not found on body data attribute.');
        return;
    }

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) throw new Error(`Network response was not ok: ${response.statusText}`);
            return response.json();
        })
        .then(data => {
            updateGaugeChart(cpuGaugeChart, data.system.cpu, 'cpu-usage-text'); // Pass ID for separate text if canvas doesn't render it
            updateGaugeChart(memoryGaugeChart, data.system.memory, 'memory-usage-text');
            updateGaugeChart(storageGaugeChart, data.system.storage, 'storage-usage-text');
            
            const vpnConnectionsEl = document.getElementById('vpn-connections'); // From original system-health.html
            if (vpnConnectionsEl) vpnConnectionsEl.textContent = data.system.vpn_connections;
            
            const totalCamerasEl = document.getElementById('total-cameras');
            if (totalCamerasEl) totalCamerasEl.textContent = data.cameras.total;
            const onlineCamerasEl = document.getElementById('online-cameras');
            if (onlineCamerasEl) onlineCamerasEl.textContent = data.cameras.online;
            const offlineCamerasEl = document.getElementById('offline-cameras');
            if (offlineCamerasEl) offlineCamerasEl.textContent = data.cameras.offline;

            // Update camera progress bars
            const onlineCamPercentage = data.cameras.total > 0 ? (data.cameras.online / data.cameras.total * 100) : 0;
            const offlineCamPercentage = data.cameras.total > 0 ? (data.cameras.offline / data.cameras.total * 100) : 0;
            
            const onlineCamBar = document.querySelector('.progress-bar.bg-success[aria-valuenow]');
            if(onlineCamBar) {
                onlineCamBar.style.width = `${onlineCamPercentage}%`;
                onlineCamBar.setAttribute('aria-valuenow', data.cameras.online);
                onlineCamBar.textContent = data.cameras.online;
            }
            const offlineCamBar = document.querySelector('.progress-bar.bg-danger[aria-valuenow]');
            if(offlineCamBar) {
                offlineCamBar.style.width = `${offlineCamPercentage}%`;
                offlineCamBar.setAttribute('aria-valuenow', data.cameras.offline);
                offlineCamBar.textContent = data.cameras.offline;
            }
            const onlineCamText = document.getElementById('online-cameras-percentage-text');
            if(onlineCamText) onlineCamText.textContent = `${onlineCamPercentage.toFixed(1)}%`;


            const refreshTimestampEl = document.getElementById('refresh-timestamp');
            if (refreshTimestampEl) refreshTimestampEl.textContent = new Date().toLocaleTimeString();

            // Update location VPN statuses (placeholder, as there's no API endpoint for this specifically)
            // This should ideally come from an API if real-time status is needed per location
            // updateVpnStatusIndicators(data.locations); // If API provided this
        })
        .catch(error => {
            console.error('Error updating system health stats:', error);
            showNotification('Failed to update system statistics.', 'danger');
        });
}

// Placeholder for updating location VPN status if data.locations was available from API
// function updateVpnStatusIndicators(locationsData) {
//     locationsData.forEach(loc => {
//         const indicator = document.querySelector(`[data-vpn-status-loc="${loc.id}"]`);
//         // ... update based on loc.vpn_status ...
//     });
// }