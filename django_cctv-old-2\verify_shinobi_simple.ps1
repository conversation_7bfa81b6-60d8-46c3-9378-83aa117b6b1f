# Simple PowerShell script to verify Shinobi database setup
Write-Host "Verifying Shinobi Database Setup..." -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Test 1: Check MariaDB container
Write-Host "`n[INFO] Checking MariaDB container..." -ForegroundColor Blue
$mariadbStatus = docker ps --filter "name=shinobi_mariadb_for_shinobi" --format "{{.Status}}"
if ($mariadbStatus) {
    Write-Host "[SUCCESS] MariaDB container is running" -ForegroundColor Green
} else {
    Write-Host "[ERROR] MariaDB container is not running" -ForegroundColor Red
}

# Test 2: Check database tables
Write-Host "`n[INFO] Checking database tables..." -ForegroundColor Blue
$tables = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -D shinobi_db -e "SHOW TABLES;" 2>$null
if ($tables) {
    $tableList = $tables -split "`n" | Where-Object { $_ -and $_ -ne "Tables_in_shinobi_db" }
    Write-Host "[SUCCESS] Found $($tableList.Count) tables in shinobi_db" -ForegroundColor Green
    Write-Host "Tables: $($tableList -join ', ')" -ForegroundColor White
} else {
    Write-Host "[ERROR] Could not retrieve tables" -ForegroundColor Red
}

# Test 3: Check super admin user
Write-Host "`n[INFO] Checking super admin user..." -ForegroundColor Blue
$superUser = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -D shinobi_db -e "SELECT mail FROM Users WHERE mail = '<EMAIL>';" 2>$null
if ($superUser -match "<EMAIL>") {
    Write-Host "[SUCCESS] Super admin user exists" -ForegroundColor Green
} else {
    Write-Host "[WARNING] Super admin user not found" -ForegroundColor Yellow
}

# Test 4: Check Shinobi NVR container
Write-Host "`n[INFO] Checking Shinobi NVR container..." -ForegroundColor Blue
$shinobiStatus = docker ps --filter "name=shinobi-nvr" --format "{{.Status}}"
if ($shinobiStatus) {
    Write-Host "[SUCCESS] Shinobi NVR container is running" -ForegroundColor Green
} else {
    Write-Host "[WARNING] Shinobi NVR container is not running" -ForegroundColor Yellow
}

# Test 5: Test web interface
Write-Host "`n[INFO] Testing Shinobi web interface..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/super" -TimeoutSec 10 -UseBasicParsing
    Write-Host "[SUCCESS] Shinobi web interface is accessible (HTTP $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "[WARNING] Shinobi web interface is not accessible" -ForegroundColor Yellow
}

Write-Host "`n====================================" -ForegroundColor Cyan
Write-Host "Verification Complete!" -ForegroundColor Green
Write-Host "`nAccess Information:" -ForegroundColor Cyan
Write-Host "  URL: http://localhost:8080/super" -ForegroundColor White
Write-Host "  Login: <EMAIL>" -ForegroundColor White
Write-Host "  Password: sU5EjCH63wRMSo048y1tOdvm3B6xGk" -ForegroundColor White
