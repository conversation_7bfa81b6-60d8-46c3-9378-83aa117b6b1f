from django.contrib import admin
from .models import Camera, District, Branch

@admin.register(Camera)
class CameraAdmin(admin.ModelAdmin):
    list_display = ('name', 'branch', 'device_index', 'is_active')
    list_filter = ('branch', 'is_active')
    search_fields = ('name', 'device_index', 'branch__name')
    actions = ['make_active', 'make_inactive']
    ordering = ('name',)  # Order by name in ascending order

    def make_active(self, request, queryset):
        queryset.update(is_active=True)
    make_active.short_description = "Activate selected cameras"

    def make_inactive(self, request, queryset):
        queryset.update(is_active=False)
    make_inactive.short_description = "Deactivate selected cameras"

@admin.register(District)
class DistrictAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name',)
    actions = ['make_active', 'make_inactive']
    ordering = ('name',)  # Order districts by name in ascending order

    def make_active(self, request, queryset):
        queryset.update(is_active=True)
    make_active.short_description = "Mark selected districts as active"

    def make_inactive(self, request, queryset):
        queryset.update(is_active=False)
    make_inactive.short_description = "Mark selected districts as inactive"

@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):
    list_display = ('name', 'district', 'is_active', 'branch_access_id')
    list_filter = ('district', 'is_active', 'branch_access_id')
    search_fields = ('name', 'district__name')
    actions = ['make_active', 'make_inactive']
    ordering = ('name',)  # Order branches by name in ascending order

    def make_active(self, request, queryset):
        queryset.update(is_active=True)
    make_active.short_description = "Mark selected branches as active"

    def make_inactive(self, request, queryset):
        queryset.update(is_active=False)
    make_inactive.short_description = "Mark selected branches as inactive"