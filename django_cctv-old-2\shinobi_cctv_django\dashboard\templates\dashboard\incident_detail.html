{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}Incident Details - ABC CCTV System{% endblock %}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4">Incident Details</h2>
    <div class="card mb-4">
        <div class="card-body">
            <h4 class="card-title">{{ incident.title }}</h4>
            <p class="card-text text-muted">Reported by: {{ incident.reporter.get_full_name|default:incident.reporter.username }} on {{ incident.created_at|date:"Y-m-d H:i" }}</p>
            <p class="card-text"><strong>Status:</strong> <span class="badge bg-{% if incident.status == 'open' %}warning{% elif incident.status == 'closed' %}success{% else %}secondary{% endif %}">{{ incident.get_status_display }}</span></p>
            <p class="card-text"><strong>Location:</strong> {{ incident.location.name }}</p>
            <p class="card-text"><strong>Camera:</strong> {{ incident.camera.name }}</p>
            <p class="card-text"><strong>Description:</strong><br>{{ incident.description|linebreaksbr }}</p>
            {% if incident.attachment %}
            <p class="card-text"><strong>Attachment:</strong> <a href="{{ incident.attachment.url }}" target="_blank">View File</a></p>
            {% endif %}
        </div>
        <div class="card-footer d-flex justify-content-between align-items-center">
            <a href="{% url 'dashboard:incidents_list' %}" class="btn btn-secondary btn-sm">Back to Incidents</a>
            {% if can_edit %}
            <a href="{% url 'dashboard:incident_edit' incident_id=incident.id %}" class="btn btn-primary btn-sm">Edit Incident</a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
