# Python file
from django.core.management.base import BaseCommand
from dashboard.models import Role, CustomUser, Location, Camera, SystemHealth, Incident
from django.utils import timezone

class Command(BaseCommand):
    help = 'Seed the database with initial dummy data for development.'

    def handle(self, *args, **options):
        # Roles
        admin_role, _ = Role.objects.get_or_create(name='Administrator', defaults={'description': 'Full admin'})
        wh_manager, _ = Role.objects.get_or_create(name='Warehouse Manager', defaults={'description': 'Manages warehouse'})
        sec_operator, _ = Role.objects.get_or_create(name='Security Operator', defaults={'description': 'Operates security'})

        # Users
        admin, _ = CustomUser.objects.get_or_create(username='admin', defaults={
            'email': '<EMAIL>', 'role': admin_role, 'is_superuser': True, 'is_staff': True
        })
        admin.set_password('admin123')
        admin.save()
        user1, _ = CustomUser.objects.get_or_create(username='manager', defaults={
            'email': '<EMAIL>', 'role': wh_manager, 'is_superuser': False, 'is_staff': True
        })
        user1.set_password('manager123')
        user1.save()
        user2, _ = CustomUser.objects.get_or_create(username='operator', defaults={
            'email': '<EMAIL>', 'role': sec_operator, 'is_superuser': False, 'is_staff': True
        })
        user2.set_password('operator123')
        user2.save()

        # Locations
        loc1, _ = Location.objects.get_or_create(name='Warehouse A', defaults={
            'address': '123 Main St', 'city': 'Metropolis', 'state': 'State', 'country': 'Country', 'zipcode': '12345', 'vpn_status': True
        })
        loc2, _ = Location.objects.get_or_create(name='Warehouse B', defaults={
            'address': '456 Side St', 'city': 'Gotham', 'state': 'State', 'country': 'Country', 'zipcode': '67890', 'vpn_status': False
        })
        loc1.users_with_access.add(admin, user1)
        loc2.users_with_access.add(admin, user2)

        # Cameras
        cam1, _ = Camera.objects.get_or_create(name='Front Gate', defaults={
            'rtsp_url': 'rtsp://example.com/1', 'shinobi_id': 'cam1', 'status': 'online', 'location': loc1, 'is_ptz': True
        })
        cam2, _ = Camera.objects.get_or_create(name='Back Door', defaults={
            'rtsp_url': 'rtsp://example.com/2', 'shinobi_id': 'cam2', 'status': 'offline', 'location': loc2, 'is_ptz': False
        })

        # System Health
        SystemHealth.objects.create(cpu_usage=20.5, memory_usage=45.2, storage_usage=60.1, vpn_connections=1, online_cameras=1, offline_cameras=1, last_updated=timezone.now())

        # Incidents
        Incident.objects.get_or_create(title='Test Incident', defaults={
            'description': 'Test description', 'camera': cam1, 'location': loc1, 'reporter': admin, 'severity': 'medium', 'status': 'open', 'created_at': timezone.now()
        })

        self.stdout.write(self.style.SUCCESS('Dummy data seeded.'))
