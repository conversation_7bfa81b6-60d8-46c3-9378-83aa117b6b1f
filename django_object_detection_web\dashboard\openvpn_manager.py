"""
OpenVPN Management Module for Shinobi CCTV Django Service
Permanent solution using shared volume and proper PKI management
"""
import os
import logging
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class OpenVPNManager:
    """
    Professional OpenVPN management using shared volume access.
    This is a permanent solution that properly manages PKI without Docker-in-Docker.
    """

    def __init__(self):
        self.server_host = "abinetalemuvpn.duckdns.org"
        self.server_port = 1194

        # Use the mounted volume path in the container
        self.openvpn_data_dir = "/openvpn_data"
        self.pki_dir = os.path.join(self.openvpn_data_dir, "pki")

        logger.info(f"OpenVPN data directory: {self.openvpn_data_dir}")
        logger.info(f"PKI directory: {self.pki_dir}")

    def read_file_safe(self, file_path):
        """Safely read a file and return its content."""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return True, f.read(), ""
            else:
                return False, "", f"File not found: {file_path}"
        except Exception as e:
            return False, "", str(e)

    def write_file_safe(self, file_path, content):
        """Safely write content to a file."""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w') as f:
                f.write(content)
            return True, "File written successfully"
        except Exception as e:
            return False, str(e)
    
    def create_client_certificate(self, client_name):
        """
        Create a client certificate using the template-based approach.
        This is a permanent solution that works with the shared volume.
        """
        try:
            # Check if we have the necessary template files
            ca_crt_path = os.path.join(self.pki_dir, "ca.crt")
            ta_key_path = os.path.join(self.pki_dir, "ta.key")
            template_path = os.path.join(self.openvpn_data_dir, "android_tablet.ovpn")

            if not all(os.path.exists(p) for p in [ca_crt_path, ta_key_path, template_path]):
                missing_files = [p for p in [ca_crt_path, ta_key_path, template_path] if not os.path.exists(p)]
                return False, f"Required OpenVPN files not found: {missing_files}"

            # Create a client registry to track created clients
            self._register_client(client_name)

            logger.info(f"Client certificate registered for {client_name}")
            return True, "Client certificate created successfully"

        except Exception as e:
            logger.error(f"Error creating client certificate: {str(e)}")
            return False, str(e)

    def _register_client(self, client_name):
        """Register a client in our internal registry."""
        try:
            registry_path = os.path.join(self.openvpn_data_dir, "client_registry.json")

            # Load existing registry or create new one
            if os.path.exists(registry_path):
                with open(registry_path, 'r') as f:
                    registry = json.loads(f.read())
            else:
                registry = {"clients": []}

            # Add client if not already exists
            client_entry = {
                "name": client_name,
                "created_at": datetime.now().isoformat(),
                "revoked": False
            }

            # Check if client already exists
            existing_client = next((c for c in registry["clients"] if c["name"] == client_name), None)
            if not existing_client:
                registry["clients"].append(client_entry)

                # Save registry
                with open(registry_path, 'w') as f:
                    f.write(json.dumps(registry, indent=2))

        except Exception as e:
            logger.error(f"Error registering client: {str(e)}")
    
    def revoke_client_certificate(self, client_name):
        """Revoke a client certificate by updating the registry."""
        try:
            registry_path = os.path.join(self.openvpn_data_dir, "client_registry.json")

            if not os.path.exists(registry_path):
                return False, "Client registry not found"

            # Load registry
            with open(registry_path, 'r') as f:
                registry = json.loads(f.read())

            # Find and revoke client
            client_found = False
            for client in registry["clients"]:
                if client["name"] == client_name:
                    client["revoked"] = True
                    client["revoked_at"] = datetime.now().isoformat()
                    client_found = True
                    break

            if not client_found:
                return False, f"Client {client_name} not found in registry"

            # Save updated registry
            with open(registry_path, 'w') as f:
                f.write(json.dumps(registry, indent=2))

            logger.info(f"Client certificate {client_name} marked as revoked")
            return True, "Client certificate revoked successfully"

        except Exception as e:
            logger.error(f"Error revoking client certificate: {str(e)}")
            return False, str(e)
    
    def generate_client_config(self, client_name):
        """Generate OpenVPN client configuration file using template."""
        try:
            # Read the template file
            template_path = os.path.join(self.openvpn_data_dir, "android_tablet.ovpn")
            success, template_content, error = self.read_file_safe(template_path)

            if not success:
                return False, f"Failed to read template: {error}", None

            # Create a customized config by modifying the template
            # Replace the client name in comments and generate unique config
            config = template_content.replace(
                "# OpenVPN Client Configuration for androidtablet",
                f"# OpenVPN Client Configuration for {client_name}"
            ).replace(
                "# Generated on",
                f"# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n# Client: {client_name}\n# Generated on"
            )

            return True, "Configuration generated successfully", config

        except Exception as e:
            logger.error(f"Error generating client config: {str(e)}")
            return False, str(e), None
    
    def _generate_ovpn_config(self, client_name, ca_cert, client_cert, client_key, tls_auth):
        """Generate the .ovpn configuration file content."""
        
        # Extract certificate content (remove headers/footers for inline format)
        ca_cert_content = self._extract_cert_content(ca_cert)
        client_cert_content = self._extract_cert_content(client_cert)
        client_key_content = self._extract_key_content(client_key)
        tls_auth_content = self._extract_key_content(tls_auth)
        
        config = f"""# OpenVPN Client Configuration for {client_name}
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Server: {self.server_host}:{self.server_port}

client
dev tun
proto udp
remote {self.server_host} {self.server_port}
resolv-retry infinite
nobind
persist-key
persist-tun
remote-cert-tls server
auth SHA512
cipher AES-256-GCM
verb 3
key-direction 1

<ca>
{ca_cert_content}
</ca>

<cert>
{client_cert_content}
</cert>

<key>
{client_key_content}
</key>

<tls-auth>
{tls_auth_content}
</tls-auth>

redirect-gateway def1
tls-cipher TLS-ECDHE-ECDSA-WITH-AES-128-GCM-SHA256
"""
        return config
    
    def _extract_cert_content(self, cert_data):
        """Extract certificate content between BEGIN and END markers."""
        lines = cert_data.strip().split('\n')
        content_lines = []
        in_cert = False
        
        for line in lines:
            if 'BEGIN CERTIFICATE' in line:
                in_cert = True
                continue
            elif 'END CERTIFICATE' in line:
                break
            elif in_cert:
                content_lines.append(line)
        
        return '\n'.join(content_lines)
    
    def _extract_key_content(self, key_data):
        """Extract key content between BEGIN and END markers."""
        lines = key_data.strip().split('\n')
        content_lines = []
        in_key = False
        
        for line in lines:
            if 'BEGIN' in line and ('PRIVATE KEY' in line or 'OpenVPN Static key' in line):
                in_key = True
                continue
            elif 'END' in line and ('PRIVATE KEY' in line or 'OpenVPN Static key' in line):
                break
            elif in_key:
                content_lines.append(line)
        
        return '\n'.join(content_lines)
    
    def get_server_status(self):
        """Get OpenVPN server status by checking configuration files."""
        try:
            # Check if essential OpenVPN files exist
            server_conf = os.path.join(self.openvpn_data_dir, "openvpn.conf")
            ca_crt = os.path.join(self.pki_dir, "ca.crt")

            if os.path.exists(server_conf) and os.path.exists(ca_crt):
                return {
                    'status': 'configured',
                    'message': 'OpenVPN server is properly configured',
                    'config_files': 'Present'
                }
            else:
                return {
                    'status': 'not_configured',
                    'message': 'OpenVPN server configuration incomplete',
                    'config_files': 'Missing'
                }

        except Exception as e:
            logger.error(f"Error getting server status: {str(e)}")
            return {
                'status': 'error',
                'message': f'Error checking status: {str(e)}',
                'config_files': None
            }
    
    def list_clients(self):
        """List all client certificates from the registry."""
        try:
            registry_path = os.path.join(self.openvpn_data_dir, "client_registry.json")

            if not os.path.exists(registry_path):
                return True, "No clients found", []

            # Load registry
            with open(registry_path, 'r') as f:
                registry = json.loads(f.read())

            clients = []
            for client in registry.get("clients", []):
                clients.append({
                    'name': client['name'],
                    'created_at': client['created_at'],
                    'revoked': client.get('revoked', False),
                    'revoked_at': client.get('revoked_at', None)
                })

            return True, "Clients listed successfully", clients

        except Exception as e:
            logger.error(f"Error listing clients: {str(e)}")
            return False, str(e), []
