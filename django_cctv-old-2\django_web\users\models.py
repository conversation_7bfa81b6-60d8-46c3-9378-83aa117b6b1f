from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _

class User(AbstractUser):
    """Custom user model for enhanced user management."""
    
    class UserRole(models.TextChoices):
        ADMIN = 'admin', _('Administrator')
        OPERATOR = 'operator', _('Operator')
        VIEWER = 'viewer', _('Viewer')
    
    email = models.EmailField(_('Email address'), unique=True)
    role = models.CharField(
        max_length=20,
        choices=UserRole.choices,
        default=UserRole.VIEWER,
    )
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    profile_image = models.ImageField(upload_to='profile_images/', blank=True, null=True)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)
    
    # Explicitly adding first_name and last_name fields
    first_name = models.CharField(_('first name'), max_length=150, blank=True)
    last_name = models.Char<PERSON>ield(_('last name'), max_length=150, blank=True)
    
    # Track camera access permissions
    camera_groups = models.ManyToManyField(
        'cameras.CameraGroup', 
        related_name='users', 
        blank=True
    )
    
    # For audit purposes
    date_modified = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
    
    def has_camera_access(self, camera):
        """Check if user has access to a specific camera."""
        if self.is_superuser or self.role == self.UserRole.ADMIN:
            return True
        return self.camera_groups.filter(cameras=camera).exists()
    
    @property
    def is_admin(self):
        return self.role == self.UserRole.ADMIN or self.is_superuser
    
    @property
    def is_operator(self):
        return self.role == self.UserRole.OPERATOR