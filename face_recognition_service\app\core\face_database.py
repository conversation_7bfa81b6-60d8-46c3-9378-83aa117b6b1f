"""
🎖️ FACE DATABASE OPERATIONS
🎯 OPERATION: FACE RECOGNITION DATABASE MANAGEMENT
⚔️ TACTICAL DATABASE ACCESS AND MANIPULATION
"""

import logging
import numpy as np
import pickle
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.models.database_models import Person, FaceRecord, RecognitionLog
from app.core.database import get_database

logger = logging.getLogger(__name__)

class FaceDatabaseManager:
    """🎖️ Face Database Management System"""
    
    def __init__(self):
        self.logger = logger
    
    # 👤 PERSON MANAGEMENT
    
    async def create_person(self, name: str, employee_id: str = None, **kwargs) -> Person:
        """Create a new person in database"""
        async with get_database() as db:
            person = Person(
                name=name,
                employee_id=employee_id,
                department=kwargs.get('department'),
                role=kwargs.get('role'),
                email=kwargs.get('email'),
                phone=kwargs.get('phone'),
                status=kwargs.get('status', 'active')
            )
            db.add(person)
            await db.commit()
            await db.refresh(person)
            
            self.logger.info(f"✅ Created person: {name} (ID: {person.id})")
            return person
    
    async def get_person_by_id(self, person_id: int) -> Optional[Person]:
        """Get person by ID"""
        async with get_database() as db:
            return await db.get(Person, person_id)
    
    async def get_person_by_name(self, name: str) -> Optional[Person]:
        """Get person by name"""
        async with get_database() as db:
            result = await db.execute(
                select(Person).where(Person.name == name)
            )
            return result.scalar_one_or_none()
    
    async def get_person_by_employee_id(self, employee_id: str) -> Optional[Person]:
        """Get person by employee ID"""
        async with get_database() as db:
            result = await db.execute(
                select(Person).where(Person.employee_id == employee_id)
            )
            return result.scalar_one_or_none()
    
    async def list_all_persons(self) -> List[Person]:
        """List all persons"""
        async with get_database() as db:
            result = await db.execute(select(Person).order_by(Person.name))
            return result.scalars().all()
    
    # 🎭 FACE FEATURES MANAGEMENT
    
    async def save_face_features(self, person_id: int, features: np.ndarray, 
                                image_path: str = None, **metadata) -> FaceRecord:
        """
        💾 Save face features to database
        
        Args:
            person_id: Person ID
            features: 512-dimensional feature vector
            image_path: Path to source image
            metadata: Additional face data (bbox, confidence, etc.)
        """
        async with get_database() as db:
            # Serialize numpy array to binary
            feature_binary = pickle.dumps(features)
            
            face_record = FaceRecord(
                person_id=person_id,
                image_path=image_path or f"memory_extraction_{person_id}",
                feature_vector=feature_binary,
                feature_version="enhanced_arcface_v1",
                bounding_box=metadata.get('bbox'),
                detection_confidence=metadata.get('confidence'),
                quality_score=metadata.get('quality_score'),
                is_primary=metadata.get('is_primary', False)
            )
            
            db.add(face_record)
            await db.commit()
            await db.refresh(face_record)
            
            self.logger.info(f"✅ Saved face features for person {person_id} (Record ID: {face_record.id})")
            return face_record
    
    async def get_face_features(self, person_id: int) -> List[np.ndarray]:
        """
        📥 Get all face features for a person
        
        Returns:
            List of 512-dimensional feature vectors
        """
        async with get_database() as db:
            result = await db.execute(
                select(FaceRecord).where(
                    and_(FaceRecord.person_id == person_id, 
                         FaceRecord.status == 'active')
                )
            )
            face_records = result.scalars().all()
            
            features_list = []
            for record in face_records:
                if record.feature_vector:
                    # Deserialize binary to numpy array
                    features = pickle.loads(record.feature_vector)
                    features_list.append(features)
            
            self.logger.debug(f"📥 Retrieved {len(features_list)} feature vectors for person {person_id}")
            return features_list
    
    async def get_all_person_features(self) -> Dict[int, List[np.ndarray]]:
        """
        📥 Get all face features for all persons
        
        Returns:
            Dict mapping person_id to list of feature vectors
        """
        async with get_database() as db:
            result = await db.execute(
                select(FaceRecord).where(FaceRecord.status == 'active')
            )
            face_records = result.scalars().all()
            
            person_features = {}
            for record in face_records:
                if record.feature_vector:
                    features = pickle.loads(record.feature_vector)
                    if record.person_id not in person_features:
                        person_features[record.person_id] = []
                    person_features[record.person_id].append(features)
            
            self.logger.info(f"📥 Retrieved features for {len(person_features)} persons")
            return person_features
    
    # 🗑️ DELETION OPERATIONS
    
    async def delete_person_by_id(self, person_id: int) -> bool:
        """
        🗑️ Delete person and ALL associated data by ID
        
        Args:
            person_id: Person ID to delete
            
        Returns:
            True if deleted, False if not found
        """
        async with get_database() as db:
            person = await db.get(Person, person_id)
            if not person:
                self.logger.warning(f"❌ Person ID {person_id} not found")
                return False
            
            # Delete all face records (cascade will handle this automatically)
            await db.delete(person)
            await db.commit()
            
            self.logger.info(f"🗑️ Deleted person ID {person_id} and all associated data")
            return True
    
    async def delete_person_by_name(self, name: str) -> bool:
        """🗑️ Delete person and ALL associated data by name"""
        person = await self.get_person_by_name(name)
        if person:
            return await self.delete_person_by_id(person.id)
        return False
    
    async def delete_person_by_employee_id(self, employee_id: str) -> bool:
        """🗑️ Delete person and ALL associated data by employee ID"""
        person = await self.get_person_by_employee_id(employee_id)
        if person:
            return await self.delete_person_by_id(person.id)
        return False
    
    async def delete_face_features(self, person_id: int) -> int:
        """
        🗑️ Delete only face features for a person (keep person record)
        
        Returns:
            Number of face records deleted
        """
        async with get_database() as db:
            result = await db.execute(
                select(FaceRecord).where(FaceRecord.person_id == person_id)
            )
            face_records = result.scalars().all()
            
            count = len(face_records)
            for record in face_records:
                await db.delete(record)
            
            await db.commit()
            
            self.logger.info(f"🗑️ Deleted {count} face records for person {person_id}")
            return count
    
    # 📊 ANALYTICS AND SEARCH
    
    async def search_similar_faces(self, query_features: np.ndarray, 
                                  threshold: float = 0.5) -> List[Tuple[int, float]]:
        """
        🔍 Search for similar faces using cosine similarity
        
        Args:
            query_features: Query feature vector
            threshold: Similarity threshold
            
        Returns:
            List of (person_id, similarity_score) tuples
        """
        all_features = await self.get_all_person_features()
        matches = []
        
        for person_id, feature_list in all_features.items():
            for features in feature_list:
                # Compute cosine similarity
                similarity = np.dot(query_features, features) / (
                    np.linalg.norm(query_features) * np.linalg.norm(features)
                )
                
                if similarity >= threshold:
                    matches.append((person_id, float(similarity)))
        
        # Sort by similarity (highest first)
        matches.sort(key=lambda x: x[1], reverse=True)
        
        self.logger.debug(f"🔍 Found {len(matches)} similar faces above threshold {threshold}")
        return matches
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """📊 Get database statistics"""
        async with get_database() as db:
            # Count persons
            person_count = await db.scalar(select(func.count(Person.id)))
            
            # Count face records
            face_count = await db.scalar(select(func.count(FaceRecord.id)))
            
            # Count recognition logs
            log_count = await db.scalar(select(func.count(RecognitionLog.id)))
            
            return {
                "total_persons": person_count,
                "total_face_records": face_count,
                "total_recognition_logs": log_count,
                "avg_faces_per_person": face_count / person_count if person_count > 0 else 0
            }

# 🎖️ Global database manager instance
face_db = FaceDatabaseManager()

logger.info("🎖️ Face Database Manager initialized")
logger.info("👤 Person management: ✅")
logger.info("🎭 Face features storage: ✅")
logger.info("🗑️ Indexed deletion: ✅")
logger.info("🔍 Similarity search: ✅")
