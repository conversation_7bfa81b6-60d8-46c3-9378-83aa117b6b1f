#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to add RTSP streams from the mock NVR to Shinobi.
This script uses the Shinobi API to configure monitors for the RTSP streams
coming from the mock NVR over the OpenVPN network.
"""

import requests
import json
import argparse
import time
import sys

def add_monitor(api_key, group_key, host, monitor_id, name, rtsp_host, rtsp_port, rtsp_path):
    """Add a new monitor to Shinobi."""
    url = f"{host}/{group_key}/monitor/{monitor_id}/add"
    
    # Monitor configuration data
    data = {
        "mode": "start",
        "mid": monitor_id,
        "name": name,
        "type": "h264",
        "protocol": "rtsp",
        "host": rtsp_host,
        "port": rtsp_port,
        "path": rtsp_path,
        "details": {
            "auto_host_enable": "1",
            "detector": "0",
            "detector_trigger": "1",
            "detector_save": "1",
            "detector_webhook": "0",
            "detector_command_enable": "0",
            "detector_command": "",
            "detector_command_timeout": "10",
            "rtmp_transport": "tcp"
        }
    }
    
    try:
        response = requests.post(
            url,
            data=json.dumps(data),
            params={"api": api_key},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print(f"Successfully added monitor: {name}")
            return True, response.json()
        else:
            print(f"Failed to add monitor: {response.text}")
            return False, response.text
    
    except Exception as e:
        print(f"Error adding monitor: {str(e)}")
        return False, str(e)

def get_monitors(api_key, group_key, host):
    """Get all monitors from Shinobi."""
    url = f"{host}/{group_key}/monitor"
    
    try:
        response = requests.get(
            url,
            params={"api": api_key}
        )
        
        if response.status_code == 200:
            print(f"Successfully fetched monitors")
            return True, response.json()
        else:
            print(f"Failed to fetch monitors: {response.text}")
            return False, response.text
    
    except Exception as e:
        print(f"Error fetching monitors: {str(e)}")
        return False, str(e)

def main():
    parser = argparse.ArgumentParser(description='Add RTSP streams to Shinobi.')
    parser.add_argument('--api-key', default='r9tsIBniLsICLu7eyYcbfEkpffimn2', help='Shinobi API key')
    parser.add_argument('--group-key', default='eiDZ8uZNeT', help='Shinobi Group key')
    parser.add_argument('--host', default='http://localhost:8080', help='Shinobi host URL')
    parser.add_argument('--nvr-ip', required=True, help='VPN IP of the mock NVR')
    parser.add_argument('--rtsp-port', default='8554', help='RTSP port')
    parser.add_argument('--list', action='store_true', help='List existing monitors')
    
    args = parser.parse_args()
    
    # If --list is specified, just list the monitors and exit
    if args.list:
        success, monitors = get_monitors(args.api_key, args.group_key, args.host)
        if success:
            print("\nExisting monitors:")
            for monitor in monitors:
                print(f"  - {monitor.get('name', 'Unknown')} (ID: {monitor.get('mid', 'Unknown')})")
        return
    
    # Define the streams to add
    streams = [
        {
            "id": "mock_nvr1_sample1",
            "name": "Mock NVR - Sample 1",
            "path": "/sample1"
        },
        {
            "id": "mock_nvr1_sample2",
            "name": "Mock NVR - Sample 2",
            "path": "/sample2"
        },
        {
            "id": "mock_nvr1_camera1",
            "name": "Mock NVR - Camera 1",
            "path": "/camera1"
        },
        {
            "id": "mock_nvr1_camera2",
            "name": "Mock NVR - Camera 2",
            "path": "/camera2"
        }
    ]
    
    # Add each stream
    success_count = 0
    for stream in streams:
        print(f"Adding stream: {stream['name']} (rtsp://{args.nvr_ip}:{args.rtsp_port}{stream['path']})")
        success, result = add_monitor(
            args.api_key,
            args.group_key,
            args.host,
            stream['id'],
            stream['name'],
            args.nvr_ip,
            args.rtsp_port,
            stream['path']
        )
        
        if success:
            success_count += 1
        
        # Slight delay to avoid overwhelming the API
        time.sleep(1)
    
    print(f"\nAdded {success_count} out of {len(streams)} streams.")
    
    # Get and display the list of monitors after adding
    success, monitors = get_monitors(args.api_key, args.group_key, args.host)
    if success:
        print("\nCurrent monitors:")
        for monitor in monitors:
            print(f"  - {monitor.get('name', 'Unknown')} (ID: {monitor.get('mid', 'Unknown')})")

if __name__ == "__main__":
    main() 