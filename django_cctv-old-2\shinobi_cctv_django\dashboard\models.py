from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.conf import settings

class Role(models.Model):
    name = models.CharField(max_length=50, unique=True, null=False)
    description = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.name

class CameraGroup(models.Model):
    """Group for organizing cameras and managing permissions."""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class CustomUser(AbstractUser):
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True, related_name='users')
    # last_login, created_at (date_joined) are already in AbstractUser
    # We can override date_joined if needed, or just use it.
    # For consistency with Flask model, let's add created_at if date_joined is not sufficient.
    # date_joined is auto-set on creation.

    # Track camera access permissions
    camera_groups = models.ManyToManyField(
        'CameraGroup',
        related_name='users',
        blank=True
    )

    def has_access_to_location(self, location_id):
        if self.is_superuser or (self.role and self.role.name == "Administrator"):
            return True
        return self.accessible_locations.filter(id=location_id).exists()

    def has_camera_access(self, camera):
        """Check if user has access to a specific camera."""
        if self.is_superuser or (self.role and self.role.name == "Administrator"):
            return True
        return self.camera_groups.filter(cameras=camera).exists()

    @property
    def is_admin(self):
        return (self.role and self.role.name == "Administrator") or self.is_superuser

    def __str__(self):
        return self.username

class Location(models.Model):
    name = models.CharField(max_length=100, null=False)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    zipcode = models.CharField(max_length=20, blank=True, null=True)
    vpn_status = models.BooleanField(default=False)
    last_online = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    users_with_access = models.ManyToManyField(
        CustomUser,
        through='LocationAccess',
        related_name='accessible_locations',
        blank=True
    )
    # VPN config fields for real-time config
    vpn_enabled = models.BooleanField(default=False)
    vpn_server = models.CharField(max_length=255, blank=True, null=True)
    vpn_client_config = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

class LocationAccess(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    granted_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = ('user', 'location')

    def __str__(self):
        return f"{self.user.username} - {self.location.name}"

class Camera(models.Model):
    STATUS_CHOICES = [
        ('online', 'Online'),
        ('offline', 'Offline'),
        ('error', 'Error'),
        ('unknown', 'Unknown'),
    ]
    name = models.CharField(max_length=100, null=False)
    rtsp_url = models.CharField(max_length=255, null=False)
    shinobi_id = models.CharField(max_length=100, blank=True, null=True)
    shinobi_api_key = models.CharField(max_length=100, blank=True, null=True)
    shinobi_group_key = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="offline")
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='cameras')
    is_recording = models.BooleanField(default=False)
    is_ptz = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    last_updated = models.DateTimeField(auto_now=True)

    # Camera grouping for access control
    groups = models.ManyToManyField(CameraGroup, related_name='cameras', blank=True)

    def __str__(self):
        return f"{self.name} ({self.location.name})"

    @property
    def shinobi_monitor_id(self):
        """Compatibility property for shinobi_id"""
        return self.shinobi_id

class SystemHealth(models.Model):
    cpu_usage = models.FloatField(null=True, blank=True)
    memory_usage = models.FloatField(null=True, blank=True)
    storage_usage = models.FloatField(null=True, blank=True)
    vpn_connections = models.IntegerField(null=True, blank=True)
    online_cameras = models.IntegerField(null=True, blank=True)
    offline_cameras = models.IntegerField(null=True, blank=True)
    last_updated = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Health Check @ {self.last_updated.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        verbose_name_plural = "System Health Records"


class Incident(models.Model):
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('closed', 'Closed'),
        ('investigating', 'Investigating'),
    ]
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    title = models.CharField(max_length=100, null=False)
    description = models.TextField(blank=True, null=True)
    camera = models.ForeignKey(Camera, on_delete=models.SET_NULL, null=True, blank=True, related_name='incidents')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='incidents') # Link to location directly
    reporter = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='reported_incidents')
    snapshot_url = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="open")
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default="medium")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class VpnClient(models.Model):
    client_name = models.CharField(max_length=128)
    cert_cn = models.CharField(max_length=128, unique=True)
    ovpn_path = models.CharField(max_length=256)
    created_at = models.DateTimeField(auto_now_add=True)
    revoked = models.BooleanField(default=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    location = models.ForeignKey('Location', on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"{self.client_name} ({'revoked' if self.revoked else 'active'})"
