# Generated by Django 5.1.6 on 2025-05-31 00:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='location',
            name='vpn_client_config',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='location',
            name='vpn_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='location',
            name='vpn_server',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
