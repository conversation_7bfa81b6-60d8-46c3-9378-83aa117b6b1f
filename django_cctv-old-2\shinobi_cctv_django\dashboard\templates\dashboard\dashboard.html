{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}Dashboard - ABC CCTV System{% endblock %}

{% block extra_css %}
{# dashboard.html specific CSS if any #}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-dashboard">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-icon text-primary"><i class="bi bi-camera-video"></i></div>
                <h5 class="card-title">Cameras</h5>
                <div class="display-5 fw-bold mb-0" id="total-cameras">{{ camera_count|default:0 }}</div>
                <div class="text-muted">
                    <span class="text-success" id="online-cameras">{{ online_cameras|default:0 }}</span> online /
                    <span class="text-danger" id="offline-cameras">{{ offline_cameras|default:0 }}</span> offline
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-icon text-primary"><i class="bi bi-buildings"></i></div>
                <h5 class="card-title">Locations</h5>
                <div class="display-5 fw-bold mb-0" id="total-locations">{{ total_locations|default:0 }}</div>
                <div class="text-muted">
                    <span class="text-success" id="online-locations">{{ online_locations|default:0 }}</span> connected /
                    <span class="text-danger" id="offline-locations">{{ offline_locations|default:0 }}</span> disconnected
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-icon text-primary"><i class="bi bi-exclamation-triangle"></i></div>
                <h5 class="card-title">Incidents</h5>
                <div class="display-5 fw-bold mb-0" id="open-incidents">{{ open_incidents|default:0 }}</div>
                <div class="text-muted">Open incidents</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-icon text-primary"><i class="bi bi-shield-lock"></i></div>
                <h5 class="card-title">VPN Status</h5>
                <div class="display-5 fw-bold mb-0" id="vpn-connections">{{ health.vpn_connections|default:0 }}</div>
                <div class="text-muted">Active connections</div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">CPU Usage</div>
            <div class="card-body d-flex flex-column justify-content-center">
                <div class="progress mb-2" style="height: 20px;">
                    <div id="cpu-progress" class="progress-bar" role="progressbar"
                         style="width: {{ health.cpu_usage|default:0 }}%"
                         aria-valuenow="{{ health.cpu_usage|default:0 }}" aria-valuemin="0" aria-valuemax="100">
                        <span id="cpu-usage">{{ health.cpu_usage|default:0|floatformat:1 }}%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">Memory Usage</div>
            <div class="card-body d-flex flex-column justify-content-center">
                <div class="progress mb-2" style="height: 20px;">
                    <div id="memory-progress" class="progress-bar" role="progressbar"
                         style="width: {{ health.memory_usage|default:0 }}%"
                         aria-valuenow="{{ health.memory_usage|default:0 }}" aria-valuemin="0" aria-valuemax="100">
                        <span id="memory-usage">{{ health.memory_usage|default:0|floatformat:1 }}%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">Storage Usage</div>
            <div class="card-body d-flex flex-column justify-content-center">
                <div class="progress mb-2" style="height: 20px;">
                    <div id="storage-progress" class="progress-bar" role="progressbar"
                         style="width: {{ health.storage_usage|default:0 }}%"
                         aria-valuenow="{{ health.storage_usage|default:0 }}" aria-valuemin="0" aria-valuemax="100">
                        <span id="storage-usage">{{ health.storage_usage|default:0|floatformat:1 }}%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<h4 class="mb-3">Locations Overview</h4>
<div class="row mb-4">
    {% for location in locations %}
    <div class="col-md-4 mb-4">
        <div class="card location-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ location.name }}</h5>
                <span class="badge {% if location.vpn_status %}bg-success{% else %}bg-danger{% endif %}">
                    {% if location.vpn_status %}Online{% else %}Offline{% endif %}
                </span>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    {{ location.address|default_if_none:"" }}{% if location.address and location.city %}, {% endif %}{{ location.city|default_if_none:"" }}
                </p>
                <hr>
                <div class="d-flex justify-content-between">
                    <div><strong>Cameras:</strong> <span class="badge bg-secondary">{{ location.camera_set.count }}</span></div>
                    <div class="vpn-status {% if location.vpn_status %}vpn-connected{% else %}vpn-disconnected{% endif %}">
                        <i class="bi {% if location.vpn_status %}bi-shield-check{% else %}bi-shield-x{% endif %}"></i>
                        VPN {% if location.vpn_status %}Connected{% else %}Disconnected{% endif %}
                    </div>
                </div>
                <div class="mt-3 d-grid">
                    <a href="{% url 'dashboard:cameras' %}?location={{ location.id }}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-camera-video"></i> View Cameras
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12"><p class="text-center text-muted">No locations accessible or configured.</p></div>
    {% endfor %}
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header"><h5 class="mb-0">Recent Incidents</h5></div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr><th>ID</th><th>Title</th><th>Location</th><th>Date</th><th>Severity</th><th>Status</th></tr>
                        </thead>
                        <tbody>
                            {% for incident in incidents %}
                            <tr>
                                <td>INC-{{ incident.id }}</td>
                                <td><a href="{% url 'dashboard:incident_detail' incident_id=incident.id %}">{{ incident.title|truncatechars:30 }}</a></td>
                                <td>{{ incident.location.name|default:"N/A" }}</td>
                                <td>{{ incident.created_at|date:"Y-m-d H:i" }}</td>
                                <td><span class="badge bg-{{ incident.get_severity_display|lower }}">{{ incident.get_severity_display }}</span></td>
                                <td><span class="badge bg-{{ incident.get_status_display|lower }}">{{ incident.get_status_display }}</span></td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="6" class="text-center text-muted py-3">No recent incidents.</td></tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer text-end">
                <a href="{% url 'dashboard:incidents_list' %}" class="btn btn-sm btn-outline-primary">View All Incidents</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{% static 'dashboard/js/dashboard.js' %}"></script>
{% endblock %}