{% load static %}
<nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="{% url 'dashboard:dashboard' %}">
            {# <img src="{% static 'dashboard/svg/camera-icon.svg' %}" alt="Shinobi Logo" width="30" height="30" class="me-2"> #}
            <i class="bi bi-camera-video fs-4 me-2"></i> {# Using Bootstrap icon for now #}
            <span>Eagle CCTV</span>
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain" aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarMain">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link {% if page_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard:dashboard' %}">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if page_name == 'cameras' %}active{% endif %}" href="{% url 'dashboard:cameras' %}">
                        <i class="bi bi-camera-video"></i> Cameras
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if page_name == 'locations' %}active{% endif %}" href="{% url 'dashboard:locations' %}">
                        <i class="bi bi-buildings"></i> Locations
                    </a>
                </li>
                {% if request.user.is_superuser or request.user.role and request.user.role.name == "Administrator" %}
                <li class="nav-item">
                    <a class="nav-link {% if page_name == 'users' %}active{% endif %}" href="{% url 'dashboard:users' %}">
                        <i class="bi bi-people"></i> Users
                    </a>
                </li>
                {% endif %}
                <li class="nav-item">
                    <a class="nav-link {% if page_name == 'system_health' %}active{% endif %}" href="{% url 'dashboard:system_health' %}">
                        <i class="bi bi-heart-pulse"></i> System Health
                    </a>
                </li>
                {% if request.user.is_superuser or request.user.role and request.user.role.name == "Administrator" %}
                <li class="nav-item">
                    <a class="nav-link {% if page_name == 'settings' %}active{% endif %}" href="{% url 'dashboard:settings' %}">
                        <i class="bi bi-gear"></i> Settings
                    </a>
                </li>
                {% endif %}
            </ul>

            <div class="d-flex align-items-center">
                <div class="dropdown me-3">
                    <a class="nav-link position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-bell fs-5"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            3 <span class="visually-hidden">unread notifications</span>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="width: 300px;">
                        {# ... Notification items ... #}
                        <li><a class="dropdown-item text-center small" href="#">View All Notifications</a></li>
                    </ul>
                </div>

                <div class="dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="d-flex align-items-center">
                            <div class="avatar bg-primary rounded-circle me-2 d-flex align-items-center justify-content-center text-white" style="width: 32px; height: 32px;">
                                <span>{{ request.user.first_name.0|default:request.user.username.0|upper }}</span>
                            </div>
                            <span class="d-none d-md-inline">{{ request.user.first_name|default:request.user.username }}</span>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <div class="dropdown-item-text">
                                <div>{{ request.user.get_full_name|default:request.user.username }}</div>
                                <div class="small text-muted">{{ request.user.role.name|default:'User' }}</div>
                            </div>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-key me-2"></i> Change Password</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'dashboard:logout' %}"><i class="bi bi-box-arrow-right me-2"></i> Log Out</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>