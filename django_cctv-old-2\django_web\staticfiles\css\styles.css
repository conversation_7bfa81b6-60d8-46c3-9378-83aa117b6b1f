/* Custom styles for CCTV Monitoring Web Application */

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #3b3b3b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Custom animations */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-online {
  background-color: #10B981; /* Green */
  box-shadow: 0 0 0 rgba(16, 185, 129, 0.4);
  animation: pulse-green 2s infinite;
}

.status-offline {
  background-color: #EF4444; /* Red */
}

.status-disabled {
  background-color: #9CA3AF; /* Gray */
}

.status-maintenance {
  background-color: #F59E0B; /* Amber */
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

/* Camera grid */
.camera-grid {
  display: grid;
  gap: 16px;
  width: 100%;
}

.camera-grid.layout-1x1 {
  grid-template-columns: 1fr;
}

.camera-grid.layout-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.camera-grid.layout-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.camera-grid.layout-4x4 {
  grid-template-columns: repeat(4, 1fr);
}

.camera-feed {
  aspect-ratio: 16/9;
  background-color: #000;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.camera-feed iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.camera-feed-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .camera-grid.layout-3x3,
  .camera-grid.layout-4x4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .camera-grid.layout-2x2,
  .camera-grid.layout-3x3,
  .camera-grid.layout-4x4 {
    grid-template-columns: 1fr;
  }
}

/* Dark mode optimizations */
.dark-mode {
  background-color: #111827;
  color: #f3f4f6;
}

.dark-mode .card {
  background-color: #1F2937;
  border-color: #374151;
}

.dark-mode .sidebar {
  background-color: #111827;
}

/* Dashboard statistics */
.stat-card {
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}