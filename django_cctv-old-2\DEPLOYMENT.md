# 🎖️ ONE-COMMAND DEPLOYMENT GUIDE

## 🚀 Complete System Deployment

Deploy the entire CCTV Warehouse Monitoring System with **one command**!

## ⚡ Quick Start

```bash
# Deploy everything automatically
make deploy
```

## 🔧 What Gets Deployed

### ✅ **Automatic Initialization:**
- 🔐 **OpenVPN Server**: PKI setup, certificates, server config
- 📹 **Shinobi NVR**: Database tables, super admin, API keys
- 🗄️ **Databases**: PostgreSQL and MariaDB with schemas
- 📁 **Configuration Files**: All service configs auto-generated
- 🔑 **Certificates**: VPN certificates and keys created

### ✅ **Services Started:**
- **Django Web** (Port 8000): Main web interface
- **Shinobi CCTV** (Port 5000): Camera management
- **Shinobi NVR** (Port 8080): Video recording
- **OpenVPN Server** (Port 1194): VPN access
- **PostgreSQL**: Main database
- **MariaDB**: Shinobi database
- **pgAdmin** (Port 5050): Database management

## 🎯 Access Points

| Service | URL | Credentials |
|---------|-----|-------------|
| **Django Web** | http://localhost:8000 | Create admin user |
| **Shinobi CCTV** | http://localhost:5000 | Create admin user |
| **Shinobi NVR** | http://localhost:8080 | <EMAIL> / sU5EjCH63wRMSo048y1tOdvm3B6xGk |
| **pgAdmin** | http://localhost:5050 | <EMAIL> / admin123 |

## 📋 Available Commands

```bash
# Full deployment
make deploy          # Initialize + build + start all services

# System management
make status          # Show service status
make logs            # View all logs
make test            # Test functionality
make restart         # Restart all services
make clean           # Stop and remove everything

# Manual steps (if needed)
make init            # Just initialize configs
./scripts/init-all.sh # Run initialization script
```

## 🔍 Verification Steps

After deployment, verify everything is working:

```bash
# Check service status
make status

# Test all endpoints
make test

# View logs if needed
make logs
```

## 🛠️ What Happens During Deployment

### 1. **Initialization Phase** (`make init`)
- Creates directory structure
- Generates environment files
- Creates Shinobi configuration files
- Sets proper permissions

### 2. **Build Phase** (`docker-compose up -d --build`)
- Builds custom Docker images
- Pulls required base images
- Creates networks and volumes

### 3. **Auto-Configuration Phase**
- **OpenVPN**: Runs `init-openvpn.sh` to setup PKI
- **Shinobi**: Runs `init-shinobi.sh` to setup database
- **Services**: Start with proper dependencies

### 4. **Verification Phase**
- Health checks for all services
- Database connectivity tests
- VPN server status check

## 🔧 Manual Deployment (Alternative)

If you prefer step-by-step deployment:

```bash
# 1. Initialize system
chmod +x scripts/*.sh
./scripts/init-all.sh

# 2. Start services
docker-compose up -d --build

# 3. Wait for initialization
sleep 30

# 4. Check status
docker-compose ps
```

## 🚨 Troubleshooting

### Services Not Starting
```bash
# Check logs
docker-compose logs <service-name>

# Common issues:
# - Port conflicts: Change ports in docker-compose.yml
# - Permission issues: Run with sudo (Linux/Mac)
# - Memory issues: Increase Docker memory limit
```

### Database Issues
```bash
# Reset everything
make clean
make deploy
```

### VPN Issues
```bash
# Check OpenVPN initialization
docker-compose logs openvpn

# Verify certificates
docker exec openvpn_server ls -la /etc/openvpn/pki/
```

## 🏭 Production Deployment

For production environments:

1. **Update credentials** in `.env` files
2. **Configure DuckDNS** domain
3. **Set up SSL certificates**
4. **Configure firewall rules**
5. **Set up backup procedures**

```bash
# Production deployment
make production
```

## ✅ Success Indicators

You'll know deployment succeeded when:

- ✅ All services show "Up" status
- ✅ Web interfaces are accessible
- ✅ VPN certificates are generated
- ✅ Databases are initialized
- ✅ Health checks pass

## 🎉 Ready for Use!

Your complete CCTV Warehouse Monitoring System is now deployed and ready for production use!
