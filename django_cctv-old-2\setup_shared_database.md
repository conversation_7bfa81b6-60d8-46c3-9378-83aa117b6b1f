# Shared Database Setup Guide

This guide explains how to set up both Django services (django_web and shinobi_cctv_django) to use the same PostgreSQL database while maintaining separate session management.

## Configuration Changes Made

### 1. Database Configuration
Both services now use the same PostgreSQL database:
- **Database Name**: `warehouse_shinobi`
- **User**: `user`
- **Password**: `admin`
- **Host**: `db` (PostgreSQL container)
- **Port**: `5432`

### 2. Session Management Separation
To prevent session interference between the two services:

**django_web (Port 8000)**:
- Session Cookie Name: `django_web_sessionid`
- CSRF Cookie Name: `django_web_csrftoken`

**shinobi_cctv_django (Port 5000)**:
- Session Cookie Name: `shinobi_cctv_sessionid`
- CSRF Cookie Name: `shinobi_cctv_csrftoken`

### 3. Port Configuration
- **django_web**: Accessible on port 8000
- **shinobi_cctv_django**: Accessible on port 5000

## Setup Instructions

### 1. Start the Services
```bash
docker-compose up -d
```

### 2. Run Migrations for Both Services
Since both services share the same database, you need to run migrations for both:

```bash
# For django_web
docker-compose exec web python manage.py migrate

# For shinobi_cctv_django
docker-compose exec shinobi_cctv_django python manage.py migrate
```

### 3. Create Superusers (Optional)
Create admin users for both services:

```bash
# For django_web
docker-compose exec web python manage.py createsuperuser

# For shinobi_cctv_django
docker-compose exec shinobi_cctv_django python manage.py createsuperuser
```

## Important Notes

1. **Database Tables**: Both services will create their tables in the same database. Django's app prefixing will prevent conflicts.

2. **User Models**: Each service has its own user model:
   - `django_web`: `users.User`
   - `shinobi_cctv_django`: `dashboard.CustomUser`

3. **Session Isolation**: Different cookie names ensure that logging into one service doesn't affect the other.

4. **Static Files**: Each service manages its own static files separately.

## Accessing the Services

- **django_web**: http://localhost:8000
- **shinobi_cctv_django**: http://localhost:5000
- **PostgreSQL Admin (pgAdmin)**: http://localhost:5050
  - Email: <EMAIL>
  - Password: admin123

## Troubleshooting

### Database Connection Issues
If you encounter database connection issues:

1. Check if the PostgreSQL container is running:
   ```bash
   docker-compose ps db
   ```

2. Check database logs:
   ```bash
   docker-compose logs db
   ```

### Migration Conflicts
If there are migration conflicts:

1. Check migration status:
   ```bash
   docker-compose exec web python manage.py showmigrations
   docker-compose exec shinobi_cctv_django python manage.py showmigrations
   ```

2. If needed, fake migrations that are already applied:
   ```bash
   docker-compose exec web python manage.py migrate --fake-initial
   ```

### Session Issues
If sessions are still interfering:

1. Clear browser cookies
2. Check that different cookie names are being used in browser developer tools
3. Restart both services:
   ```bash
   docker-compose restart web shinobi_cctv_django
   ```
