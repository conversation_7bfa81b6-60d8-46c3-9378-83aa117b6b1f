#!/bin/bash

# Setup script for Django services with shared database
# This script sets up both django_web and shinobi_cctv_django to use the same PostgreSQL database

set -e  # Exit on any error

echo "🚀 Setting up Django services with shared database"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
fi

print_status "Starting PostgreSQL database..."
docker-compose up -d db

print_status "Waiting for PostgreSQL to be ready..."
sleep 10

# Check if database is ready
print_status "Checking database connectivity..."
if docker-compose exec -T db pg_isready -U user -d warehouse_shinobi; then
    print_success "Database is ready!"
else
    print_error "Database is not ready. Please check the logs."
    docker-compose logs db
    exit 1
fi

print_status "Starting all services..."
docker-compose up -d

print_status "Waiting for services to start..."
sleep 15

print_status "Running migrations for django_web..."
if docker-compose exec -T web python manage.py migrate; then
    print_success "django_web migrations completed successfully!"
else
    print_warning "django_web migrations failed. This might be normal if tables already exist."
fi

print_status "Running migrations for shinobi_cctv_django..."
if docker-compose exec -T shinobi_cctv_django python manage.py migrate; then
    print_success "shinobi_cctv_django migrations completed successfully!"
else
    print_warning "shinobi_cctv_django migrations failed. This might be normal if tables already exist."
fi

print_status "Collecting static files for django_web..."
docker-compose exec -T web python manage.py collectstatic --noinput

print_status "Collecting static files for shinobi_cctv_django..."
docker-compose exec -T shinobi_cctv_django python manage.py collectstatic --noinput --clear

print_status "Testing service availability..."

# Test django_web
if curl -f -s http://localhost:8000 > /dev/null; then
    print_success "django_web is accessible at http://localhost:8000"
else
    print_warning "django_web might not be ready yet at http://localhost:8000"
fi

# Test shinobi_cctv_django
if curl -f -s http://localhost:5000 > /dev/null; then
    print_success "shinobi_cctv_django is accessible at http://localhost:5000"
else
    print_warning "shinobi_cctv_django might not be ready yet at http://localhost:5000"
fi

echo ""
echo "=================================================="
print_success "Setup completed!"
echo ""
echo "📋 Service Information:"
echo "  • django_web: http://localhost:8000"
echo "  • shinobi_cctv_django: http://localhost:5000"
echo "  • pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
echo ""
echo "🔧 Next Steps:"
echo "  1. Create superusers for both services:"
echo "     docker-compose exec web python manage.py createsuperuser"
echo "     docker-compose exec shinobi_cctv_django python manage.py createsuperuser"
echo ""
echo "  2. Access admin interfaces:"
echo "     • django_web admin: http://localhost:8000/admin/"
echo "     • shinobi_cctv_django admin: http://localhost:5000/admin/"
echo ""
echo "  3. Run verification script:"
echo "     python verify_setup.py"
echo ""
echo "🗄️  Database Configuration:"
echo "  • Both services share the same PostgreSQL database"
echo "  • Database: warehouse_shinobi"
echo "  • Sessions are isolated with different cookie names"
echo ""
print_success "All services are configured and running!"
