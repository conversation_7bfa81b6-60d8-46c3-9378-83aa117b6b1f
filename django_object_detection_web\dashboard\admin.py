# =============================================================================
# 🎖️ SHINOBI CCTV DJANGO ADMIN - SHARED MODELS + SERVICE SPECIFIC
# =============================================================================
# UPDATED: Now includes shared models admin for complete management
# =============================================================================

from django.contrib import admin
from .models import (
    # Shared models (unmanaged but need admin interface)
    CustomUser, Role, Camera, CameraGroup, Person, CameraRecognitionEvent,
    # Service-specific models
    Location, LocationAccess, SystemHealth, Incident, VpnClient, RecognitionAlert
)


# =============================================================================
# 🎖️ SHARED MODELS ADMIN (Unmanaged but need admin interface)
# =============================================================================

class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')

class CameraGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')

class CustomUserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'role', 'is_active', 'date_joined')
    list_filter = ('role', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    filter_horizontal = ('camera_groups',)
    readonly_fields = ('date_joined', 'last_login', 'date_modified')

class CameraAdmin(admin.ModelAdmin):
    list_display = ('name', 'location_name', 'status', 'shinobi_monitor_id', 'is_ptz', 'created_at')
    list_filter = ('status', 'type', 'is_ptz', 'is_recording', 'groups')
    search_fields = ('name', 'location_name', 'shinobi_monitor_id', 'shinobi_id')
    filter_horizontal = ('groups',)
    readonly_fields = ('created_at', 'updated_at', 'last_online')

class PersonAdmin(admin.ModelAdmin):
    list_display = ('name', 'employee_id', 'department', 'status', 'last_sync')
    list_filter = ('status', 'department', 'sync_status')
    search_fields = ('name', 'employee_id', 'email')
    readonly_fields = ('face_recognition_id', 'last_sync', 'created_at', 'updated_at')

class CameraRecognitionEventAdmin(admin.ModelAdmin):
    list_display = ('camera', 'person', 'event_type', 'confidence_score', 'timestamp')
    list_filter = ('event_type', 'camera', 'timestamp')
    search_fields = ('camera__name', 'person__name')
    readonly_fields = ('timestamp', 'recognition_log_id', 'processing_time_ms')

# =============================================================================
# 🎖️ SHINOBI CCTV DJANGO SPECIFIC ADMIN
# =============================================================================

class LocationAccessInline(admin.TabularInline):
    model = LocationAccess
    extra = 1
    # autocomplete_fields = ['user']  # Disabled - user model is unmanaged


class LocationAdmin(admin.ModelAdmin):
    list_display = ('name', 'city', 'state', 'vpn_status', 'created_at')
    list_filter = ('vpn_status', 'state', 'city')
    search_fields = ('name', 'address', 'city')
    inlines = [LocationAccessInline]


class IncidentAdmin(admin.ModelAdmin):
    list_display = ('title', 'location', 'camera', 'severity', 'status', 'reporter', 'created_at')
    list_filter = ('status', 'severity', 'location', 'reporter')
    search_fields = ('title', 'description')
    date_hierarchy = 'created_at'
    # autocomplete_fields = ['location', 'camera', 'reporter']  # Disabled - camera and reporter models are unmanaged


class SystemHealthAdmin(admin.ModelAdmin):
    list_display = ('last_updated', 'cpu_usage', 'memory_usage', 'storage_usage', 'vpn_connections', 'online_cameras')
    date_hierarchy = 'last_updated'


class VpnClientAdmin(admin.ModelAdmin):
    list_display = ('client_name', 'cert_cn', 'user', 'location', 'created_at', 'revoked')
    list_filter = ('revoked', 'location', 'created_at')
    search_fields = ('client_name', 'cert_cn', 'user__username')
    # autocomplete_fields = ['user', 'location']  # Disabled - user model is unmanaged


class RecognitionAlertAdmin(admin.ModelAdmin):
    list_display = ('location', 'camera', 'person', 'alert_type', 'alert_status', 'confidence_score', 'timestamp')
    list_filter = ('alert_type', 'alert_status', 'location', 'camera', 'timestamp')
    search_fields = ('person__name', 'camera__name', 'location__name')
    readonly_fields = ('timestamp', 'created_at', 'updated_at', 'recognition_log_id', 'processing_time_ms')
    fieldsets = (
        ('Alert Information', {
            'fields': ('location', 'camera', 'person', 'alert_type', 'alert_status', 'confidence_score')
        }),
        ('Recognition Data', {
            'fields': ('recognition_log_id', 'bounding_box', 'processing_time_ms')
        }),
        ('Alert Management', {
            'fields': ('acknowledged_by', 'acknowledged_at', 'resolution_notes')
        }),
        ('Media', {
            'fields': ('snapshot_url', 'face_crop_path')
        }),
        ('Timestamps', {
            'fields': ('timestamp', 'created_at', 'updated_at')
        }),
        ('Additional Data', {
            'fields': ('extra_data',)
        }),
    )


# =============================================================================
# 🎖️ REGISTER SHARED MODELS (Unmanaged but need admin interface)
# =============================================================================

admin.site.register(Role, RoleAdmin)
admin.site.register(CameraGroup, CameraGroupAdmin)
admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(Camera, CameraAdmin)
admin.site.register(Person, PersonAdmin)
admin.site.register(CameraRecognitionEvent, CameraRecognitionEventAdmin)

# =============================================================================
# 🎖️ REGISTER SHINOBI CCTV DJANGO SPECIFIC MODELS
# =============================================================================

admin.site.register(Location, LocationAdmin)
admin.site.register(LocationAccess)
admin.site.register(SystemHealth, SystemHealthAdmin)
admin.site.register(Incident, IncidentAdmin)
admin.site.register(VpnClient, VpnClientAdmin)
admin.site.register(RecognitionAlert, RecognitionAlertAdmin)
