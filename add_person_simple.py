#!/usr/bin/env python3
"""
🎖️ SIMPLE PERSON REGISTRATION TOOL
Easy way to add people to Face Recognition system
"""

import requests
import json

# Face Recognition Service URL
FACE_RECOGNITION_URL = "http://localhost:8090"

def add_person(name, employee_id=None, department=None, role=None):
    """Add a person to the Face Recognition system"""
    
    person_data = {
        "name": name,
        "employee_id": employee_id,
        "department": department,
        "role": role
    }
    
    try:
        response = requests.post(
            f"{FACE_RECOGNITION_URL}/persons",
            json=person_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Person added successfully!")
            print(f"   Name: {result['name']}")
            print(f"   ID: {result['id']}")
            print(f"   Employee ID: {result.get('employee_id', 'N/A')}")
            return result['id']
        else:
            print(f"❌ Failed to add person: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error adding person: {str(e)}")
        return None

def add_face_image(person_id, image_path):
    """Add a face image to a person"""
    
    try:
        with open(image_path, 'rb') as image_file:
            files = {'face_image': image_file}
            
            response = requests.post(
                f"{FACE_RECOGNITION_URL}/persons/{person_id}/faces",
                files=files,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Face image added successfully!")
                print(f"   File: {result['face_record']['filename']}")
                return True
            else:
                print(f"❌ Failed to add face image: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except FileNotFoundError:
        print(f"❌ Image file not found: {image_path}")
        return False
    except Exception as e:
        print(f"❌ Error adding face image: {str(e)}")
        return False

def list_persons():
    """List all registered persons"""
    
    try:
        response = requests.get(f"{FACE_RECOGNITION_URL}/persons", timeout=10)
        
        if response.status_code == 200:
            persons = response.json()
            print(f"📋 Registered Persons ({len(persons)}):")
            for person in persons:
                print(f"   ID: {person['id']} - {person['name']} ({person.get('employee_id', 'No ID')})")
            return persons
        else:
            print(f"❌ Failed to get persons: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error getting persons: {str(e)}")
        return []

def main():
    """Main function - Interactive person registration"""
    
    print("🎖️ FACE RECOGNITION PERSON REGISTRATION")
    print("=" * 50)
    
    # Check service health
    try:
        response = requests.get(f"{FACE_RECOGNITION_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Face Recognition Service is healthy")
        else:
            print("❌ Face Recognition Service is not responding")
            return
    except:
        print("❌ Cannot connect to Face Recognition Service")
        return
    
    # Show current persons
    print("\n📋 Current registered persons:")
    list_persons()
    
    # Interactive registration
    print("\n🎯 Add New Person:")
    name = input("Enter name: ").strip()
    if not name:
        print("❌ Name is required")
        return
    
    employee_id = input("Enter employee ID (optional): ").strip() or None
    department = input("Enter department (optional): ").strip() or None
    role = input("Enter role (optional): ").strip() or None
    
    # Add person
    person_id = add_person(name, employee_id, department, role)
    
    if person_id:
        # Ask for face image
        add_image = input("\nDo you want to add a face image? (y/n): ").strip().lower()
        if add_image == 'y':
            image_path = input("Enter path to image file: ").strip()
            if image_path:
                add_face_image(person_id, image_path)
    
    print("\n🎖️ Registration complete!")
    print("🎯 The person is now registered for face recognition.")

if __name__ == "__main__":
    main()
