<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎖️ Face Recognition Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #dee2e6;
        }
        .tab {
            padding: 15px 25px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            border-radius: 10px 10px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px 0;
        }
        .tab-content.active {
            display: block;
        }
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; }
        
        .person-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .person-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .person-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .info-item {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
        }
        .info-label {
            font-weight: bold;
            color: #6c757d;
        }
        #results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .edit-form {
            display: none;
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .edit-form.active {
            display: block;
        }
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎖️ Face Recognition Management System</h1>
        
        <!-- Tabs -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('list')">📋 List Persons</button>
            <button class="tab" onclick="showTab('create')">➕ Create Person</button>
            <button class="tab" onclick="showTab('edit')">✏️ Edit Person</button>
            <button class="tab" onclick="showTab('delete')">🗑️ Delete Person</button>
        </div>

        <!-- List Persons Tab -->
        <div id="list" class="tab-content active">
            <div class="section">
                <h2>📋 List All Persons</h2>
                <button class="button btn-primary" onclick="listPersons()">🔄 Refresh List</button>
                <div id="personsList"></div>
            </div>
        </div>

        <!-- Create Person Tab -->
        <div id="create" class="tab-content">
            <div class="section">
                <h2>➕ Create New Person</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="createName">Name *</label>
                        <input type="text" id="createName" placeholder="Enter full name" required>
                    </div>
                    <div class="form-group">
                        <label for="createEmployeeId">Employee ID</label>
                        <input type="text" id="createEmployeeId" placeholder="Enter employee ID">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="createDepartment">Department</label>
                        <input type="text" id="createDepartment" placeholder="Enter department">
                    </div>
                    <div class="form-group">
                        <label for="createRole">Role</label>
                        <input type="text" id="createRole" placeholder="Enter role/position">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="createEmail">Email</label>
                        <input type="email" id="createEmail" placeholder="Enter email address">
                    </div>
                    <div class="form-group">
                        <label for="createPhone">Phone</label>
                        <input type="tel" id="createPhone" placeholder="Enter phone number">
                    </div>
                </div>
                <div class="form-group">
                    <label for="createNotes">Notes</label>
                    <textarea id="createNotes" placeholder="Additional notes or comments"></textarea>
                </div>
                <button class="button btn-success" onclick="createPerson()">➕ Create Person</button>
            </div>
        </div>

        <!-- Edit Person Tab -->
        <div id="edit" class="tab-content">
            <div class="section">
                <h2>✏️ Edit Person Information</h2>
                <div class="alert alert-info">
                    <strong>💡 Tip:</strong> First search for the person, then edit their information. 
                    Perfect for fixing spelling errors in names!
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchPersonId">Search by Person ID</label>
                        <input type="number" id="searchPersonId" placeholder="Enter Person ID" min="1">
                        <button class="button btn-info" onclick="searchPersonById()">🔍 Search by ID</button>
                    </div>
                    <div class="form-group">
                        <label for="searchPersonName">Search by Name</label>
                        <input type="text" id="searchPersonName" placeholder="Enter person name">
                        <button class="button btn-info" onclick="searchPersonByName()">🔍 Search by Name</button>
                    </div>
                </div>

                <!-- Edit Form (hidden by default) -->
                <div id="editForm" class="edit-form">
                    <h3>✏️ Edit Person Information</h3>
                    <input type="hidden" id="editPersonId">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editName">Name</label>
                            <input type="text" id="editName" placeholder="Enter corrected name">
                        </div>
                        <div class="form-group">
                            <label for="editEmployeeId">Employee ID</label>
                            <input type="text" id="editEmployeeId" placeholder="Enter employee ID">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editDepartment">Department</label>
                            <input type="text" id="editDepartment" placeholder="Enter department">
                        </div>
                        <div class="form-group">
                            <label for="editRole">Role</label>
                            <input type="text" id="editRole" placeholder="Enter role">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editEmail">Email</label>
                            <input type="email" id="editEmail" placeholder="Enter email">
                        </div>
                        <div class="form-group">
                            <label for="editPhone">Phone</label>
                            <input type="tel" id="editPhone" placeholder="Enter phone">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editStatus">Status</label>
                            <select id="editStatus">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="suspended">Suspended</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editNotes">Notes</label>
                            <textarea id="editNotes" placeholder="Additional notes"></textarea>
                        </div>
                    </div>
                    
                    <button class="button btn-warning" onclick="updatePerson()">✏️ Update Person</button>
                    <button class="button btn-info" onclick="cancelEdit()">❌ Cancel</button>
                </div>
            </div>
        </div>

        <!-- Delete Person Tab -->
        <div id="delete" class="tab-content">
            <div class="section">
                <h2>🗑️ Delete Person</h2>
                <div class="alert alert-warning">
                    <strong>⚠️ WARNING:</strong> Deletion is PERMANENT and cannot be undone!
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="deleteId">Delete by Person ID</label>
                        <input type="number" id="deleteId" placeholder="Enter Person ID" min="1">
                        <button class="button btn-danger" onclick="deletePersonById()">🗑️ Delete by ID</button>
                    </div>
                    <div class="form-group">
                        <label for="deleteName">Delete by Name</label>
                        <input type="text" id="deleteName" placeholder="Enter person name">
                        <button class="button btn-danger" onclick="deletePersonByName()">🗑️ Delete by Name</button>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="deleteEmployeeId">Delete by Employee ID</label>
                        <input type="text" id="deleteEmployeeId" placeholder="Enter employee ID">
                        <button class="button btn-danger" onclick="deletePersonByEmployeeId()">🗑️ Delete by Employee ID</button>
                    </div>
                    <div class="form-group">
                        <label for="deleteFacesId">Delete Face Features Only</label>
                        <input type="number" id="deleteFacesId" placeholder="Enter Person ID" min="1">
                        <button class="button btn-warning" onclick="deletePersonFaces()">🗑️ Delete Faces Only</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Area -->
        <div class="section">
            <h2>📊 Results</h2>
            <div id="results">Ready for operations...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8090/api/v1/persons';
        
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
            
            // Auto-load persons list when switching to list tab
            if (tabName === 'list') {
                listPersons();
            }
        }
        
        function updateResults(data) {
            document.getElementById('results').textContent = JSON.stringify(data, null, 2);
        }
        
        function showError(error) {
            document.getElementById('results').textContent = `❌ Error: ${error}`;
        }
        
        function showSuccess(message) {
            document.getElementById('results').textContent = `✅ Success: ${message}`;
        }
        
        // CREATE PERSON
        async function createPerson() {
            const personData = {
                name: document.getElementById('createName').value,
                employee_id: document.getElementById('createEmployeeId').value || null,
                department: document.getElementById('createDepartment').value || null,
                role: document.getElementById('createRole').value || null,
                email: document.getElementById('createEmail').value || null,
                phone: document.getElementById('createPhone').value || null,
                notes: document.getElementById('createNotes').value || null
            };
            
            if (!personData.name) {
                showError('Name is required');
                return;
            }
            
            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(personData)
                });
                const data = await response.json();
                updateResults(data);
                
                if (data.success) {
                    // Clear form
                    document.querySelectorAll('#create input, #create textarea').forEach(input => {
                        input.value = '';
                    });
                    showSuccess(`Person '${data.person.name}' created successfully!`);
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        // LIST PERSONS
        async function listPersons() {
            try {
                const response = await fetch(API_BASE);
                const data = await response.json();
                updateResults(data);
                
                if (data.success) {
                    displayPersonsList(data.persons);
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        function displayPersonsList(persons) {
            const container = document.getElementById('personsList');
            if (!persons || persons.length === 0) {
                container.innerHTML = '<p>No persons found.</p>';
                return;
            }
            
            let html = '';
            persons.forEach(person => {
                html += `
                    <div class="person-card">
                        <h4>${person.name} (ID: ${person.id})</h4>
                        <div class="person-info">
                            <div class="info-item"><span class="info-label">Employee ID:</span> ${person.employee_id || 'N/A'}</div>
                            <div class="info-item"><span class="info-label">Department:</span> ${person.department || 'N/A'}</div>
                            <div class="info-item"><span class="info-label">Role:</span> ${person.role || 'N/A'}</div>
                            <div class="info-item"><span class="info-label">Status:</span> ${person.status}</div>
                            <div class="info-item"><span class="info-label">Face Records:</span> ${person.face_count}</div>
                            <div class="info-item"><span class="info-label">Created:</span> ${new Date(person.created_at).toLocaleDateString()}</div>
                        </div>
                        <button class="button btn-warning" onclick="editPersonFromList(${person.id})">✏️ Edit</button>
                        <button class="button btn-danger" onclick="deletePersonFromList(${person.id}, '${person.name}')">🗑️ Delete</button>
                    </div>
                `;
            });
            container.innerHTML = html;
        }
        
        // SEARCH AND EDIT
        async function searchPersonById() {
            const personId = document.getElementById('searchPersonId').value;
            if (!personId) {
                showError('Please enter a Person ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/${personId}`);
                const data = await response.json();
                
                if (data.success) {
                    populateEditForm(data.person);
                    updateResults(data);
                } else {
                    showError('Person not found');
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function searchPersonByName() {
            const personName = document.getElementById('searchPersonName').value;
            if (!personName) {
                showError('Please enter a person name');
                return;
            }
            
            // Search through all persons (simple implementation)
            try {
                const response = await fetch(API_BASE);
                const data = await response.json();
                
                if (data.success) {
                    const person = data.persons.find(p => 
                        p.name.toLowerCase().includes(personName.toLowerCase())
                    );
                    
                    if (person) {
                        populateEditForm(person);
                        updateResults({success: true, message: `Found person: ${person.name}`});
                    } else {
                        showError(`No person found with name containing '${personName}'`);
                    }
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        function populateEditForm(person) {
            document.getElementById('editPersonId').value = person.id;
            document.getElementById('editName').value = person.name || '';
            document.getElementById('editEmployeeId').value = person.employee_id || '';
            document.getElementById('editDepartment').value = person.department || '';
            document.getElementById('editRole').value = person.role || '';
            document.getElementById('editEmail').value = person.email || '';
            document.getElementById('editPhone').value = person.phone || '';
            document.getElementById('editStatus').value = person.status || 'active';
            document.getElementById('editNotes').value = person.notes || '';
            
            document.getElementById('editForm').classList.add('active');
        }
        
        function editPersonFromList(personId) {
            showTab('edit');
            document.getElementById('searchPersonId').value = personId;
            searchPersonById();
        }
        
        async function updatePerson() {
            const personId = document.getElementById('editPersonId').value;
            if (!personId) {
                showError('No person selected for editing');
                return;
            }
            
            const updateData = {
                name: document.getElementById('editName').value || null,
                employee_id: document.getElementById('editEmployeeId').value || null,
                department: document.getElementById('editDepartment').value || null,
                role: document.getElementById('editRole').value || null,
                email: document.getElementById('editEmail').value || null,
                phone: document.getElementById('editPhone').value || null,
                status: document.getElementById('editStatus').value || null,
                notes: document.getElementById('editNotes').value || null
            };
            
            try {
                const response = await fetch(`${API_BASE}/${personId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updateData)
                });
                const data = await response.json();
                updateResults(data);
                
                if (data.success) {
                    showSuccess(`Person updated successfully!`);
                    cancelEdit();
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        function cancelEdit() {
            document.getElementById('editForm').classList.remove('active');
            document.querySelectorAll('#editForm input, #editForm textarea, #editForm select').forEach(input => {
                input.value = '';
            });
        }
        
        // DELETE FUNCTIONS
        async function deletePersonById() {
            const personId = document.getElementById('deleteId').value;
            if (!personId) {
                showError('Please enter a Person ID');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete person ID ${personId} and ALL associated data?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/${personId}?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
                
                if (data.success) {
                    document.getElementById('deleteId').value = '';
                    showSuccess(data.message);
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function deletePersonByName() {
            const personName = document.getElementById('deleteName').value;
            if (!personName) {
                showError('Please enter a Person Name');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete person "${personName}" and ALL associated data?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/by-name/${encodeURIComponent(personName)}?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
                
                if (data.success) {
                    document.getElementById('deleteName').value = '';
                    showSuccess(data.message);
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function deletePersonByEmployeeId() {
            const employeeId = document.getElementById('deleteEmployeeId').value;
            if (!employeeId) {
                showError('Please enter an Employee ID');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete person with employee ID "${employeeId}" and ALL associated data?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/by-employee-id/${encodeURIComponent(employeeId)}?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
                
                if (data.success) {
                    document.getElementById('deleteEmployeeId').value = '';
                    showSuccess(data.message);
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        async function deletePersonFaces() {
            const personId = document.getElementById('deleteFacesId').value;
            if (!personId) {
                showError('Please enter a Person ID');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete face features for person ID ${personId}? (Person record will be kept)`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/${personId}/faces?confirm=true`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                updateResults(data);
                
                if (data.success) {
                    document.getElementById('deleteFacesId').value = '';
                    showSuccess(data.message);
                }
            } catch (error) {
                showError(error.message);
            }
        }
        
        function deletePersonFromList(personId, personName) {
            if (confirm(`Are you sure you want to delete "${personName}" and ALL associated data?`)) {
                fetch(`${API_BASE}/${personId}?confirm=true`, { method: 'DELETE' })
                    .then(r => r.json())
                    .then(data => {
                        updateResults(data);
                        if (data.success) {
                            showSuccess(data.message);
                            listPersons(); // Refresh list
                        }
                    })
                    .catch(error => showError(error.message));
            }
        }
        
        // Auto-load persons list on page load
        document.addEventListener('DOMContentLoaded', function() {
            listPersons();
        });
    </script>
</body>
</html>
