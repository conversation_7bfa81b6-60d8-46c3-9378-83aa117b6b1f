#!/bin/bash
# 🎖️ FACE RECOGNITION MICROSERVICE ENTRYPOINT
# 🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 3
# ⚔️ TACTICAL DOCKER ENTRYPOINT

set -e

echo "🎖️ Face Recognition Microservice Starting..."
echo "🎯 Operation: Face Recognition Empire - Phase 3"
echo "⚔️ Docker Integration Mode"

# Wait for dependencies (if any)
echo "🔍 Checking dependencies..."

# Check if Redis is available (optional)
if [ ! -z "$REDIS_URL" ]; then
    echo "🚀 Checking Redis connection..."
    # Extract host and port from Redis URL
    REDIS_HOST=$(echo $REDIS_URL | sed -n 's/.*:\/\/\([^:]*\):.*/\1/p')
    REDIS_PORT=$(echo $REDIS_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    
    if [ ! -z "$REDIS_HOST" ] && [ ! -z "$REDIS_PORT" ]; then
        echo "🔄 Waiting for Redis at $REDIS_HOST:$REDIS_PORT..."
        timeout 30 bash -c "until nc -z $REDIS_HOST $REDIS_PORT; do sleep 1; done" || echo "⚠️ Redis not available, continuing without cache"
    fi
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p /app/uploads /app/faces /app/weights /app/logs /app/static /app/media

# Set permissions
echo "🔧 Setting permissions..."
chmod -R 755 /app/uploads /app/faces /app/weights /app/logs /app/static /app/media

# Check Python availability
echo "🚀 Checking Python environment..."
python --version || echo "⚠️ Python check failed"

# Initialize database (if needed)
echo "🗄️ Initializing database..."
# Database initialization would go here if needed

echo "✅ Face Recognition Microservice initialization complete!"
echo "🌐 Starting service on port 8090..."

# Start the application (FULL AI SERVICE with YOLOv11 + ArcFace)
echo "🧠 Starting FULL AI SERVICE with YOLOv11 + ArcFace..."
exec python -m app.main
