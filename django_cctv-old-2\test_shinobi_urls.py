#!/usr/bin/env python3
"""
Test different Shinobi URL patterns to find the working embed format
"""

import requests
import time

# Configuration
HOST = "http://localhost:8080"
API_KEY = "vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx"
GROUP_KEY = "VqJe1awj1m"
MONITOR_ID = "L537lfvteS"

def test_url(url, description):
    """Test a specific URL"""
    print(f"\n🔍 Testing: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            print(f"✅ SUCCESS! Content-Type: {content_type}")
            if 'html' in content_type.lower():
                print("   Response contains HTML (likely embed page)")
            elif 'video' in content_type.lower() or 'stream' in content_type.lower():
                print("   Response contains video stream")
            else:
                print(f"   Response length: {len(response.content)} bytes")
            return True
        else:
            print(f"❌ Failed: {response.text[:100]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Timeout")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🚀 Testing Shinobi Embed URL Patterns")
    print("=" * 60)
    
    # Test different URL patterns
    url_patterns = [
        # Standard embed patterns
        (f"{HOST}/embed/{GROUP_KEY}/{MONITOR_ID}/{API_KEY}", "Pattern 1: /embed/group/monitor/api"),
        (f"{HOST}/{GROUP_KEY}/embed/{MONITOR_ID}/{API_KEY}", "Pattern 2: /group/embed/monitor/api"),
        (f"{HOST}/{GROUP_KEY}/embed/{MONITOR_ID}?api={API_KEY}", "Pattern 3: /group/embed/monitor?api=key"),
        (f"{HOST}/embed/{GROUP_KEY}/{MONITOR_ID}?api={API_KEY}", "Pattern 4: /embed/group/monitor?api=key"),
        
        # Alternative patterns
        (f"{HOST}/{GROUP_KEY}/monitor/{MONITOR_ID}/embed?api={API_KEY}", "Pattern 5: /group/monitor/id/embed?api=key"),
        (f"{HOST}/monitor/{GROUP_KEY}/{MONITOR_ID}/embed?api={API_KEY}", "Pattern 6: /monitor/group/id/embed?api=key"),
        (f"{HOST}/{GROUP_KEY}/stream/{MONITOR_ID}?api={API_KEY}", "Pattern 7: /group/stream/monitor?api=key"),
        (f"{HOST}/stream/{GROUP_KEY}/{MONITOR_ID}?api={API_KEY}", "Pattern 8: /stream/group/monitor?api=key"),
        
        # Live stream patterns
        (f"{HOST}/{GROUP_KEY}/mjpeg/{MONITOR_ID}?api={API_KEY}", "Pattern 9: /group/mjpeg/monitor?api=key"),
        (f"{HOST}/{GROUP_KEY}/hls/{MONITOR_ID}/index.m3u8?api={API_KEY}", "Pattern 10: /group/hls/monitor/index.m3u8?api=key"),
        (f"{HOST}/{GROUP_KEY}/mp4/{MONITOR_ID}?api={API_KEY}", "Pattern 11: /group/mp4/monitor?api=key"),
        
        # Direct access patterns
        (f"{HOST}/{GROUP_KEY}/embed/{MONITOR_ID}", "Pattern 12: /group/embed/monitor (no API)"),
        (f"{HOST}/embed/{GROUP_KEY}/{MONITOR_ID}", "Pattern 13: /embed/group/monitor (no API)"),
        
        # With different API parameter names
        (f"{HOST}/{GROUP_KEY}/embed/{MONITOR_ID}?key={API_KEY}", "Pattern 14: /group/embed/monitor?key=api"),
        (f"{HOST}/{GROUP_KEY}/embed/{MONITOR_ID}?auth={API_KEY}", "Pattern 15: /group/embed/monitor?auth=api"),
    ]
    
    successful_patterns = []
    
    for url, description in url_patterns:
        success = test_url(url, description)
        if success:
            successful_patterns.append((url, description))
        time.sleep(0.5)  # Small delay between requests
    
    print("\n" + "=" * 60)
    print("📊 RESULTS")
    print("=" * 60)
    
    if successful_patterns:
        print(f"✅ Found {len(successful_patterns)} working pattern(s):")
        for url, description in successful_patterns:
            print(f"\n🎯 {description}")
            print(f"   URL: {url}")
    else:
        print("❌ No working patterns found!")
        print("\nTroubleshooting suggestions:")
        print("1. Check if Shinobi is running on localhost:8080")
        print("2. Verify API key and group key are correct")
        print("3. Check Shinobi configuration for embed settings")
        print("4. Try accessing Shinobi web interface directly")

if __name__ == "__main__":
    main()
