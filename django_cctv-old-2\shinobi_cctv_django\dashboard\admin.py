from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import Role, CustomUser, Location, LocationAccess, Camera, SystemHealth, Incident, CameraGroup
from .forms import CustomUserCreationForm, CustomUserChangeForm

class LocationAccessInline(admin.TabularInline):
    model = LocationAccess
    extra = 1
    autocomplete_fields = ['location']


class CustomUserAdmin(BaseUserAdmin):
    form = CustomUserChangeForm
    add_form = CustomUserCreationForm

    list_display = ('username', 'email', 'first_name', 'last_name', 'role', 'get_camera_groups', 'is_staff', 'is_active')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'role', 'camera_groups')
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'email')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
        ('Custom Profile', {'fields': ('role', 'camera_groups')}),
    )
    filter_horizontal = ('camera_groups', 'groups', 'user_permissions')

    def get_camera_groups(self, obj):
        """Display camera groups in list view"""
        if obj.is_admin:
            return "All (Admin)"
        groups = obj.camera_groups.all()
        if groups:
            return ", ".join([group.name for group in groups])
        return "None"
    get_camera_groups.short_description = 'Camera Access'
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'password1', 'password2', 'first_name', 'last_name', 'email', 'role', 'is_active'),
        }),
    )
    search_fields = ('username', 'first_name', 'last_name', 'email')
    ordering = ('username',)
    inlines = [LocationAccessInline]

    def get_fieldsets(self, request, obj=None):
        if not obj:
            return self.add_fieldsets
        return super().get_fieldsets(request, obj)

    def get_form(self, request, obj=None, **kwargs):
        if obj is None:
            kwargs['form'] = self.add_form
        else:
            kwargs['form'] = self.form
        return super().get_form(request, obj, **kwargs)


class LocationAdmin(admin.ModelAdmin):
    list_display = ('name', 'city', 'state', 'vpn_status', 'created_at')
    list_filter = ('vpn_status', 'state', 'city')
    search_fields = ('name', 'address', 'city')
    inlines = [LocationAccessInline]
    autocomplete_fields = [] # For LocationAccessInline if user field is large


class CameraAdmin(admin.ModelAdmin):
    list_display = ('name', 'location', 'status', 'get_groups', 'is_recording', 'is_ptz')
    list_filter = ('status', 'is_recording', 'is_ptz', 'location', 'groups')
    search_fields = ('name', 'rtsp_url', 'shinobi_id')
    autocomplete_fields = ['location']
    filter_horizontal = ['groups']

    def get_groups(self, obj):
        """Display camera groups in list view"""
        return ", ".join([group.name for group in obj.groups.all()])
    get_groups.short_description = 'Camera Groups'

class CameraGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'get_camera_count', 'get_user_count', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')

    def get_camera_count(self, obj):
        """Display number of cameras in this group"""
        return obj.cameras.count()
    get_camera_count.short_description = 'Cameras'

    def get_user_count(self, obj):
        """Display number of users with access to this group"""
        return obj.users.count()
    get_user_count.short_description = 'Users with Access'


class IncidentAdmin(admin.ModelAdmin):
    list_display = ('title', 'location', 'camera', 'severity', 'status', 'reporter', 'created_at')
    list_filter = ('status', 'severity', 'location', 'reporter')
    search_fields = ('title', 'description')
    date_hierarchy = 'created_at'
    autocomplete_fields = ['location', 'camera', 'reporter']


class SystemHealthAdmin(admin.ModelAdmin):
    list_display = ('last_updated', 'cpu_usage', 'memory_usage', 'storage_usage', 'vpn_connections', 'online_cameras')
    date_hierarchy = 'last_updated'


admin.site.register(Role)
admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(Location, LocationAdmin)
admin.site.register(Camera, CameraAdmin)
admin.site.register(CameraGroup, CameraGroupAdmin)
admin.site.register(SystemHealth, SystemHealthAdmin)
admin.site.register(Incident, IncidentAdmin)
admin.site.register(LocationAccess)
