{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}{{ title|default:'Add Camera' }} - ABC CCTV System{% endblock %}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4">{{ title|default:'Add Camera' }}</h2>
    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
        {% csrf_token %}
        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
        {% for field in form.visible_fields %}
            <div class="mb-3">
                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                {{ field }}
                {% if field.help_text %}
                    <div class="form-text">{{ field.help_text }}</div>
                {% endif %}
                {% for error in field.errors %}
                    <div class="invalid-feedback d-block">{{ error }}</div>
                {% endfor %}
            </div>
        {% endfor %}
        <button type="submit" class="btn btn-primary">Submit</button>
        <a href="{% url 'dashboard:cameras' %}" class="btn btn-secondary ms-2">Cancel</a>
    </form>
</div>
{% endblock %}
