#!/usr/bin/env python3
"""
Test script to verify camera access control for user 'deginet'.
Expected: Should only see Stream 1 (8IeKtyU00l) from kXWAe group.
"""

import requests
import json

def test_user_access(username, password, expected_desc):
    """Test camera access for a specific user"""
    print(f"Testing camera access for user: {username}")
    print(f"Expected: {expected_desc}")
    print("-" * 50)
    
    # Test both services
    services = [
        {
            'name': 'Django Web',
            'url': 'http://localhost:8000',
            'cameras_path': '/cameras/live/'
        },
        # Skip Shinobi CCTV Django for now since it has API timeout issues
        # {
        #     'name': 'Shinobi CCTV Django',
        #     'url': 'http://localhost:5000',
        #     'cameras_path': '/cameras/'
        # }
    ]
    
    for service in services:
        print(f"\nTesting {service['name']}")
        
        # Create session
        session = requests.Session()
        
        # Get login page
        if service['name'] == 'Django Web':
            login_url = f"{service['url']}/users/login/"
        else:
            login_url = f"{service['url']}/login/"
        try:
            response = session.get(login_url)
            
            if response.status_code != 200:
                print(f"Failed to get login page: {response.status_code}")
                continue

            # Extract CSRF token
            csrf_token = None
            for line in response.text.split('\n'):
                if 'csrfmiddlewaretoken' in line and 'value=' in line:
                    csrf_token = line.split('value="')[1].split('"')[0]
                    break

            if not csrf_token:
                print(f"Could not find CSRF token")
                continue
            
            # Login
            login_data = {
                'username': username,
                'password': password,
                'csrfmiddlewaretoken': csrf_token
            }
            
            response = session.post(login_url, data=login_data)
            
            if response.status_code not in [200, 302]:
                print(f"Login failed: {response.status_code}")
                continue
            
            # Test camera access
            cameras_url = f"{service['url']}{service['cameras_path']}"
            response = session.get(cameras_url)
            
            if response.status_code == 200:
                # Count cameras in response
                camera_count = response.text.count('camera-card')
                print(f"Access successful - Found {camera_count} cameras")

                # Check for specific monitor IDs
                has_stream1 = '8IeKtyU00l' in response.text
                has_stream2 = '15iBWQ1frP' in response.text

                print(f"   Stream 1 (8IeKtyU00l): {'Visible' if has_stream1 else 'Hidden'}")
                print(f"   Stream 2 (15iBWQ1frP): {'Visible' if has_stream2 else 'Hidden'}")

                # Verify expected behavior
                if has_stream1 and not has_stream2:
                    print(f"   CORRECT: {username} can only see Stream 1 (kXWAe group)")
                elif has_stream1 and has_stream2:
                    print(f"   RESULT: {username} can see both streams")
                elif not has_stream1 and not has_stream2:
                    print(f"   RESULT: {username} cannot see any streams")
                else:
                    print(f"   RESULT: {username} can only see Stream 2 (b22BI group)")
                
            else:
                print(f"Camera access failed: {response.status_code}")

        except Exception as e:
            print(f"Error testing {service['name']}: {str(e)}")

if __name__ == "__main__":
    # Test different users
    print("=== TESTING ROLE-BASED ACCESS CONTROL ===")
    print()

    # Test deginet (Operator - should see Stream 1 only)
    test_user_access('deginet', 'Dhaloota1977', 'Stream 1 only (kXWAe group)')
    print()

    # Test admin (Administrator - should see both streams)
    test_user_access('admin', 'admin123', 'Both streams (Administrator role)')
    print()

    print("=== TEST COMPLETE ===")
