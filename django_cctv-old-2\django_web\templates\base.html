{% load static %}
<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CCTV Monitoring System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#EBF5FF',
                            100: '#E1EFFE',
                            200: '#C3DDFD',
                            300: '#A4CAFE',
                            400: '#76A9FA',
                            500: '#3F83F8',
                            600: '#1C64F2',
                            700: '#1A56DB',
                            800: '#1E429F',
                            900: '#233876',
                        },
                        secondary: {
                            50: '#F0FDFA',
                            100: '#CCFBF1',
                            200: '#99F6E4',
                            300: '#5EEAD4',
                            400: '#2DD4BF',
                            500: '#14B8A6',
                            600: '#0D9488',
                            700: '#0F766E',
                            800: '#115E59',
                            900: '#134E4A',
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">
    
    {% block extra_head %}{% endblock %}
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="h-full bg-gray-50 dark:bg-gray-900">
    <div class="flex h-full">
        <!-- Sidebar -->
        <aside id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-md transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out">
            <div class="h-full flex flex-col">
                <!-- Logo -->
                <div class="px-4 py-6 flex items-center border-b border-gray-200 dark:border-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600 dark:text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="1"></circle>
                        <circle cx="12" cy="12" r="5"></circle>
                        <circle cx="12" cy="12" r="9"></circle>
                    </svg>
                    <span class="ml-3 text-xl font-semibold text-gray-800 dark:text-white">SecureView</span>
                </div>
                
                <!-- Navigation -->
                <nav class="flex-1 px-2 py-4 overflow-y-auto">
                    <a href="{% url 'dashboard:index' %}" class="flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'index' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        <span>Dashboard</span>
                    </a>
                    
                    <a href="{% url 'cameras:camera_grid' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.path == '/cameras/grid/' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <span>Live View</span>
                    </a>
                    
                    <a href="{% url 'cameras:event_list' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.path == '/cameras/events/' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>Events</span>
                    </a>
                    
                    <a href="{% url 'cameras:camera_list' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'camera_list' and request.resolver_match.app_name == 'cameras' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        </svg>
                        <span>Cameras</span>
                    </a>
                    <a href="{% url 'cameras:camera_create' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'camera_create' and request.resolver_match.app_name == 'cameras' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        <span>Add Camera</span>
                    </a>
                    {% if request.resolver_match.url_name == 'camera_detail' or request.resolver_match.url_name == 'camera_edit' or request.resolver_match.url_name == 'camera_delete' and request.resolver_match.app_name == 'cameras' %}
                    <div class="ml-4">
                        <a href="{% url 'cameras:camera_detail' camera.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'camera_detail' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>View Camera</span>
                        </a>
                        <a href="{% url 'cameras:camera_edit' camera.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'camera_edit' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>Edit Camera</span>
                        </a>
                        <a href="{% url 'cameras:camera_delete' camera.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'camera_delete' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>Delete Camera</span>
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if user.is_admin %}
                    <a href="{% url 'cameras:group_list' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'group_list' and request.resolver_match.app_name == 'cameras' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        <span>Camera Groups</span>
                    </a>
                    <a href="{% url 'cameras:group_create' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'group_create' and request.resolver_match.app_name == 'cameras' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        <span>Add Group</span>
                    </a>
                    {% if request.resolver_match.url_name == 'group_detail' or request.resolver_match.url_name == 'group_edit' or request.resolver_match.url_name == 'group_delete' and request.resolver_match.app_name == 'cameras' %}
                    <div class="ml-4">
                        <a href="{% url 'cameras:group_detail' group.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'group_detail' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>View Group</span>
                        </a>
                        <a href="{% url 'cameras:group_edit' group.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'group_edit' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>Edit Group</span>
                        </a>
                        <a href="{% url 'cameras:group_delete' group.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'group_delete' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>Delete Group</span>
                        </a>
                    </div>
                    {% endif %}
                    <a href="{% url 'vpn:client_list' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'client_list' and request.resolver_match.app_name == 'vpn' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>VPN Clients</span>
                    </a>
                    <a href="{% url 'vpn:client_create' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'client_create' and request.resolver_match.app_name == 'vpn' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        <span>Add VPN Client</span>
                    </a>
                    {% if request.resolver_match.url_name == 'client_detail' or request.resolver_match.url_name == 'client_edit' or request.resolver_match.url_name == 'client_delete' and request.resolver_match.app_name == 'vpn' %}
                    <div class="ml-4">
                        <a href="{% url 'vpn:client_detail' client.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'client_detail' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>View Client</span>
                        </a>
                        <a href="{% url 'vpn:client_edit' client.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'client_edit' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>Edit Client</span>
                        </a>
                        <a href="{% url 'vpn:client_delete' client.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'client_delete' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                            <span>Delete Client</span>
                        </a>
                        <a href="{% url 'vpn:client_download_config' client.id %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                            <span>Download Config</span>
                        </a>
                    </div>
                    {% endif %}
                    <a href="{% url 'vpn:vpn_status' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'vpn_status' and request.resolver_match.app_name == 'vpn' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2a4 4 0 014-4h2a4 4 0 014 4v2" />
                            <circle cx="9" cy="7" r="4" />
                        </svg>
                        <span>VPN Status</span>
                    </a>
                    <a href="{% url 'vpn:vpn_logs' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.resolver_match.url_name == 'vpn_logs' and request.resolver_match.app_name == 'vpn' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v4a1 1 0 001 1h3m10-5v4a1 1 0 01-1 1h-3m-6 4h6m2 0h2a2 2 0 002-2v-5a2 2 0 00-2-2h-2a2 2 0 00-2 2v5a2 2 0 002 2z" />
                        </svg>
                        <span>VPN Logs</span>
                    </a>
                    
                    <a href="{% url 'users:user_list' %}" class="flex items-center px-4 py-2 mt-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg {% if request.path == '/users/' %}bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-200{% endif %}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <span>Users</span>
                    </a>
                    {% endif %}
                </nav>
                
                <!-- User section -->
                <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="{% url 'users:profile' %}" class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            {% if user.profile_image %}
                                <img src="{{ user.profile_image.url }}" alt="{{ user.username }}" class="w-10 h-10 rounded-full">
                            {% else %}
                                <span class="text-lg font-semibold text-gray-700 dark:text-gray-300">{{ user.username|first|upper }}</span>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ user.get_full_name|default:user.username }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ user.get_role_display }}</p>
                        </div>
                    </a>
                    <div class="mt-3 flex space-x-2">
                        <a href="{% url 'users:profile' %}" class="px-2 py-1 text-xs rounded-md bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300">Profile</a>
                        <form method="post" action="{% url 'users:logout' %}" class="inline">
                            {% csrf_token %}
                            <button type="submit" class="px-2 py-1 text-xs rounded-md bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300">Logout</button>
                        </form>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-64">
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-40">
                <div class="px-4 sm:px-6 lg:px-8 py-4">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <button id="sidebar-toggle" class="lg:hidden mr-3 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                            <h1 class="text-2xl font-semibold text-gray-800 dark:text-white">{% block header_title %}Dashboard{% endblock %}</h1>
                        </div>
                        <div class="flex items-center space-x-4">
                            <!-- Notifications -->
                            <div class="relative">
                                <button id="notifications-btn" class="p-1 rounded-full text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                                    <span class="sr-only">View notifications</span>
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                    </svg>
                                    <span class="absolute top-0 right-0 h-3 w-3 rounded-full bg-red-500"></span>
                                </button>
                                
                                <!-- Notifications Panel (hidden by default) -->
                                <div id="notifications-panel" class="hidden absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50">
                                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Notifications</h3>
                                    </div>
                                    <div class="max-h-64 overflow-y-auto">
                                        <!-- Notification items will be populated dynamically -->
                                        <div class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0 pt-0.5">
                                                    <span class="status-indicator status-online"></span>
                                                </div>
                                                <div class="ml-3 w-0 flex-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Camera Front Door is now online</p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">2 minutes ago</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0 pt-0.5">
                                                    <span class="status-indicator status-offline"></span>
                                                </div>
                                                <div class="ml-3 w-0 flex-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Camera Parking Lot is offline</p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">15 minutes ago</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0 pt-0.5">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                    </svg>
                                                </div>
                                                <div class="ml-3 w-0 flex-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Motion detected at Back Door</p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">30 minutes ago</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
                                        <a href="#" class="text-xs text-blue-600 dark:text-blue-400 hover:underline">View all notifications</a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Dark Mode Toggle -->
                            <div class="flex items-center">
                                <span class="text-xs text-gray-600 dark:text-gray-400 mr-2">Dark</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="dark-mode-toggle" class="sr-only peer">
                                    <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="p-4 sm:p-6 lg:p-8">
                {% if messages %}
                    <div class="mb-6">
                        {% for message in messages %}
                            <div class="p-4 mb-3 rounded-md {% if message.tags == 'error' %}bg-red-50 text-red-700 dark:bg-red-900 dark:text-red-200{% elif message.tags == 'success' %}bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-200{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-200{% elif message.tags == 'info' %}bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200{% endif %} fade-in">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                
                {% block content %}{% endblock %}
            </main>
            
            <!-- Footer -->
            <footer class="bg-white dark:bg-gray-800 p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                <p>© {% now "Y" %} SecureView CCTV Monitoring System. All rights reserved.</p>
            </footer>
        </div>
    </div>
    
    <!-- Main JS -->
    <script src="{% static 'js/main.js' %}"></script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>