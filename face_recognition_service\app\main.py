"""
🎖️ FACE RECOGNITION MICROSERVICE - MAIN APPLICATION
🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 1
⚔️ TACTICAL DEPLOYMENT COMMAND CENTER
"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse

from app.core.config import settings
from app.core.database import init_database, check_database_health
from app.core.cache import cache_manager

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """🚀 Application lifespan management"""
    
    # 🎯 Startup
    logger.info("🎖️ Face Recognition Microservice starting up...")
    
    try:
        # Initialize database
        logger.info("🗄️ Initializing database...")
        await init_database()
        
        # Connect to Redis cache
        logger.info("🚀 Connecting to Redis cache...")
        await cache_manager.connect()
        
        # TODO: Load AI models
        logger.info("🧠 Loading AI models...")
        # await load_models()
        
        logger.info("✅ Face Recognition Microservice started successfully!")
        
    except Exception as e:
        logger.error(f"🚨 Startup failed: {e}")
        raise e
    
    yield
    
    # 🎯 Shutdown
    logger.info("🔄 Face Recognition Microservice shutting down...")
    
    try:
        # Disconnect from Redis
        await cache_manager.disconnect()
        logger.info("🔌 Redis disconnected")
        
        # TODO: Cleanup AI models
        # await cleanup_models()
        
        logger.info("✅ Face Recognition Microservice shutdown complete!")
        
    except Exception as e:
        logger.error(f"🚨 Shutdown error: {e}")

# 🎖️ Create FastAPI application
app = FastAPI(
    title="Face Recognition Microservice",
    description="🎭 Enterprise-grade Face Recognition System",
    version=settings.SERVICE_VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 🌐 CORS Configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 🎯 Health Check Endpoints
@app.get("/health")
async def health_check():
    """🩺 Basic health check"""
    return {
        "status": "healthy",
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "timestamp": time.time()
    }

@app.get("/health/detailed")
async def detailed_health_check():
    """🩺 Detailed health check"""
    
    # Check database
    db_healthy = await check_database_health()
    
    # Check cache
    cache_stats = await cache_manager.get_cache_stats()
    cache_healthy = cache_stats.get("connected", False)
    
    # Overall health
    overall_healthy = db_healthy and cache_healthy
    
    return {
        "status": "healthy" if overall_healthy else "unhealthy",
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "components": {
            "database": {
                "status": "healthy" if db_healthy else "unhealthy",
                "connected": db_healthy
            },
            "cache": {
                "status": "healthy" if cache_healthy else "unhealthy",
                "connected": cache_healthy,
                "stats": cache_stats
            }
        },
        "configuration": {
            "debug": settings.DEBUG,
            "gpu_enabled": settings.USE_GPU,
            "max_workers": settings.MAX_WORKERS
        }
    }

# 🎯 Service Information
@app.get("/info")
async def service_info():
    """ℹ️ Service information"""
    return {
        "service": settings.SERVICE_NAME,
        "version": settings.SERVICE_VERSION,
        "description": "Enterprise-grade Face Recognition Microservice",
        "features": [
            "YOLOv11 Face Detection",
            "ArcFace Feature Extraction", 
            "Real-time Recognition",
            "Redis Caching",
            "PostgreSQL Storage",
            "REST API",
            "WebSocket Streaming"
        ],
        "endpoints": {
            "health": "/health",
            "docs": "/docs",
            "api": "/api/v1"
        }
    }

# 🚨 Global Exception Handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """🚨 Global exception handler"""
    logger.error(f"🚨 Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "service": settings.SERVICE_NAME
        }
    )

# 🎯 API Routes - Phase 2 Integration (FACE RECOGNITION FOCUS)
from app.api import detection, recognition, management

app.include_router(detection.router, prefix="/api/v1", tags=["detection"])
app.include_router(recognition.router, prefix="/api/v1", tags=["recognition"])
app.include_router(management.router, prefix="/api/v1", tags=["management"])

# 🎯 NOTE: Object detection will be a separate service in the future
# object_detection.py is ready for future integration as standalone service

# 🎖️ Root endpoint
@app.get("/")
async def root():
    """🏠 Root endpoint"""
    return {
        "message": "🎖️ Face Recognition Microservice",
        "status": "operational",
        "version": settings.SERVICE_VERSION,
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"🚀 Starting {settings.SERVICE_NAME} v{settings.SERVICE_VERSION}")
    logger.info(f"🌐 Server: {settings.SERVICE_HOST}:{settings.SERVICE_PORT}")
    logger.info(f"🎯 Debug mode: {settings.DEBUG}")
    
    uvicorn.run(
        "app.main:app",
        host=settings.SERVICE_HOST,
        port=settings.SERVICE_PORT,
        reload=settings.DEBUG,
        workers=1 if settings.DEBUG else settings.MAX_WORKERS
    )
