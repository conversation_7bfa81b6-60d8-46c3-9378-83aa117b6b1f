body {
  display: flex;
  flex-direction: column;
}
.content {
  flex: 1;
  background: url("../images/bgg.jpg") no-repeat center center;
  background-size: cover;
  color: rgb(0, 0, 0);
  width: 100%;
  min-height: 90vh;
  display: flex;
  align-items: center; /* Vertically center content */
  justify-content: center; /* Horizontally center content */
  text-align: center; /* Center the text */
}
.hero {
  color: white;
  padding: 15px 0;
}

.navbar {
  padding: 0.8rem 0;
}
.navbar-brand {
  display: flex;
  align-items: center;
  gap: 10px; /* Add space between the logo and text */
}

.navbar-brand img {
  height: 40px; /* Adjust logo size */
  object-fit: contain;
}

.sitename {
  font-size: 1.2rem; /* Match text size with logo */
  margin: 0;
  color: #353535;
}

@media (max-width: 768px) {
  .navbar-brand img {
    height: 35px; /* Smaller size on mobile */
  }

  .sitename {
    font-size: 1.5rem; /* Adjust text size on mobile */
  }
}

.navbar-nav .nav-item {
  margin: 0 10px;
}

.navbar-nav .nav-link {
  color: #353535 !important;
  font-weight: 500;
  transition: color 0.3s ease-in-out;
}

.navbar-nav .nav-link:hover {
  color: #ffdd57 !important;
}

.navbar-toggler {
  border: none;
}

.navbar-toggler:focus {
  box-shadow: none;
}

.navbar-toggler-icon {
  filter: invert(1); /* Makes the icon white */
}

.content h1 {
  font-size: 3rem;
  font-weight: bold;
  color: #333;
}

.content p {
  font-size: 1.2rem;
  color: #555;
}
footer {
  background: #343a40;
  color: white;
  text-align: center;
  padding: 15px;
}

/* Styles from index.html */
.camera-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 1rem;
  padding: 1rem;
}
.camera-card {
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}
.video-wrapper {
  margin-top: 10px; /* Ensure the video feed isn't covered by the dropdown */
  position: relative;
  background-color: #000;
  width: 100%;
  height: 100%;  /* Adjust as needed */
  aspect-ratio: 16/9;
}
.video-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}
.video-updating {
  opacity: 0.5;
}
.camera-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 0.5rem;
  font-size: 0.9rem;
}
.status-indicator {
  width: 10px;
  height: 10px;
  background: gray;
  border-radius: 50%;
  display: inline-block;
  transition: background-color 0.3s ease;
}
/* Camera Controls Wrapper */
.camera-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7); /* Change this to be more transparent */
  color: white;
  font-size: 0.9rem;
}
/* Left section (Status + Camera Name) */
.camera-info {
  display: flex;
  align-items: center;
  gap: 8px; /* Space between indicator and text */
}
/* Right section (Counter & Detection Toggles) */
.toggle-group {
  display: flex;
  align-items: center;
  gap: 15px; /* Space between toggles and labels */
}
/* Toggle Switch Styling */
.switch {
  position: relative;
  display: inline-block;
  width: 34px;
  height: 20px;
}
.switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}
.slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}
input:checked + .slider {
  background-color: #0f0;
}
input:checked + .slider:before {
  transform: translateX(14px);
}
/* Style for the label selector dropdown */
.label-selector {
  position: absolute; /* Position it relative to the camera card */
  top: 0px;          /* Adjust this value to set it at the top */
  left: 0px;         /* Adjust this value to set it at the left */
  z-index: 10;        /* Ensure it's above the video wrapper */
  background-color: rgba(0, 0, 0, 0.3);  /* Optional: for contrast */
  padding: 5px;
  border-radius: 5px;
  width: 100%;  /* Adjust width as needed */
}
.label-dropdown {
  background-color: rgba(0, 0, 0, 0.3); /* Transparent background */
  color: white; /* Text color */
  border: 1px solid rgba(0, 0, 0, 0.7); /* Light border */
}
.label-dropdown option {
  background-color: rgba(50, 50, 50, 0.3); /* Semi-transparent background */
  color: white; /* Keep text visible */
}
.label-dropdown option:checked {
  background-color: rgba(0, 0, 0, 0.3) !important; /* Transparent orange */
  color: white;
}
@keyframes fadeInBounce {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  50% {
    opacity: 0.5;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
/* Moving & Fading Text */
.animated-text {
  position: fixed; /* Fixed so it moves separately */
  top: 270px; /* Adjust position */
  left: 50%;
  transform: translateX(-50%);
  font-size: 3rem;
  font-weight: bold;
  color: #000000;
  transition: transform 0.1s ease-out;
  animation: fadeEffect 3s infinite alternate; /* Fading effect */
}
/* Keyframes for Fade In and Out */
@keyframes fadeEffect {
  0% { opacity: 0.2; }  /* Start with low visibility */
  50% { opacity: 1; }  /* Fully visible */
  100% { opacity: 0.2; } /* Fade out again */
}