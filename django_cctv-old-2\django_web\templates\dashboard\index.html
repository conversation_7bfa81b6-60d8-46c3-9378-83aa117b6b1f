{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - CCTV Monitoring System{% endblock %}

{% block header_title %}Dashboard{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- System Status Overview -->
    <div id="system-status-panel" class="mb-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">System Status</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Camera Status -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 stat-card">
                <div class="flex items-center">
                    <div class="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500 dark:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Cameras</p>
                        <p id="total-cameras" class="text-2xl font-semibold text-gray-800 dark:text-white">{{ total_cameras }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Online Cameras -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 stat-card">
                <div class="flex items-center">
                    <div class="bg-green-100 dark:bg-green-900 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500 dark:text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Online Cameras</p>
                        <p id="online-cameras" class="text-2xl font-semibold text-green-600 dark:text-green-400">{{ online_cameras }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Offline Cameras -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 stat-card">
                <div class="flex items-center">
                    <div class="bg-red-100 dark:bg-red-900 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500 dark:text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Offline Cameras</p>
                        <p id="offline-cameras" class="text-2xl font-semibold text-red-600 dark:text-red-400">{{ offline_cameras }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Services Status -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 stat-card">
                <div class="flex flex-col h-full justify-between">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Services Status</p>
                    
                    <div class="flex items-center mb-2">
                        <span id="vpn-status-indicator" class="status-indicator {% if openvpn_status %}status-online{% else %}status-offline{% endif %}"></span>
                        <span class="text-gray-700 dark:text-gray-300">OpenVPN Server</span>
                    </div>
                    
                    <div class="flex items-center">
                        <span id="shinobi-status-indicator" class="status-indicator {% if shinobi_status %}status-online{% else %}status-offline{% endif %}"></span>
                        <span class="text-gray-700 dark:text-gray-300">Shinobi NVR</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Events -->
    <div class="mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Recent Events</h2>
            <a href="{% url 'cameras:event_list' %}" class="text-sm text-blue-600 dark:text-blue-400 hover:underline">View all events</a>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Camera</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Event</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {% for event in recent_events %}
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <span class="status-indicator {% if event.camera.status == 'online' %}status-online{% elif event.camera.status == 'offline' %}status-offline{% elif event.camera.status == 'disabled' %}status-disabled{% else %}status-maintenance{% endif %}"></span>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ event.camera.name }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-700 dark:text-gray-300">{{ event.get_event_type_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ event.timestamp|date:"M d, Y H:i:s" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                {% if event.snapshot_url %}
                                <a href="{{ event.snapshot_url }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">View Snapshot</a>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">No recent events</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Quick Access to Cameras -->
    <div>
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Quick Access</h2>
            <a href="{% url 'cameras:camera_list' %}" class="text-sm text-blue-600 dark:text-blue-400 hover:underline">View all cameras</a>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for camera in cameras %}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="aspect-video bg-black relative">
                    <!-- Live Camera Preview -->
                    {% if camera.hls_url %}
                    <!-- Live HLS Preview (muted, no controls for dashboard) -->
                    <video id="preview-{{ camera.id }}"
                           width="100%"
                           height="100%"
                           muted
                           autoplay
                           loop
                           poster="{{ camera.preview_url }}"
                           class="w-full h-full object-cover">
                        <source src="{{ camera.hls_url }}" type="application/x-mpegURL">
                    </video>
                    {% elif camera.preview_url %}
                    <!-- Static preview image -->
                    <img src="{{ camera.preview_url }}" alt="{{ camera.name }}" class="w-full h-full object-cover">
                    {% else %}
                    <!-- Fallback placeholder -->
                    <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </div>
                    {% endif %}

                    <!-- Simple Status Badge (replicated from shinobi_cctv_django) -->
                    <span class="camera-status status-{{ camera.status }} position-absolute top-0 end-0 m-2 px-2 py-1 rounded fw-bold {% if camera.status == 'online' %}bg-success text-light{% else %}bg-danger text-light{% endif %}" data-status-indicator="{{ camera.id }}">
                        {{ camera.status|title }}
                    </span>


                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent text-white p-3">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <span>{{ camera.name }}</span>
                            </div>
                            <span class="text-xs opacity-75">{{ camera.location }}</span>
                        </div>
                    </div>
                </div>
                <div class="p-4 flex justify-between">
                    <a href="{% url 'cameras:camera_detail' camera_id=camera.id %}" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">Details</a>
                    <a href="{% url 'cameras:camera_grid' %}?cameras={{ camera.id }}&layout=1x1" class="text-blue-600 dark:text-blue-400 hover:underline text-sm">View Live</a>
                </div>
            </div>
            {% empty %}
            <div class="col-span-full bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center">
                <p class="text-gray-500 dark:text-gray-400">No cameras available</p>
                {% if user.is_admin %}
                <a href="{% url 'cameras:camera_create' %}" class="mt-2 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Add Camera</a>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Status Badge Styles (replicated from shinobi_cctv_django) */
    .camera-status {
        padding: 4px 8px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        z-index: 10;
    }

    .status-online {
        background-color: #28a745 !important; /* Bootstrap success color */
        color: white !important;
    }

    .status-offline {
        background-color: #dc3545 !important; /* Bootstrap danger color */
        color: white !important;
    }

    .status-unknown {
        background-color: #ffc107 !important; /* Bootstrap warning color */
        color: black !important;
    }
</style>
{% endblock %}

{% block extra_scripts %}
<!-- HLS.js for video streaming -->
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Simple HLS setup for dashboard preview videos (replicated from shinobi_cctv_django)
        const videos = document.querySelectorAll('video[id^="preview-"]');
        videos.forEach(video => {
            const source = video.querySelector('source');
            if (!source) return;

            const hlsUrl = source.src;
            if (Hls.isSupported()) {
                const hls = new Hls();
                hls.loadSource(hlsUrl);
                hls.attachMedia(video);
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = hlsUrl;
            }
        });
    });


</script>
{% endblock %}