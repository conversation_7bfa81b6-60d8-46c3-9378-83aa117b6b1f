#!/usr/bin/env python3
"""
🎖️ CAMERA VIEW FIX VERIFICATION SCRIPT
Test script to verify that the camera viewing fix is working correctly
"""

import requests
import json
import sys
import time
from urllib.parse import urljoin

# Service URLs
DJANGO_WEB_URL = "http://localhost:8000"
SHINOBI_CCTV_URL = "http://localhost:5000"
SHINOBI_NVR_URL = "http://localhost:8080"

def test_service_health(service_name, url):
    """Test if a service is responding"""
    print(f"\n🔍 Testing {service_name} health...")
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print(f"✅ {service_name} is responding (Status: {response.status_code})")
            return True
        else:
            print(f"⚠️ {service_name} responded with status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ {service_name} is not responding: {str(e)}")
        return False

def test_django_web_cameras():
    """Test Django Web camera views"""
    print(f"\n🎥 Testing Django Web Camera Views...")
    
    # Test Live Monitors endpoint
    try:
        live_monitors_url = urljoin(DJANGO_WEB_URL, "/cameras/live/")
        print(f"Testing: {live_monitors_url}")
        
        response = requests.get(live_monitors_url, timeout=15)
        if response.status_code == 200:
            print("✅ Live Monitors endpoint is accessible")
            
            # Check if the response contains camera data
            content = response.text
            if "camera-card" in content or "monitors" in content:
                print("✅ Live Monitors page contains camera elements")
            else:
                print("⚠️ Live Monitors page loaded but no camera elements found")
                
        else:
            print(f"❌ Live Monitors endpoint failed (Status: {response.status_code})")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Django Web Live Monitors test failed: {str(e)}")

def test_shinobi_cctv_cameras():
    """Test Shinobi CCTV Django camera views"""
    print(f"\n🎥 Testing Shinobi CCTV Camera Views...")
    
    # Test Cameras endpoint
    try:
        cameras_url = urljoin(SHINOBI_CCTV_URL, "/cameras/")
        print(f"Testing: {cameras_url}")
        
        response = requests.get(cameras_url, timeout=15)
        if response.status_code == 200:
            print("✅ Cameras endpoint is accessible")
            
            # Check if the response contains camera data
            content = response.text
            if "camera" in content.lower() or "monitor" in content.lower():
                print("✅ Cameras page contains camera elements")
            else:
                print("⚠️ Cameras page loaded but no camera elements found")
                
        else:
            print(f"❌ Cameras endpoint failed (Status: {response.status_code})")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Shinobi CCTV Cameras test failed: {str(e)}")

def test_shinobi_nvr():
    """Test Shinobi NVR accessibility"""
    print(f"\n🎬 Testing Shinobi NVR...")
    
    try:
        response = requests.get(SHINOBI_NVR_URL, timeout=10)
        if response.status_code == 200:
            print("✅ Shinobi NVR is accessible")
            return True
        else:
            print(f"⚠️ Shinobi NVR responded with status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Shinobi NVR is not accessible: {str(e)}")
        return False

def test_camera_urls():
    """Test if camera stream URLs are properly formatted"""
    print(f"\n🔗 Testing Camera URL Generation...")
    
    # Test HLS URL format
    test_api_key = "test_api_key"
    test_group_key = "test_group_key"
    test_monitor_id = "test_monitor_id"
    
    expected_hls_url = f"http://localhost:8080/{test_api_key}/hls/{test_group_key}/{test_monitor_id}/s.m3u8"
    expected_preview_url = f"http://localhost:8080/{test_api_key}/jpeg/{test_group_key}/{test_monitor_id}/s.jpg"
    
    print(f"✅ Expected HLS URL format: {expected_hls_url}")
    print(f"✅ Expected Preview URL format: {expected_preview_url}")
    
    return True

def test_camera_counts():
    """Test camera count display in Shinobi CCTV Django dashboard"""
    print(f"\n📊 Testing Camera Count Display...")

    try:
        dashboard_url = urljoin(SHINOBI_CCTV_URL, "/")
        print(f"Testing: {dashboard_url}")

        response = requests.get(dashboard_url, timeout=15)
        if response.status_code == 200:
            print("✅ Dashboard endpoint is accessible")

            # Check if the response contains camera count elements
            content = response.text
            if "Cameras" in content and ("0" in content or "2" in content):
                print("✅ Dashboard contains camera count elements")
                if "2" in content:
                    print("✅ Camera count appears to be updated (found '2')")
                else:
                    print("⚠️ Camera count might still show 0 - check after login")
            else:
                print("⚠️ Dashboard loaded but no camera count elements found")

        else:
            print(f"❌ Dashboard endpoint failed (Status: {response.status_code})")

    except requests.exceptions.RequestException as e:
        print(f"❌ Dashboard camera count test failed: {str(e)}")

def main():
    """Main test function"""
    print("🎖️ CAMERA VIEW & NAVIGATION FIX VERIFICATION")
    print("=" * 60)

    # Test service health
    django_web_healthy = test_service_health("Django Web", DJANGO_WEB_URL)
    shinobi_cctv_healthy = test_service_health("Shinobi CCTV Django", SHINOBI_CCTV_URL)
    shinobi_nvr_healthy = test_shinobi_nvr()

    # Test camera views if services are healthy
    if django_web_healthy:
        test_django_web_cameras()

    if shinobi_cctv_healthy:
        test_shinobi_cctv_cameras()
        test_camera_counts()

    # Test URL generation
    test_camera_urls()

    # Summary
    print(f"\n🎖️ TEST SUMMARY")
    print("=" * 30)
    print(f"Django Web Service: {'✅ Healthy' if django_web_healthy else '❌ Unhealthy'}")
    print(f"Shinobi CCTV Service: {'✅ Healthy' if shinobi_cctv_healthy else '❌ Unhealthy'}")
    print(f"Shinobi NVR Service: {'✅ Healthy' if shinobi_nvr_healthy else '❌ Unhealthy'}")

    if django_web_healthy and shinobi_cctv_healthy:
        print("\n🏆 CAMERA VIEW & NAVIGATION FIX VERIFICATION COMPLETE!")
        print("🎥 Both services are responding. Manual testing required:")
        print(f"   - Django Web Dashboard: {DJANGO_WEB_URL}/")
        print(f"   - Django Web Live Monitors: {DJANGO_WEB_URL}/cameras/live/")
        print(f"   - Shinobi CCTV Dashboard: {SHINOBI_CCTV_URL}/")
        print(f"   - Shinobi CCTV Cameras: {SHINOBI_CCTV_URL}/cameras/")
        print("\n🔍 Test sidebar navigation in both services:")
        print("   - Click sidebar links to verify no logout occurs")
        print("   - Check camera counts in Shinobi CCTV dashboard")
    else:
        print("\n⚠️ Some services are not responding. Please check Docker containers:")
        print("   docker-compose ps")
        print("   docker-compose logs web")
        print("   docker-compose logs shinobi_cctv_django")

    return 0

if __name__ == "__main__":
    sys.exit(main())
