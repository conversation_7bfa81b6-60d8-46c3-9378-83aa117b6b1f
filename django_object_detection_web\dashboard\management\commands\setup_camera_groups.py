"""
Management command to set up Camera Groups for testing access control in Shinobi CCTV Django
"""
from django.core.management.base import BaseCommand
from dashboard.models import CustomUser, Camera, CameraGroup, Location

class Command(BaseCommand):
    help = 'Set up Camera Groups and assign users for testing access control in Shinobi CCTV Django'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-groups',
            action='store_true',
            help='Create sample camera groups',
        )
        parser.add_argument(
            '--assign-cameras',
            action='store_true',
            help='Assign cameras to groups',
        )
        parser.add_argument(
            '--assign-users',
            action='store_true',
            help='Assign users to camera groups',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Run all setup steps',
        )

    def handle(self, *args, **options):
        if options['all']:
            options['create_groups'] = True
            options['assign_cameras'] = True
            options['assign_users'] = True

        if options['create_groups']:
            self.create_camera_groups()

        if options['assign_cameras']:
            self.assign_cameras_to_groups()

        if options['assign_users']:
            self.assign_users_to_groups()

        self.stdout.write(
            self.style.SUCCESS('Camera Groups setup completed successfully!')
        )

    def create_camera_groups(self):
        """Create sample camera groups"""
        groups_data = [
            {
                'name': 'Warehouse A',
                'description': 'Cameras for Warehouse A - Loading dock and storage areas'
            },
            {
                'name': 'Warehouse B', 
                'description': 'Cameras for Warehouse B - Processing and shipping areas'
            },
            {
                'name': 'Office Areas',
                'description': 'Cameras for office and administrative areas'
            },
            {
                'name': 'Perimeter Security',
                'description': 'External perimeter and parking lot cameras'
            },
            {
                'name': 'All Access',
                'description': 'Full access to all cameras (for supervisors)'
            }
        ]

        for group_data in groups_data:
            group, created = CameraGroup.objects.get_or_create(
                name=group_data['name'],
                defaults={'description': group_data['description']}
            )
            if created:
                self.stdout.write(f"Created camera group: {group.name}")
            else:
                self.stdout.write(f"Camera group already exists: {group.name}")

    def assign_cameras_to_groups(self):
        """Assign existing cameras to groups based on their names/locations"""
        cameras = Camera.objects.all()
        
        if not cameras.exists():
            self.stdout.write(
                self.style.WARNING('No cameras found. Please add cameras first.')
            )
            return

        # Get groups
        warehouse_a = CameraGroup.objects.get_or_create(name='Warehouse A')[0]
        warehouse_b = CameraGroup.objects.get_or_create(name='Warehouse B')[0]
        office_areas = CameraGroup.objects.get_or_create(name='Office Areas')[0]
        perimeter = CameraGroup.objects.get_or_create(name='Perimeter Security')[0]
        all_access = CameraGroup.objects.get_or_create(name='All Access')[0]

        for camera in cameras:
            # Clear existing groups
            camera.groups.clear()
            
            # Assign based on camera name/location
            location_name = camera.location.name.lower() if camera.location else ''
            camera_name = camera.name.lower()
            
            if 'warehouse a' in location_name or 'dock' in camera_name:
                camera.groups.add(warehouse_a)
                self.stdout.write(f"Assigned {camera.name} to Warehouse A")
            elif 'warehouse b' in location_name or 'shipping' in camera_name:
                camera.groups.add(warehouse_b)
                self.stdout.write(f"Assigned {camera.name} to Warehouse B")
            elif 'office' in location_name or 'admin' in camera_name:
                camera.groups.add(office_areas)
                self.stdout.write(f"Assigned {camera.name} to Office Areas")
            elif 'perimeter' in location_name or 'parking' in camera_name or 'entrance' in camera_name:
                camera.groups.add(perimeter)
                self.stdout.write(f"Assigned {camera.name} to Perimeter Security")
            else:
                # Default assignment to Warehouse A
                camera.groups.add(warehouse_a)
                self.stdout.write(f"Assigned {camera.name} to Warehouse A (default)")
            
            # All cameras also go to "All Access" group
            camera.groups.add(all_access)

    def assign_users_to_groups(self):
        """Assign users to camera groups based on their roles"""
        users = CustomUser.objects.filter(is_superuser=False)
        
        if not users.exists():
            self.stdout.write(
                self.style.WARNING('No non-admin users found. Please create users first.')
            )
            return

        # Get groups
        warehouse_a = CameraGroup.objects.get_or_create(name='Warehouse A')[0]
        warehouse_b = CameraGroup.objects.get_or_create(name='Warehouse B')[0]
        office_areas = CameraGroup.objects.get_or_create(name='Office Areas')[0]
        perimeter = CameraGroup.objects.get_or_create(name='Perimeter Security')[0]
        all_access = CameraGroup.objects.get_or_create(name='All Access')[0]

        for user in users:
            # Clear existing camera groups
            user.camera_groups.clear()
            
            role_name = user.role.name if user.role else 'Viewer'
            
            if role_name == 'Administrator':
                # Admins get all access
                user.camera_groups.add(all_access)
                self.stdout.write(f"Assigned {user.username} (Admin) to All Access")
            elif role_name in ['Warehouse Manager', 'Security Operator']:
                # Managers/Operators get access to multiple areas
                user.camera_groups.add(warehouse_a, warehouse_b, perimeter)
                self.stdout.write(f"Assigned {user.username} ({role_name}) to Warehouse A, B, and Perimeter")
            else:  # Viewer or other roles
                # Viewers get limited access based on username
                username_lower = user.username.lower()
                if 'warehouse_a' in username_lower or 'dock' in username_lower:
                    user.camera_groups.add(warehouse_a)
                    self.stdout.write(f"Assigned {user.username} (Viewer) to Warehouse A")
                elif 'warehouse_b' in username_lower or 'shipping' in username_lower:
                    user.camera_groups.add(warehouse_b)
                    self.stdout.write(f"Assigned {user.username} (Viewer) to Warehouse B")
                elif 'office' in username_lower:
                    user.camera_groups.add(office_areas)
                    self.stdout.write(f"Assigned {user.username} (Viewer) to Office Areas")
                else:
                    # Default: give access to one warehouse
                    user.camera_groups.add(warehouse_a)
                    self.stdout.write(f"Assigned {user.username} (Viewer) to Warehouse A (default)")

        self.stdout.write(
            self.style.SUCCESS(f'Assigned {users.count()} users to camera groups')
        )
