# Generated by Django 5.0.3 on 2025-06-04 18:15

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Camera',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('location', models.CharField(max_length=200)),
                ('type', models.CharField(choices=[('ip', 'IP Camera'), ('rtsp', 'RTSP Stream'), ('http', 'HTTP Stream'), ('hls', 'HLS Stream')], default='ip', max_length=20)),
                ('stream_url', models.CharField(max_length=255)),
                ('username', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('password', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('port', models.IntegerField(default=80)),
                ('status', models.CharField(choices=[('online', 'Online'), ('offline', 'Offline'), ('disabled', 'Disabled'), ('maintenance', 'Maintenance')], default='offline', max_length=20)),
                ('last_online', models.DateTimeField(blank=True, null=True)),
                ('shinobi_monitor_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CameraGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CameraEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('motion', 'Motion Detected'), ('object', 'Object Detected'), ('face', 'Face Detected'), ('custom', 'Custom Event'), ('offline', 'Camera Offline'), ('online', 'Camera Online')], default='motion', max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('snapshot_url', models.URLField(blank=True, null=True)),
                ('details', models.JSONField(blank=True, null=True)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='cameras.camera')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddField(
            model_name='camera',
            name='groups',
            field=models.ManyToManyField(blank=True, related_name='cameras', to='cameras.cameragroup'),
        ),
        migrations.CreateModel(
            name='CameraPreset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('position_data', models.JSONField()),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='presets', to='cameras.camera')),
            ],
            options={
                'ordering': ['camera', 'name'],
                'unique_together': {('camera', 'name')},
            },
        ),
    ]
