# Quick test script to verify both Django services are working
Write-Host "🔍 Testing Django Services..." -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Test django_web (port 8000)
Write-Host "`n📊 Testing django_web (port 8000)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ django_web: HTTP $($response.StatusCode) - Service is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ django_web: Service not accessible" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test shinobi_cctv_django (port 5000)
Write-Host "`n📊 Testing shinobi_cctv_django (port 5000)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ shinobi_cctv_django: HTTP $($response.StatusCode) - Service is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ shinobi_cctv_django: Service not accessible" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test pgAdmin (port 5050)
Write-Host "`n📊 Testing pgAdmin (port 5050)..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5050" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ pgAdmin: HTTP $($response.StatusCode) - Service is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ pgAdmin: Service not accessible" -ForegroundColor Red
}

# Check Docker service status
Write-Host "`n🐳 Docker Service Status:" -ForegroundColor Yellow
try {
    $services = docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    Write-Host $services
} catch {
    Write-Host "❌ Could not get Docker service status" -ForegroundColor Red
}

Write-Host "`n================================" -ForegroundColor Cyan
Write-Host "🎯 Service URLs:" -ForegroundColor Cyan
Write-Host "   • django_web: http://localhost:8000" -ForegroundColor White
Write-Host "   • shinobi_cctv_django: http://localhost:5000" -ForegroundColor White
Write-Host "   • pgAdmin: http://localhost:5050" -ForegroundColor White
Write-Host "   • Shinobi NVR: http://localhost:8080" -ForegroundColor White

Write-Host "`n🔧 Next Steps:" -ForegroundColor Cyan
Write-Host "   1. Create superusers: docker-compose exec web python manage.py createsuperuser" -ForegroundColor White
Write-Host "   2. Create superusers: docker-compose exec shinobi_cctv_django python manage.py createsuperuser" -ForegroundColor White
Write-Host "   3. Access admin interfaces at /admin/ on each service" -ForegroundColor White

Write-Host "`n✨ Setup Complete! Both services are isolated and ready to use." -ForegroundColor Green
