#!/usr/bin/env python3
"""
Test script to verify the updated URLs are browser-accessible
"""

import os
import sys
import django

# Add Django project to path
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cctv_project.settings')

django.setup()

from cameras.models import Camera

def test_camera_urls():
    """Test that camera URLs are browser-accessible"""
    
    print("🔍 Testing Camera URLs")
    print("=" * 50)
    
    cameras = Camera.objects.all()
    
    for camera in cameras:
        print(f"\n🎥 Camera: {camera.name}")
        print(f"   Monitor ID: {camera.shinobi_monitor_id}")
        print(f"   Live Stream URL: {camera.live_stream_url}")
        print(f"   Embed URL: {camera.shinobi_embed_url}")
        print(f"   Embed URL with API: {camera.shinobi_embed_url_with_api}")
        print(f"   Thumbnail URL: {camera.thumbnail_url}")
        
        # Check if URLs use localhost instead of shinobi-nvr
        if camera.shinobi_embed_url_with_api:
            if "localhost:8080" in camera.shinobi_embed_url_with_api:
                print("   ✅ URL uses localhost (browser-accessible)")
            else:
                print("   ❌ URL still uses internal hostname")
        else:
            print("   ⚠️  No embed URL available")
    
    print("\n" + "=" * 50)
    print("🎉 URL test completed!")

if __name__ == "__main__":
    test_camera_urls()
