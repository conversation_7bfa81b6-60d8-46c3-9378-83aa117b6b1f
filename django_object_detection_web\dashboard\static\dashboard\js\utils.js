// dashboard/static/dashboard/js/utils.js

/**
 * Retrieves the CSRF token from a meta tag or a global variable.
 * @returns {string|null} The CSRF token or null if not found.
 */
function getCSRFToken() {
    let csrfToken = null;
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    if (metaTag) {
        csrfToken = metaTag.getAttribute('content');
    } else if (window.CSRF_TOKEN) {
        csrfToken = window.CSRF_TOKEN;
    } else {
        // Fallback: Try to find it in a form if available (less reliable for general use)
        const input = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (input) {
            csrfToken = input.value;
        }
    }
    // if (!csrfToken) {
    //     console.warn('CSRF token not found. POST requests may fail.');
    // }
    return csrfToken;
}

/**
 * Shows a Bootstrap toast-like notification.
 * @param {string} message The message to display.
 * @param {string} type The type of notification ('success', 'danger', 'warning', 'info'). Default is 'info'.
 */
function showNotification(message, type = 'info') {
    const notificationsContainer = document.getElementById('notifications-container');
    if (!notificationsContainer) {
        console.warn("Notifications container with ID 'notifications-container' not found. Appending to body.");
        // As a fallback, append to body, but it won't be styled as a fixed toast container.
    }
    const container = notificationsContainer || document.body;

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.setAttribute('role', 'alert');
    // If it's inside notifications-container, it will likely be styled as a toast
    if (notificationsContainer) {
        alertDiv.classList.add('notification-toast'); // Ensure this class is styled for toast appearance
    }

    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    container.appendChild(alertDiv);

    // Initialize Bootstrap alert for dismissal
    const bsAlert = new bootstrap.Alert(alertDiv);

    setTimeout(() => {
        bsAlert.close();
    }, 5000);
}