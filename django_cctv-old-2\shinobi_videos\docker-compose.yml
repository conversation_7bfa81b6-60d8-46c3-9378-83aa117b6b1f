services:
  # Django Web Application
  django_app:
    build:
      context: ./django_app
      dockerfile: Dockerfile
    container_name: django_web_app
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn --bind 0.0.0.0:8000 core.wsgi"
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./django_app:/app
    env_file:
      - .env
    depends_on:
      - db
      - redis
    networks:
      - cctv_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # PostgreSQL Database for Django
  db:
    image: postgres:13
    container_name: postgres_db_django
    volumes:
      - postgres_data_django:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=warehouse
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=admin
    networks:
      - cctv_net
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d warehouse"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis for caching
  redis:
    image: redis:6.2
    container_name: redis_cache
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - cctv_net
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # Nginx as reverse proxy
  nginx:
    image: nginx:1.21
    container_name: nginx_proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./certs:/etc/letsencrypt
    depends_on:
      - django_app
    networks:
      - cctv_net
    restart: unless-stopped

  # DuckDNS for dynamic DNS
  duckdns:
    image: linuxserver/duckdns:latest
    container_name: duckdns
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Istanbul
      - SUBDOMAINS=abinetalemuvpn
      - TOKEN=your_duckdns_token
      - LOG_FILE=false
    volumes:
      - duckdns_config:/config
    restart: unless-stopped
    networks:
      - cctv_net

  # OpenVPN Server
  openvpn:
    build:
      context: ./openvpn
      dockerfile: Dockerfile
    container_name: openvpn
    cap_add:
      - NET_ADMIN
      - NET_RAW
      - SYS_ADMIN
    security_opt:
      - label:disable
    sysctls:
      - net.ipv4.ip_forward=1
      - net.ipv6.conf.all.forwarding=1
      - net.ipv6.conf.default.forwarding=1
    ports:
      - "1194:1194/udp"
    volumes:
      - ./openvpn/config:/etc/openvpn
      - /dev/net/tun:/dev/net/tun
      - ./openvpn/logs:/var/log/openvpn
    environment:
      - EASYRSA_KEY_SIZE=2048
      - EASYRSA_CA_EXPIRE=3650
      - EASYRSA_CERT_EXPIRE=3650
      - EASYRSA_CRL_DAYS=3650
      - EASYRSA_REQ_CN=abinetalemuvpn.duckdns.org
      - EASYRSA_REQ_COUNTRY=US
      - EASYRSA_REQ_PROVINCE=California
      - EASYRSA_REQ_CITY=SanFrancisco
      - EASYRSA_REQ_ORG=ShinobiDjango
      - EASYRSA_REQ_EMAIL=<EMAIL>
      - EASYRSA_REQ_OU=IT
    networks:
      - cctv_net
    restart: unless-stopped
    depends_on:
      - duckdns

  # OpenVPN Web UI
  openvpn-ui:
    image: nginx:alpine
    container_name: openvpn-ui
    ports:
      - "8082:80"
    volumes:
      - ./openvpn/config:/etc/openvpn
      - ./openvpn/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - openvpn
    networks:
      - cctv_net

  # Shinobi NVR
  shinobi-nvr:
    image: shinobisystems/shinobi
    container_name: shinobi-nvr
    environment:
      - TZ=Europe/Istanbul
      - PUID=1000
      - PGID=1000
      - DB_HOST=shinobi_db
      - DB_USER=shinobi
      - DB_PASSWORD=shinobi
      - DB_DATABASE=ccio
    volumes:
      - ./shinobi/config:/config
      - ./shinobi/customAutoLoad:/home/<USER>/libs/customAutoLoad
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "8080:8080"
    networks:
      - cctv_net
    depends_on:
      shinobi_db:
        condition: service_healthy
    restart: unless-stopped

  # MariaDB for Shinobi
  shinobi_db:
    image: mariadb:10.6
    container_name: shinobi_db
    environment:
      - MYSQL_ROOT_PASSWORD=shinobi
      - MYSQL_USER=shinobi
      - MYSQL_PASSWORD=shinobi
      - MYSQL_DATABASE=ccio
    volumes:
      - shinobi_db_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --skip-name-resolve
    networks:
      - cctv_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u$${MYSQL_USER}", "-p$${MYSQL_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # pgAdmin for PostgreSQL management
  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin_service_django
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data_django:/var/lib/pgadmin
    networks:
      - cctv_net
    restart: unless-stopped

networks:
  cctv_net:
    name: cctv_shared_network
    external: true

volumes:
  # Django App related
  postgres_data_django:
  static_volume:
  media_volume:
  logs_volume:
  redis_data:
  pgadmin_data_django:
  
  # Shinobi NVR related
  shinobi_db_data:
  
  # DuckDNS
  duckdns_config:
