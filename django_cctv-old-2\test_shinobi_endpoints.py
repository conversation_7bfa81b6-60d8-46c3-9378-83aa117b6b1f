#!/usr/bin/env python3
"""
Test different Shinobi API endpoint formats to find the correct one
"""

import requests
import json

# Configuration
HOST = "http://shinobi-nvr:8080"
API_KEY = "vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx"
GROUP_KEY = "VqJe1awj1m"

def test_endpoint(url, description):
    """Test a specific endpoint"""
    print(f"\n🔍 Testing: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ SUCCESS! Response type: {type(data)}")
                if isinstance(data, list):
                    print(f"Number of items: {len(data)}")
                    if data:
                        print(f"First item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
                elif isinstance(data, dict):
                    print(f"Response keys: {list(data.keys())}")
                return True, data
            except json.JSONDecodeError:
                print(f"✅ SUCCESS! Response (not JSON): {response.text[:200]}")
                return True, response.text
        else:
            print(f"❌ Failed: {response.text[:200]}")
            return False, response.text
            
    except requests.exceptions.Timeout:
        print("❌ Timeout")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, str(e)

def main():
    print("🚀 Testing Shinobi API Endpoints")
    print("=" * 50)
    
    # Test different endpoint formats
    endpoints = [
        # Standard API formats
        (f"{HOST}/{GROUP_KEY}/monitor?api={API_KEY}", "Standard format: /{group_key}/monitor"),
        (f"{HOST}/api/{GROUP_KEY}/monitor?api={API_KEY}", "API prefix: /api/{group_key}/monitor"),
        (f"{HOST}/api/monitor/{GROUP_KEY}?api={API_KEY}", "API monitor: /api/monitor/{group_key}"),
        
        # Alternative formats
        (f"{HOST}/{GROUP_KEY}/monitors?api={API_KEY}", "Plural monitors: /{group_key}/monitors"),
        (f"{HOST}/api/{GROUP_KEY}/monitors?api={API_KEY}", "API plural: /api/{group_key}/monitors"),
        (f"{HOST}/api/monitors/{GROUP_KEY}?api={API_KEY}", "API monitors: /api/monitors/{group_key}"),
        
        # JSON API formats
        (f"{HOST}/json/{GROUP_KEY}/monitor?api={API_KEY}", "JSON format: /json/{group_key}/monitor"),
        (f"{HOST}/json/{GROUP_KEY}/monitors?api={API_KEY}", "JSON plural: /json/{group_key}/monitors"),
        
        # Direct monitor access
        (f"{HOST}/monitor/{GROUP_KEY}?api={API_KEY}", "Direct monitor: /monitor/{group_key}"),
        (f"{HOST}/monitors/{GROUP_KEY}?api={API_KEY}", "Direct monitors: /monitors/{group_key}"),
        
        # With specific monitor ID
        (f"{HOST}/{GROUP_KEY}/monitor/L537lfvteS?api={API_KEY}", "Specific monitor: /{group_key}/monitor/{mid}"),
        (f"{HOST}/api/{GROUP_KEY}/monitor/L537lfvteS?api={API_KEY}", "API specific: /api/{group_key}/monitor/{mid}"),
    ]
    
    successful_endpoints = []
    
    for url, description in endpoints:
        success, data = test_endpoint(url, description)
        if success:
            successful_endpoints.append((url, description, data))
    
    print("\n" + "=" * 50)
    print("📊 RESULTS")
    print("=" * 50)
    
    if successful_endpoints:
        print(f"✅ Found {len(successful_endpoints)} working endpoint(s):")
        for url, description, data in successful_endpoints:
            print(f"\n🎯 {description}")
            print(f"   URL: {url}")
            if isinstance(data, list) and data:
                print(f"   Data: {len(data)} monitors found")
                for i, monitor in enumerate(data[:2]):  # Show first 2
                    if isinstance(monitor, dict):
                        print(f"     Monitor {i+1}: {monitor.get('name', 'Unknown')} (ID: {monitor.get('mid', 'Unknown')})")
            elif isinstance(data, dict):
                print(f"   Data: {list(data.keys())}")
    else:
        print("❌ No working endpoints found!")
        print("\nTroubleshooting suggestions:")
        print("1. Check if API key is correct")
        print("2. Check if group key exists")
        print("3. Check Shinobi logs for errors")
        print("4. Verify Shinobi version and API documentation")

if __name__ == "__main__":
    main()
