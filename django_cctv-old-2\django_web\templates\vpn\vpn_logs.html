{% extends 'base.html' %}

{% block content %}
  <div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">VPN Logs</h1>
    <form method="get" class="mb-6 flex flex-wrap gap-4">
      <input type="text" name="client" placeholder="Filter by Client ID" value="{{ request.GET.client }}" class="px-4 py-2 border rounded-md dark:bg-gray-700 dark:text-gray-100" />
      <select name="type" class="px-4 py-2 border rounded-md dark:bg-gray-700 dark:text-gray-100">
        <option value="">All Types</option>
        <option value="INFO" {% if request.GET.type == 'INFO' %}selected{% endif %}>Info</option>
        <option value="CONNECT" {% if request.GET.type == 'CONNECT' %}selected{% endif %}>Connect</option>
        <option value="DISCONNECT" {% if request.GET.type == 'DISCONNECT' %}selected{% endif %}>Disconnect</option>
      </select>
      <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 font-semibold">Filter</button>
    </form>
    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
      {% for log in logs %}
        <li class="py-2 flex flex-col md:flex-row md:items-center md:justify-between">
          <span class="text-gray-900 dark:text-gray-100">{{ log.timestamp }} - {{ log.get_event_type_display }}: {{ log.message }}</span>
          {% if log.client %}
            <span class="text-xs text-gray-600 dark:text-gray-400">Client: {{ log.client.name }} ({{ log.client.client_id }})</span>
          {% endif %}
          {% if log.ip_address %}
            <span class="text-xs text-gray-600 dark:text-gray-400">IP: {{ log.ip_address }}</span>
          {% endif %}
        </li>
      {% empty %}
        <li class="py-2 text-gray-500 dark:text-gray-400">No logs found.</li>
      {% endfor %}
    </ul>
    <div class="mt-8">
      <a href="{% url 'dashboard:index' %}" class="inline-block px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Back to Dashboard</a>
    </div>
  </div>
{% endblock %}
