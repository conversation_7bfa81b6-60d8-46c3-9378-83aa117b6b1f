services:
  # Django web application
  web:
    build: .
    container_name: bolt2_web
    restart: unless-stopped
    volumes:
      - .:/app
      - media_data:/app/media
    environment:
      - SHINOBI_API_URL=http://shinobi-nvr:8080
      - SHINOBI_API_KEY=${SHINOBI_API_KEY:-default_api_key_for_dev}
      - SHINOBI_GROUP_KEY=${SHINOBI_GROUP_KEY:-default_group_key_for_dev}
      - OPENVPN_API_URL=http://openvpn-admin:8080
      - OPENVPN_USERNAME=admin
      - OPENVPN_PASSWORD=${OPENVPN_PASSWORD:-gagaZush}
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY:-django-insecure-l0p@+4)1_k73%9k!j4nw(i5yb-g5n=s4t+rx)zr8q5y_@!m2vf}
      - DJANGO_DEBUG=1
    ports:
      - "8000:8000"
    depends_on:
      - shinobi-nvr
      - openvpn
    networks:
      - cctv_net
    command: >
      bash -c "python manage.py migrate &&
              python manage.py collectstatic --noinput &&
              gunicorn --bind 0.0.0.0:8000 cctv_project.wsgi:application"

  # DuckDNS for dynamic DNS
  duckdns:
    image: linuxserver/duckdns:latest
    container_name: bolt2_duckdns
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Istanbul
      - SUBDOMAINS=abinetalemuvpn
      - TOKEN=0085376b-1765-4ff7-924d-aa9d3c4f7a8a
      - LOG_FILE=false
    volumes:
      - duckdns_config:/config
    restart: unless-stopped
    networks:
      - cctv_net

  # OpenVPN Server
  openvpn:
    image: kylemanna/openvpn
    container_name: bolt2_openvpn
    cap_add:
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.forwarding=1
      - net.ipv6.conf.default.forwarding=1
    dns:
      - 127.0.0.11
    ports:
      - "1194:1194/udp"
    volumes:
      - ./openvpn_data:/etc/openvpn
      - ./openvpn_data/EasyRSA-3.1.7:/usr/share/easy-rsa
      - ./openvpn_data/EasyRSA-3.1.7/easyrsa:/usr/share/easy-rsa/easyrsa
      - /dev/net/tun:/dev/net/tun
      - ./openvpn_data/pki/private/ca.key:/etc/openvpn/pki/private/ca.key:ro
      - ./openvpn_data/pki/ta.key:/etc/openvpn/pki/ta.key:ro
    environment:
      - TZ=Europe/Istanbul
      - EASYRSA=/usr/share/easy-rsa/easyrsa
    networks:
      - cctv_net
    restart: unless-stopped

  # OpenVPN Web UI
  openvpn-ui:
    image: d3vilh/openvpn-ui:latest
    container_name: bolt2_openvpn_ui
    restart: unless-stopped
    networks:
      - cctv_net
    ports:
      - "8082:8080"
    environment:
      - OVPN_UI_USERNAME=admin
      - OVPN_UI_PASSWORD=gagaZush
      - TZ=Europe/Istanbul
    volumes:
      - ./openvpn_data:/etc/openvpn
      - openvpn_ui_db:/opt/openvpn-ui/db
    depends_on:
      - openvpn

  # OpenVPN Management API (flant/openvpn-admin)
  openvpn-admin:
    image: flant/openvpn-admin:latest
    container_name: bolt2_openvpn_admin
    restart: unless-stopped
    environment:
      - OA_OPENVPN_CONFIG=/etc/openvpn/openvpn.conf
      - OA_OPENVPN_DIR=/etc/openvpn
      - OA_LISTEN=0.0.0.0:8080
      - OA_USERNAME=admin
      - OA_PASSWORD=gagaZush
      - TZ=Europe/Istanbul
    volumes:
      - ./openvpn_data:/etc/openvpn
    ports:
      - "8081:8080"
    depends_on:
      - openvpn
    networks:
      - cctv_net
      
  # Shinobi NVR
  shinobi-nvr:
    image: shinobisystems/shinobi:dev
    container_name: bolt2_shinobi_nvr
    restart: always
    ports:
      - "8080:8080"   # Web UI
      - "9000:9000"   # Streaming
    volumes:
      - shinobi_config:/config
      - shinobi_customAutoLoad:/customAutoLoad
      - shinobi_videos:/home/<USER>/videos
    environment:
      - MYSQL_ROOT_PASSWORD=shinobiroot
      - MYSQL_DATABASE=ccio
      - MYSQL_USER=majesticflame
      - MYSQL_PASSWORD=shinobi
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=admin
      - STARTUP_ADMIN_USER=true
    depends_on:
      - shinobi_db
    networks:
      - cctv_net
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # MariaDB for Shinobi
  shinobi_db:
    image: mariadb:10.5
    container_name: bolt2_shinobi_mariadb
    environment:
      - MYSQL_USER=majesticflame
      - MYSQL_PASSWORD=shinobi
      - MYSQL_DATABASE=ccio
      - MYSQL_ROOT_PASSWORD=shinobiroot
    volumes:
      - shinobi_db_data:/var/lib/mysql
    networks:
      - cctv_net
    restart: unless-stopped

networks:
  cctv_net:
    driver: bridge

volumes:
  # Django application
  media_data:

  # Shinobi NVR related
  shinobi_db_data:
  shinobi_config: 
  shinobi_customAutoLoad:
  shinobi_videos:

  # DuckDNS
  duckdns_config:

  # OpenVPN
  openvpn_data:
  openvpn_ui_db: