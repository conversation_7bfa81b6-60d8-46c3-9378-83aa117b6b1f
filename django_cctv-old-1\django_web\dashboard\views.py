from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from cameras.models import Camera, CameraEvent
import requests
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

@login_required
def index(request):
    """Main dashboard view."""
    # Get camera statistics
    if request.user.is_admin:
        cameras = Camera.objects.all()
    else:
        # Filter cameras based on user's permissions
        camera_groups = request.user.camera_groups.all()
        cameras = Camera.objects.filter(groups__in=camera_groups).distinct()
    
    total_cameras = cameras.count()
    online_cameras = cameras.filter(status=Camera.CameraStatus.ONLINE).count()
    offline_cameras = cameras.filter(status=Camera.CameraStatus.OFFLINE).count()
    
    # Get recent events
    if request.user.is_admin:
        recent_events = CameraEvent.objects.order_by('-timestamp')[:10]
    else:
        recent_events = CameraEvent.objects.filter(
            camera__in=cameras
        ).order_by('-timestamp')[:10]
    
    # System status checks
    # This could be expanded to include more detailed health checks
    try:
        openvpn_status = check_openvpn_status()
    except Exception as e:
        logger.error(f"Error checking OpenVPN status: {str(e)}")
        openvpn_status = False
    
    try:
        shinobi_status = check_shinobi_status()
    except Exception as e:
        logger.error(f"Error checking Shinobi status: {str(e)}")
        shinobi_status = False
    
    context = {
        'total_cameras': total_cameras,
        'online_cameras': online_cameras,
        'offline_cameras': offline_cameras,
        'recent_events': recent_events,
        'openvpn_status': openvpn_status,
        'shinobi_status': shinobi_status,
        'cameras': cameras.order_by('name')[:5]  # Top 5 cameras for quick access
    }
    
    return render(request, 'dashboard/index.html', context)

@login_required
def system_status(request):
    """API endpoint for getting system status (for AJAX updates)."""
    # Get camera statistics
    if request.user.is_admin:
        cameras = Camera.objects.all()
    else:
        # Filter cameras based on user's permissions
        camera_groups = request.user.camera_groups.all()
        cameras = Camera.objects.filter(groups__in=camera_groups).distinct()
    
    total_cameras = cameras.count()
    online_cameras = cameras.filter(status=Camera.CameraStatus.ONLINE).count()
    offline_cameras = cameras.filter(status=Camera.CameraStatus.OFFLINE).count()
    
    # System status checks
    try:
        openvpn_status = check_openvpn_status()
    except Exception:
        openvpn_status = False
    
    try:
        shinobi_status = check_shinobi_status()
    except Exception:
        shinobi_status = False
    
    return JsonResponse({
        'total_cameras': total_cameras,
        'online_cameras': online_cameras,
        'offline_cameras': offline_cameras,
        'openvpn_status': openvpn_status,
        'shinobi_status': shinobi_status,
    })

def check_openvpn_status():
    """Check if OpenVPN service is running."""
    try:
        response = requests.get(
            f"{settings.OPENVPN_API_URL}/api/status", 
            auth=(settings.OPENVPN_USERNAME, settings.OPENVPN_PASSWORD),
            timeout=5
        )
        return response.status_code == 200
    except requests.RequestException:
        return False

def check_shinobi_status():
    """Check if Shinobi NVR service is running."""
    try:
        response = requests.get(f"{settings.SHINOBI_API_URL}/", timeout=5)
        return response.status_code == 200
    except requests.RequestException:
        return False