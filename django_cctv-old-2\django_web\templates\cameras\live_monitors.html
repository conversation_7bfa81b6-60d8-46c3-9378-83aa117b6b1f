{% extends 'base.html' %}
{% load static %}

{% block title %}Live Monitors - CCTV System{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800 dark:text-gray-200">Live Camera Monitors</h1>
        <div class="flex space-x-2">
            <button type="button" id="toggle-grid-size" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                📐 Larger Grid
            </button>
            <button type="button" id="refresh-cameras" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                <span id="refresh-spinner" class="hidden">⟳</span>
                🔄 <span id="refresh-text">Refresh</span>
            </button>
        </div>
    </div>

    <div class="camera-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {% if error_message %}
            <div class="col-span-full bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <strong>Error fetching monitors:</strong> {{ error_message }}
            </div>
        {% elif monitors %}
            {% for monitor in monitors %}
            <div class="camera-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden" 
                 data-camera-id="{{ monitor.mid }}" 
                 data-status="{{ monitor.status }}" 
                 data-location-id="{{ monitor.location_id }}">
                
                <div class="camera-feed relative">
                    <div class="aspect-video bg-black rounded-t-lg">
                        <video id="video-{{ monitor.mid }}" 
                               width="320" 
                               height="240" 
                               controls 
                               muted 
                               autoplay 
                               poster="{{ monitor.preview_url }}" 
                               class="w-full h-full object-cover rounded-t-lg">
                            <source src="{{ monitor.hls_url }}" type="application/x-mpegURL">
                            <span class="text-white">Your browser does not support the video tag.</span>
                        </video>
                    </div>
                    
                    <!-- Enhanced Status indicator with animations -->
                    <span class="camera-status absolute top-2 right-2 px-2 py-1 rounded text-xs font-bold transition-all duration-300
                                {% if monitor.status == 'online' %}bg-green-500 text-white{% else %}bg-red-500 text-white{% endif %}"
                          data-status-indicator="{{ monitor.mid }}">
                        {% if monitor.status == 'online' %}
                            <span class="inline-flex items-center">
                                <span class="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
                                Online
                            </span>
                        {% else %}
                            {{ monitor.status|title }}
                        {% endif %}
                    </span>

                    <!-- Network Status Overlay (hidden by default) -->
                    <div id="network-overlay-{{ monitor.mid }}" class="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center text-center text-white z-20 hidden transition-all duration-300">
                        <div class="network-message p-6">
                            <div class="mb-4">
                                <svg class="animate-spin h-8 w-8 mx-auto text-blue-400" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold mb-2">Reconnecting...</h3>
                            <p class="text-sm text-gray-300" id="reconnect-message-{{ monitor.mid }}">Attempting to restore connection</p>
                            <button onclick="manualRetry('{{ monitor.mid }}')" class="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Retry Now
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 bg-white dark:bg-gray-800">
                    <h5 class="font-semibold text-blue-600 dark:text-blue-400 mb-2">{{ monitor.name }}</h5>
                    
                    <!-- Monitor details -->
                    <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <div><strong>Mode:</strong> {{ monitor.mode|default:"Unknown" }}</div>
                        <div><strong>Resolution:</strong> {{ monitor.width }}x{{ monitor.height }}</div>
                        <div><strong>FPS:</strong> {{ monitor.fps }}</div>
                        <div><strong>Protocol:</strong> {{ monitor.protocol }}</div>
                    </div>
                    
                    <div class="mt-3 flex space-x-2">
                        <button type="button" 
                                class="flex-1 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                                onclick="toggleFullscreen('video-{{ monitor.mid }}')">
                            ⛶ Fullscreen
                        </button>
                        <button type="button" 
                                class="flex-1 px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
                                onclick="refreshVideo('video-{{ monitor.mid }}')">
                            🔄 Refresh
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-span-full text-center py-12">
                <div class="text-gray-400 mb-4">
                    <svg class="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                </div>
                <h4 class="text-xl text-gray-600 dark:text-gray-400 mb-2">No cameras found</h4>
                <p class="text-gray-500 dark:text-gray-500">There are no cameras configured in Shinobi.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
<script>
    // 🌐 LEGENDARY NETWORK RESILIENCE SYSTEM 🌐
    class NetworkResilienceManager {
        constructor() {
            this.connectionStatus = 'online';
            this.retryAttempts = new Map();
            this.maxRetries = 5;
            this.baseRetryDelay = 1000; // 1 second
            this.hlsInstances = new Map();
            this.videoElements = new Map();
            this.init();
        }

        init() {
            this.setupNetworkMonitoring();
            this.setupVideoMonitoring();
            this.setupPeriodicHealthCheck();
            this.createGlobalNetworkIndicator();
            console.log('🎖️ Network Resilience Manager initialized');
        }

        setupNetworkMonitoring() {
            // Monitor online/offline events
            window.addEventListener('online', () => {
                this.handleConnectionRestore();
            });

            window.addEventListener('offline', () => {
                this.handleConnectionLoss();
            });

            // Monitor network quality changes
            if ('connection' in navigator) {
                navigator.connection.addEventListener('change', () => {
                    this.handleConnectionChange();
                });
            }
        }

        setupVideoMonitoring() {
            const videos = document.querySelectorAll('video');
            videos.forEach(video => {
                const cameraId = video.id.replace('video-', '');
                this.videoElements.set(cameraId, video);

                // Enhanced video event monitoring
                video.addEventListener('error', () => this.handleVideoError(cameraId));
                video.addEventListener('loadstart', () => this.updateCameraStatus(cameraId, 'loading'));
                video.addEventListener('canplay', () => this.handleVideoSuccess(cameraId));
                video.addEventListener('stalled', () => this.updateCameraStatus(cameraId, 'buffering'));
                video.addEventListener('waiting', () => this.updateCameraStatus(cameraId, 'buffering'));
                video.addEventListener('emptied', () => this.handleVideoError(cameraId));
            });
        }

        setupPeriodicHealthCheck() {
            // Check connection health every 15 seconds
            setInterval(() => {
                this.performHealthCheck();
            }, 15000);
        }

        createGlobalNetworkIndicator() {
            // Create global network status indicator
            const indicator = document.createElement('div');
            indicator.id = 'global-network-indicator';
            indicator.className = 'fixed top-4 right-4 px-3 py-1 rounded-full text-xs font-bold z-50 transition-all duration-300 text-white hidden';
            document.body.appendChild(indicator);
        }

        async performHealthCheck() {
            try {
                const response = await fetch('/cameras/live/', {
                    method: 'HEAD',
                    cache: 'no-cache'
                });

                if (response.ok) {
                    this.updateGlobalStatus('online');
                } else {
                    this.updateGlobalStatus('degraded');
                }
            } catch (error) {
                this.updateGlobalStatus('offline');
            }
        }

        handleConnectionLoss() {
            this.updateGlobalStatus('offline');
            this.showNotification('🌐 Connection lost. Attempting to reconnect...', 'error');
            this.startGlobalReconnection();
        }

        handleConnectionRestore() {
            this.updateGlobalStatus('online');
            this.showNotification('🌐 Connection restored!', 'success');
            this.retryAllFailedVideos();
        }

        handleConnectionChange() {
            if ('connection' in navigator) {
                const connection = navigator.connection;
                const quality = this.getConnectionQuality(connection);
                this.adaptToConnectionQuality(quality);
            }
        }

        getConnectionQuality(connection) {
            const downlink = connection.downlink || 0;
            if (downlink >= 10) return 'excellent';
            if (downlink >= 5) return 'good';
            if (downlink >= 1) return 'fair';
            return 'poor';
        }

        adaptToConnectionQuality(quality) {
            if (quality === 'poor') {
                this.showNotification('🌐 Poor connection detected. Optimizing streams...', 'warning');
            }
        }

        handleVideoError(cameraId) {
            console.log(`🚨 Video error for camera ${cameraId}`);
            this.updateCameraStatus(cameraId, 'error');
            this.showNetworkOverlay(cameraId, true);
            this.scheduleVideoRetry(cameraId);
        }

        handleVideoSuccess(cameraId) {
            this.updateCameraStatus(cameraId, 'online');
            this.showNetworkOverlay(cameraId, false);
            this.retryAttempts.delete(cameraId);
        }

        scheduleVideoRetry(cameraId) {
            const attempts = this.retryAttempts.get(cameraId) || 0;

            if (attempts >= this.maxRetries) {
                this.updateCameraStatus(cameraId, 'failed');
                this.updateReconnectMessage(cameraId, `Failed after ${this.maxRetries} attempts`);
                return;
            }

            const delay = this.baseRetryDelay * Math.pow(2, attempts); // Exponential backoff
            this.retryAttempts.set(cameraId, attempts + 1);

            this.updateReconnectMessage(cameraId, `Retry ${attempts + 1}/${this.maxRetries} in ${Math.ceil(delay/1000)}s...`);

            setTimeout(() => {
                this.retryVideoConnection(cameraId);
            }, delay);
        }

        retryVideoConnection(cameraId) {
            const video = this.videoElements.get(cameraId);
            if (video) {
                this.updateCameraStatus(cameraId, 'reconnecting');
                this.updateReconnectMessage(cameraId, 'Reconnecting...');

                // Reload HLS if available
                const hls = this.hlsInstances.get(cameraId);
                if (hls) {
                    hls.destroy();
                    this.setupHLSForVideo(video, cameraId);
                } else {
                    video.load();
                }
            }
        }

        retryAllFailedVideos() {
            this.retryAttempts.clear();
            this.videoElements.forEach((video, cameraId) => {
                const status = this.getCameraStatus(cameraId);
                if (status === 'error' || status === 'failed') {
                    this.retryVideoConnection(cameraId);
                }
            });
        }

        updateCameraStatus(cameraId, status) {
            const statusIndicator = document.querySelector(`[data-status-indicator="${cameraId}"]`);
            if (statusIndicator) {
                // Update status badge
                statusIndicator.className = statusIndicator.className.replace(/bg-\w+-\d+/, this.getStatusColor(status));
                statusIndicator.innerHTML = this.getStatusText(status);
            }
        }

        getStatusColor(status) {
            switch (status) {
                case 'online': return 'bg-green-500';
                case 'loading': return 'bg-yellow-500';
                case 'buffering': return 'bg-blue-500';
                case 'reconnecting': return 'bg-orange-500';
                case 'error': return 'bg-red-500';
                case 'failed': return 'bg-gray-500';
                default: return 'bg-gray-500';
            }
        }

        getStatusText(status) {
            switch (status) {
                case 'online':
                    return '<span class="inline-flex items-center"><span class="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>Online</span>';
                case 'loading':
                    return '<span class="inline-flex items-center"><span class="animate-spin w-2 h-2 border border-white rounded-full mr-1"></span>Loading</span>';
                case 'buffering':
                    return '<span class="inline-flex items-center"><span class="animate-spin w-2 h-2 border border-white rounded-full mr-1"></span>Buffering</span>';
                case 'reconnecting':
                    return '<span class="inline-flex items-center"><span class="animate-spin w-2 h-2 border border-white rounded-full mr-1"></span>Reconnecting</span>';
                case 'error':
                    return 'Connection Lost';
                case 'failed':
                    return 'Failed';
                default:
                    return 'Unknown';
            }
        }

        getCameraStatus(cameraId) {
            const statusIndicator = document.querySelector(`[data-status-indicator="${cameraId}"]`);
            if (statusIndicator) {
                if (statusIndicator.className.includes('bg-green-500')) return 'online';
                if (statusIndicator.className.includes('bg-red-500')) return 'error';
                if (statusIndicator.className.includes('bg-yellow-500')) return 'loading';
                if (statusIndicator.className.includes('bg-blue-500')) return 'buffering';
                if (statusIndicator.className.includes('bg-orange-500')) return 'reconnecting';
                if (statusIndicator.className.includes('bg-gray-500')) return 'failed';
            }
            return 'unknown';
        }

        showNetworkOverlay(cameraId, show) {
            const overlay = document.getElementById(`network-overlay-${cameraId}`);
            if (overlay) {
                if (show) {
                    overlay.classList.remove('hidden');
                } else {
                    overlay.classList.add('hidden');
                }
            }
        }

        updateReconnectMessage(cameraId, message) {
            const messageElement = document.getElementById(`reconnect-message-${cameraId}`);
            if (messageElement) {
                messageElement.textContent = message;
            }
        }

        updateGlobalStatus(status) {
            this.connectionStatus = status;
            const indicator = document.getElementById('global-network-indicator');
            if (indicator) {
                switch (status) {
                    case 'online':
                        indicator.className = indicator.className.replace(/bg-\w+-\d+/, 'bg-green-500');
                        indicator.textContent = '🌐 Online';
                        indicator.classList.add('hidden');
                        break;
                    case 'degraded':
                        indicator.className = indicator.className.replace(/bg-\w+-\d+/, 'bg-yellow-500');
                        indicator.textContent = '🌐 Slow Connection';
                        indicator.classList.remove('hidden');
                        break;
                    case 'offline':
                        indicator.className = indicator.className.replace(/bg-\w+-\d+/, 'bg-red-500');
                        indicator.textContent = '🌐 Offline';
                        indicator.classList.remove('hidden');
                        break;
                }
            }
        }

        startGlobalReconnection() {
            const reconnectInterval = setInterval(() => {
                if (navigator.onLine) {
                    this.performHealthCheck();
                    if (this.connectionStatus === 'online') {
                        clearInterval(reconnectInterval);
                    }
                }
            }, 2000);
        }

        setupHLSForVideo(video, cameraId) {
            const source = video.querySelector('source');
            if (source && source.src) {
                if (Hls.isSupported()) {
                    const hls = new Hls({
                        enableWorker: false,
                        lowLatencyMode: true,
                        backBufferLength: 90
                    });

                    hls.loadSource(source.src);
                    hls.attachMedia(video);
                    this.hlsInstances.set(cameraId, hls);

                    hls.on(Hls.Events.ERROR, (event, data) => {
                        if (data.fatal) {
                            this.handleVideoError(cameraId);
                        }
                    });
                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                    video.src = source.src;
                }
            }
        }

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-600' : type === 'error' ? 'bg-red-600' : type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600';
            notification.className = `fixed bottom-4 right-4 ${bgColor} text-white px-4 py-2 rounded-md shadow-lg z-50 transition-all duration-300`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
    }

    // Initialize the system
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize Network Resilience Manager
        const networkManager = new NetworkResilienceManager();

        // Enhanced HLS setup with network resilience
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
            const cameraId = video.id.replace('video-', '');
            networkManager.setupHLSForVideo(video, cameraId);
        });

        // Grid size toggle
        const toggleButton = document.getElementById('toggle-grid-size');
        const grid = document.querySelector('.camera-grid');
        let isLargeGrid = false;

        toggleButton.addEventListener('click', function() {
            if (isLargeGrid) {
                grid.className = 'camera-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4';
                toggleButton.textContent = '📐 Larger Grid';
                isLargeGrid = false;
            } else {
                grid.className = 'camera-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6';
                toggleButton.textContent = '📐 Smaller Grid';
                isLargeGrid = true;
            }
        });

        // Enhanced refresh with network awareness
        document.getElementById('refresh-cameras').addEventListener('click', function() {
            const spinner = document.getElementById('refresh-spinner');
            const text = document.getElementById('refresh-text');

            spinner.classList.remove('hidden');
            text.textContent = 'Refreshing...';

            networkManager.showNotification('🔄 Refreshing camera feeds...', 'info');

            setTimeout(() => {
                location.reload();
            }, 1000);
        });

        // Make networkManager globally available
        window.networkManager = networkManager;
    });

    // Enhanced fullscreen function with network awareness
    function toggleFullscreen(videoId) {
        const video = document.getElementById(videoId);
        if (video) {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }

            if (window.networkManager) {
                window.networkManager.showNotification('📺 Entering fullscreen mode', 'info');
            }
        }
    }

    // Enhanced refresh video function with network resilience
    function refreshVideo(videoId) {
        const video = document.getElementById(videoId);
        if (video && window.networkManager) {
            window.networkManager.retryVideoConnection(videoId);
            window.networkManager.showNotification('🔄 Refreshing camera feed...', 'info');
        } else if (video) {
            video.load();
        }
    }

    // Manual retry function for network overlay
    function manualRetry(cameraId) {
        if (window.networkManager) {
            window.networkManager.retryVideoConnection(cameraId);
            window.networkManager.showNotification('🔄 Manual retry initiated...', 'info');
        }
    }
</script>
{% endblock %}
