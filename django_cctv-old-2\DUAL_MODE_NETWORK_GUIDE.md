# 🌐 Dual-Mode Network Configuration Guide

## 🎯 Overview

The CCTV Warehouse Monitoring System now supports **dual-mode network operation**, allowing seamless integration with organization VPN networks while maintaining standalone functionality.

## 🏗️ Architecture Modes

### 🏠 **Standalone Mode** (Default)
- **Network**: Local network only (192.168.x.x, 10.x.x.x)
- **Cameras**: Local network cameras
- **VPN**: Internal OpenVPN server for remote access
- **Use Case**: Standard deployment, home/office networks

### 🏢 **Organization VPN Mode**
- **Network**: Organization VPN network (172.16.x.x)
- **Cameras**: Organization VPN cameras
- **VPN**: Connected to organization's VPN
- **Use Case**: Deployment at client organization with existing VPN

### 🔄 **Hybrid Mode**
- **Network**: Both local and VPN networks
- **Cameras**: Local + VPN cameras in unified dashboard
- **VPN**: Dual network access
- **Use Case**: Complex deployments with multiple camera sources

## 🚀 Quick Start

### **Windows (PowerShell)**
```powershell
# Auto-detect and configure
.\switch_network_mode.ps1 detect

# Switch to organization VPN mode
.\switch_network_mode.ps1 organization

# Switch to hybrid mode
.\switch_network_mode.ps1 hybrid

# Check current status
.\switch_network_mode.ps1 status
```

### **Linux/Mac (Bash)**
```bash
# Auto-detect and configure
./switch_network_mode.sh detect

# Switch to organization VPN mode
./switch_network_mode.sh organization

# Switch to hybrid mode
./switch_network_mode.sh hybrid

# Check current status
./switch_network_mode.sh status
```

## 📋 Deployment Scenarios

### **Scenario 1: Organization X Deployment**

**Problem**: Organization has existing VPN (172.168.x.x), wants to integrate our system

**Solution**:
1. Connect host machine to organization VPN
2. Run: `.\switch_network_mode.ps1 organization`
3. System automatically detects VPN and configures for organization cameras
4. Shinobi discovers cameras on VPN network
5. Django services display VPN cameras with proper access control

### **Scenario 2: Hybrid Deployment**

**Problem**: Need to monitor both local cameras and organization VPN cameras

**Solution**:
1. Connect to organization VPN
2. Run: `.\switch_network_mode.ps1 hybrid`
3. System operates on both networks simultaneously
4. Unified dashboard shows all cameras with proper grouping

### **Scenario 3: Switching Between Modes**

**Problem**: Need to switch between standalone and organization modes

**Solution**:
```powershell
# Start in standalone mode
.\switch_network_mode.ps1 standalone

# Connect to organization VPN
# Switch to organization mode
.\switch_network_mode.ps1 organization

# Disconnect from VPN
# Switch back to standalone
.\switch_network_mode.ps1 standalone
```

## 🔧 Technical Implementation

### **Network Detection**
- Automatically detects available network interfaces
- Identifies VPN connections (tun/tap interfaces, 172.16.x.x IPs)
- Configures services based on detected network topology

### **Dynamic Configuration**
- Updates environment variables based on network mode
- Reconfigures Shinobi NVR for appropriate network binding
- Adjusts camera discovery ranges automatically

### **Service Integration**
- Django Web Service: Adapts camera URLs based on network mode
- Shinobi CCTV Django: Configures for appropriate network access
- Shinobi NVR: Binds to correct network interfaces

## 📊 Configuration Files

### **Environment Variables**
```bash
# Dual-Mode Network Configuration
NETWORK_MODE=auto                           # auto, standalone, organization_vpn, hybrid
ENABLE_DUAL_MODE=true                       # Enable dual-mode functionality
SHINOBI_BACKEND_URL=http://shinobi-nvr:8080 # Internal Docker URL
SHINOBI_FRONTEND_URL=http://localhost:8080  # Frontend access URL
ORGANIZATION_VPN_ENABLED=false             # VPN mode flag
CAMERA_DISCOVERY_RANGES=***********/24     # Local camera ranges
VPN_CAMERA_DISCOVERY_RANGES=**********/12  # VPN camera ranges
```

### **Network Configuration Manager**
- `network_config_manager.py`: Detects and manages network interfaces
- `dynamic_shinobi_config.py`: Configures Shinobi based on network mode
- Auto-updates configuration based on network changes

## 🛡️ Security Considerations

### **Access Control**
- Camera Group permissions work across all network modes
- Users only see cameras from their assigned groups
- Network mode doesn't bypass security controls

### **VPN Security**
- Organization VPN credentials managed separately
- No interference with internal OpenVPN server
- Secure camera access through VPN tunnels

### **Network Isolation**
- Services properly isolated between network modes
- No cross-network security leaks
- Proper firewall and routing configuration

## 🧪 Testing and Validation

### **Network Connectivity Tests**
```powershell
# Test all network connectivity
.\switch_network_mode.ps1 test

# Check current network status
.\switch_network_mode.ps1 status
```

### **Service Health Checks**
- Shinobi NVR: `http://localhost:8080`
- Django Web: `http://localhost:8000`
- Shinobi CCTV Django: `http://localhost:5000`

### **Camera Discovery Validation**
- Verify cameras appear in appropriate network mode
- Test camera streaming from both networks
- Validate access control across network modes

## 🔄 Troubleshooting

### **Common Issues**

**VPN Not Detected**
```powershell
# Check network interfaces
.\switch_network_mode.ps1 status

# Force organization mode
.\switch_network_mode.ps1 organization
```

**Services Not Responding**
```powershell
# Restart services
docker-compose restart shinobi-nvr django_web shinobi_cctv_django

# Test connectivity
.\switch_network_mode.ps1 test
```

**Camera Discovery Issues**
- Check camera IP ranges in environment files
- Verify network connectivity to camera subnets
- Update discovery ranges if needed

### **Manual Configuration**
If automatic detection fails, manually edit environment files:

```bash
# django_web/.env
NETWORK_MODE=organization_vpn
ORGANIZATION_VPN_ENABLED=true
VPN_CAMERA_DISCOVERY_RANGES=***********/24  # Adjust for organization network
```

## 🎖️ Best Practices

1. **Always test connectivity** after switching modes
2. **Backup configurations** before major changes
3. **Document organization VPN settings** for future deployments
4. **Monitor service logs** during network mode switches
5. **Validate camera access** after configuration changes

## 📞 Support

For issues with dual-mode network configuration:
1. Run `.\switch_network_mode.ps1 status` to gather information
2. Check Docker service logs: `docker-compose logs`
3. Verify network connectivity with organization IT team
4. Test with `.\switch_network_mode.ps1 test`

---

**🎖️ The dual-mode network system enables seamless deployment across any network topology while maintaining security and functionality!**
