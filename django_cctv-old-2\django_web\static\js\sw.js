/**
 * Service Worker for CCTV System Network Resilience
 * Provides offline support and background sync
 */

const CACHE_NAME = 'cctv-system-v1';
const OFFLINE_URL = '/offline/';

// Resources to cache for offline use
const CACHE_RESOURCES = [
    '/',
    '/static/css/main.css',
    '/static/js/network-resilience.js',
    '/static/images/offline-camera.svg',
    OFFLINE_URL
];

// Install event - cache resources
self.addEventListener('install', event => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('📦 Caching resources...');
                return cache.addAll(CACHE_RESOURCES);
            })
            .then(() => {
                console.log('✅ Service Worker installed successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Service Worker installation failed:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - network-first strategy with fallbacks
self.addEventListener('fetch', event => {
    const request = event.request;
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Handle different types of requests
    if (request.url.includes('/api/')) {
        // API requests - network first with timeout
        event.respondWith(handleApiRequest(request));
    } else if (request.url.includes('/static/')) {
        // Static resources - cache first
        event.respondWith(handleStaticRequest(request));
    } else if (request.url.includes('.m3u8') || request.url.includes('.ts')) {
        // HLS video streams - network only with timeout
        event.respondWith(handleVideoRequest(request));
    } else {
        // HTML pages - network first with offline fallback
        event.respondWith(handlePageRequest(request));
    }
});

// Handle API requests with timeout and retry
async function handleApiRequest(request) {
    try {
        // Try network first with timeout
        const networkResponse = await fetchWithTimeout(request, 10000);
        
        if (networkResponse.ok) {
            // Cache successful responses
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.warn('🌐 API request failed, trying cache:', request.url);
        
        // Try cache as fallback
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return error response
        return new Response(
            JSON.stringify({ 
                error: 'Network unavailable', 
                offline: true,
                timestamp: new Date().toISOString()
            }),
            {
                status: 503,
                statusText: 'Service Unavailable',
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Handle static resources - cache first
async function handleStaticRequest(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.warn('📦 Static resource failed:', request.url);
        return new Response('Resource unavailable', { status: 404 });
    }
}

// Handle video streams - network only with timeout
async function handleVideoRequest(request) {
    try {
        return await fetchWithTimeout(request, 15000);
    } catch (error) {
        console.warn('📹 Video stream failed:', request.url);
        
        // Return placeholder for failed video streams
        return new Response('Video stream unavailable', {
            status: 503,
            statusText: 'Video Stream Unavailable'
        });
    }
}

// Handle page requests - network first with offline fallback
async function handlePageRequest(request) {
    try {
        const networkResponse = await fetchWithTimeout(request, 8000);
        
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.warn('📄 Page request failed, trying cache:', request.url);
        
        // Try cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page
        return caches.match(OFFLINE_URL);
    }
}

// Fetch with timeout utility
function fetchWithTimeout(request, timeout = 10000) {
    return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
            reject(new Error('Request timeout'));
        }, timeout);
        
        fetch(request)
            .then(response => {
                clearTimeout(timeoutId);
                resolve(response);
            })
            .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
    });
}

// Background sync for failed requests
self.addEventListener('sync', event => {
    console.log('🔄 Background sync triggered:', event.tag);
    
    if (event.tag === 'camera-status-sync') {
        event.waitUntil(syncCameraStatus());
    } else if (event.tag === 'retry-failed-requests') {
        event.waitUntil(retryFailedRequests());
    }
});

// Sync camera status when back online
async function syncCameraStatus() {
    try {
        console.log('📹 Syncing camera status...');
        
        const response = await fetch('/api/cameras/status/', {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
        });
        
        if (response.ok) {
            const data = await response.json();
            
            // Notify all clients about status update
            const clients = await self.clients.matchAll();
            clients.forEach(client => {
                client.postMessage({
                    type: 'CAMERA_STATUS_UPDATE',
                    data: data
                });
            });
        }
    } catch (error) {
        console.error('❌ Camera status sync failed:', error);
    }
}

// Retry failed requests when back online
async function retryFailedRequests() {
    try {
        console.log('🔄 Retrying failed requests...');
        
        // Get failed requests from IndexedDB (if implemented)
        // This would retry any requests that failed while offline
        
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'RETRY_FAILED_REQUESTS'
            });
        });
    } catch (error) {
        console.error('❌ Retry failed requests failed:', error);
    }
}

// Handle messages from main thread
self.addEventListener('message', event => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
        case 'CACHE_CAMERA_DATA':
            cacheCameraData(data);
            break;
        case 'CLEAR_CACHE':
            clearCache();
            break;
        default:
            console.log('📨 Unknown message type:', type);
    }
});

// Cache camera data for offline use
async function cacheCameraData(data) {
    try {
        const cache = await caches.open(CACHE_NAME);
        const response = new Response(JSON.stringify(data), {
            headers: { 'Content-Type': 'application/json' }
        });
        await cache.put('/api/cameras/cached/', response);
        console.log('📦 Camera data cached successfully');
    } catch (error) {
        console.error('❌ Failed to cache camera data:', error);
    }
}

// Clear all caches
async function clearCache() {
    try {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
        console.log('🗑️ All caches cleared');
    } catch (error) {
        console.error('❌ Failed to clear cache:', error);
    }
}
