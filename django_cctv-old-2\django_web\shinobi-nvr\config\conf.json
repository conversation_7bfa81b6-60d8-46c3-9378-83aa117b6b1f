{"port": 8080, "addStorage": [{"name": "default", "path": "/var/lib/shinobi/videos"}], "db": {"host": "shinobi_db", "user": "majesticflame", "password": "shinobi", "database": "ccio", "port": 3306}, "rtmpServer": {"port": 1935}, "streamDir": "/dev/shm", "videosDir": "/var/lib/shinobi/videos", "binDir": "/home/<USER>", "superUseLog": true, "cron": {"deleteOverMax": true, "deleteOverMaxOffset": 10000}, "pluginKeys": {}, "databaseType": "postgresql", "cpuUsageMarker": "CPU", "subscriptionId": "sub_XXXXXXXXXXXX", "thisIsDocker": true, "ssl": {}, "viewSystemLog": true}