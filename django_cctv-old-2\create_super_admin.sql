-- Create Super Admin User for Shinob<PERSON>
-- This script creates the super admin user with the credentials from super.json

USE shinobi_db;

-- Insert the super admin user
INSERT INTO `Users` (`ke`, `uid`, `auth`, `mail`, `pass`, `accountType`, `details`) VALUES 
('TvqQwm', 'TvqQwm', 'TvqQwm', '<EMAIL>', 'sU5EjCH63wRMSo048y1tOdvm3B6xGk', 1, '{"max_storage":100000,"sub":true,"all_monitors":true,"permissions":{"watch_stream":true,"watch_snapshot":true,"watch_videos":true,"control_monitors":true,"delete_videos":true,"watch_timelapse":true,"delete_timelapse_frames":true,"watch_events":true,"delete_events":true,"control_presets":true,"edit_monitor_settings":true,"edit_monitor_groups":true,"edit_users":true,"edit_api_keys":true,"edit_schedules":true,"edit_sub_accounts":true,"edit_logs":true,"edit_system_settings":true,"view_system_logs":true,"view_monitor_logs":true,"view_preset_logs":true,"view_user_logs":true,"view_api_key_logs":true,"view_schedule_logs":true,"view_sub_account_logs":true,"view_system_settings_logs":true,"view_monitor_settings_logs":true,"view_monitor_groups_logs":true,"view_users_logs":true,"view_logs_logs":true}}')
ON DUPLICATE KEY UPDATE 
    `pass` = 'sU5EjCH63wRMSo048y1tOdvm3B6xGk',
    `accountType` = 1,
    `details` = '{"max_storage":100000,"sub":true,"all_monitors":true,"permissions":{"watch_stream":true,"watch_snapshot":true,"watch_videos":true,"control_monitors":true,"delete_videos":true,"watch_timelapse":true,"delete_timelapse_frames":true,"watch_events":true,"delete_events":true,"control_presets":true,"edit_monitor_settings":true,"edit_monitor_groups":true,"edit_users":true,"edit_api_keys":true,"edit_schedules":true,"edit_sub_accounts":true,"edit_logs":true,"edit_system_settings":true,"view_system_logs":true,"view_monitor_logs":true,"view_preset_logs":true,"view_user_logs":true,"view_api_key_logs":true,"view_schedule_logs":true,"view_sub_account_logs":true,"view_system_settings_logs":true,"view_monitor_settings_logs":true,"view_monitor_groups_logs":true,"view_users_logs":true,"view_logs_logs":true}}';

-- Verify the user was created
SELECT mail, accountType FROM Users WHERE mail = '<EMAIL>';
