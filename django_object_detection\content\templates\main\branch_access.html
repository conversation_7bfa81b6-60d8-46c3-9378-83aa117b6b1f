{% extends 'layouts/default/page.html' %}
{% load bootstrap4 i18n %}

{% block content %}
<div class="container d-flex justify-content-center align-items-center" style="min-height: 100vh;">
    <div class="card shadow-lg p-4" style="width: 350px;">
        <h4 class="mb-3 text-center">{% translate 'Branch Access Verification' %}</h4>
        <form method="post">
            {% csrf_token %}
            <div class="mb-3">
                <label for="branch_access_id" class="form-label">
                    {% translate 'Enter Branch Access ID for' %} {{ branch.name }}
                </label>
                <input type="text" class="form-control" name="branch_access_id" required>
            </div>
            <button type="submit" class="btn btn-primary btn-block">
                {% translate 'Verify Access' %}
            </button>
        </form>
        
        {% if messages %}
        <div class="mt-3">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}