from django import forms
from .models import Camera, CameraGroup

class CameraForm(forms.ModelForm):
    """Form for creating and updating cameras."""
    
    class Meta:
        model = Camera
        fields = [
            'name', 'description', 'location', 'type', 
            'stream_url', 'username', 'password', 'ip_address', 'port',
            'status', 'groups'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'description': forms.Textarea(attrs={'class': 'w-full px-4 py-2 border rounded-md', 'rows': 3}),
            'location': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'type': forms.Select(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'stream_url': forms.URLInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'username': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'password': forms.PasswordInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'ip_address': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'port': forms.NumberInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'status': forms.Select(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'groups': forms.SelectMultiple(attrs={'class': 'w-full px-4 py-2 border rounded-md'})
        }

class CameraGroupForm(forms.ModelForm):
    """Form for creating and updating camera groups."""
    
    class Meta:
        model = CameraGroup
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'description': forms.Textarea(attrs={'class': 'w-full px-4 py-2 border rounded-md', 'rows': 3})
        }

class CameraGridViewForm(forms.Form):
    """Form for configuring grid view layout."""
    
    cameras = forms.ModelMultipleChoiceField(
        queryset=Camera.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-checkbox'})
    )
    layout = forms.ChoiceField(
        choices=[
            ('1x1', '1 Camera (1x1)'),
            ('2x2', '4 Cameras (2x2)'),
            ('3x3', '9 Cameras (3x3)'),
            ('4x4', '16 Cameras (4x4)'),
        ],
        widget=forms.Select(attrs={'class': 'w-full px-4 py-2 border rounded-md'})
    )
    
    def __init__(self, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user and not user.is_admin:
            # Filter cameras based on user's permissions
            camera_groups = user.camera_groups.all()
            accessible_cameras = Camera.objects.filter(groups__in=camera_groups).distinct()
            self.fields['cameras'].queryset = accessible_cameras