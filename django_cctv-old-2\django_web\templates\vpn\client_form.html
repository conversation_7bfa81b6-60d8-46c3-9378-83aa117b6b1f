{% extends 'base.html' %}

{% block content %}
  <div class="max-w-xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">{{ title }}</h1>
    <form method="post" class="space-y-6">
      {% csrf_token %}
      {% for field in form %}
        <div>
          <label class="block text-gray-700 dark:text-gray-200 font-semibold mb-1" for="{{ field.id_for_label }}">
            {{ field.label }}
          </label>
          {{ field }}
          {% if field.help_text %}
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ field.help_text }}</p>
          {% endif %}
          {% for error in field.errors %}
            <p class="text-sm text-red-600 dark:text-red-400">{{ error }}</p>
          {% endfor %}
        </div>
      {% endfor %}
      <div class="flex justify-end">
        <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 font-semibold">
          Save Client
        </button>
      </div>
    </form>
    <div class="mt-8">
      <a href="{% url 'vpn:client_list' %}" class="inline-block px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Back to Client List</a>
    </div>
  </div>
{% endblock %}
