# 🎯 Object Detection Web Service Dockerfile
# Cloned from shinobi_cctv_django and adapted for object detection
FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Set work directory inside the container
WORKDIR /app
    
# Install system dependencies first (for better layer caching)
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && apt-get install -y easy-rsa \
    && apt-get clean \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy only requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies with retry mechanism
RUN pip install --upgrade pip && \
    pip install --retries 3 --timeout 60 -r requirements.txt

# Copy project files
COPY . .

# Create necessary directories
RUN mkdir -p /app/static /app/media /app/logs

# Set proper permissions
RUN chmod +x /app/entrypoint.sh /app/healthcheck.sh

# Install curl for health checks
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Expose port 8000 for Django app
EXPOSE 8000

# Run the application
ENTRYPOINT ["/app/entrypoint.sh"]
