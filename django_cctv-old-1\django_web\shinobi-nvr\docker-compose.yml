version: '3.8'

services:
  shinobi_nvr:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: shinobi-nvr
    restart: unless-stopped
    ports:
      - "8080:8080"   # Web UI
      - "9000:9000"   # Streaming / RTSP proxy
    environment:
      - MYSQL_ROOT_PASSWORD=supersecret
      - MYSQL_DATABASE=cc
      - MYSQL_USER=shinobi
      - MYSQL_PASSWORD=shinobi
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=admin
      - STARTUP_ADMIN_USER=true
      - ENABLE_RTSP_SERVER=true
      - RTSP_PORT=9000
      - RTSP_HOST=0.0.0.0
    volumes:
      - ./config:/config
      - ./docker-data:/var/lib/shinobi
      - ./videos:/home/<USER>/videos
      - ./customAutoLoad:/home/<USER>/libs/customAutoLoad
    networks:
      - cctv_net
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    depends_on:
      - shinobi_db

  shinobi_db:
    image: mariadb:10.5
    container_name: shinobi-db
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=shinobiroot
      - MYSQL_DATABASE=ccio
      - MYSQL_USER=majesticflame
      - MYSQL_PASSWORD=shinobi
    volumes:
      - ./shinobi_db_data:/var/lib/mysql
    networks:
      - cctv_net

networks:
  cctv_net:
    external: true
