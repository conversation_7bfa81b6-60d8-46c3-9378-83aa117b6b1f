from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse, FileResponse, Http404, HttpResponseForbidden
from django.views.decorators.http import require_POST, require_GET
from django.views.decorators.csrf import csrf_exempt, csrf_protect # Use proper CSRF protection for all API endpoints in production
from django.utils import timezone
import json
from django.conf import settings # For SHINOBI_URL
import os
import tempfile
import shutil
import subprocess
import stat
import logging

logger = logging.getLogger(__name__)

from .forms import (
    LoginForm, CustomUserCreationForm, CustomUserChangeForm,
    LocationForm, CameraForm, IncidentForm
)
from .models import Location, SystemHealth, Incident, VpnClient, Camera, CustomUser, Role, CameraGroup
from .real_shinobi import ShinobiClient
from .openvpn_manager import OpenVPNManager

OPENVPN_SERVER_CONF = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'openvpn_data', 'server.conf')
OPENVPN_CLIENT_OVPN = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'openvpn_data', 'android_tablet.ovpn')

# Configurable EasyRSA path (default to /usr/share/easy-rsa/easyrsa)
EASYRSA_PATH = os.environ.get('EASYRSA_PATH', '/usr/share/easy-rsa/easyrsa')

def resolve_easyrsa_path():
    """Return a valid EasyRSA path, checking env, then fallback, or raise error."""
    env_path = os.environ.get('EASYRSA_PATH', '/usr/share/easy-rsa/easyrsa')
    fallback_paths = [
        '/usr/local/bin/easyrsa',
        '/usr/bin/easyrsa',
        os.path.join(os.path.dirname(OPENVPN_CLIENT_OVPN), 'easyrsa')
    ]
    for path in [env_path] + fallback_paths:
        if os.path.isfile(path):
            # make sure it is executable; chmod +x once, then test
            try:
                st = os.stat(path)
                if not (st.st_mode & stat.S_IXUSR):
                    os.chmod(path, st.st_mode | stat.S_IXUSR)
            except OSError:
                # silently ignore, the later call will raise anyway
                pass
            if os.access(path, os.X_OK):
                return path
    checked_paths = [env_path] + fallback_paths
    raise FileNotFoundError(f"No valid EasyRSA binary found. Checked: {', '.join(checked_paths)}")

# --- Permission Helper Functions ---
def is_admin_check(user):
    return user.is_authenticated and (user.is_superuser or (user.role and user.role.name == 'Administrator'))

def get_location_id_for_camera(camera):
    """Helper function to get location ID for a camera based on location_name"""
    if not camera.location_name:
        return None
    try:
        location = Location.objects.get(name=camera.location_name)
        return location.id
    except Location.DoesNotExist:
        return None

def can_manage_locations_check(user):
    return user.is_authenticated and (user.is_superuser or (user.role and user.role.name in ["Administrator", "Warehouse Manager"]))

def can_manage_cameras_check(user): # Similar to locations for now
    return user.is_authenticated and (user.is_superuser or (user.role and user.role.name in ["Administrator", "Warehouse Manager"]))

def can_control_recording_check(user):
    return user.is_authenticated and (user.is_superuser or (user.role and user.role.name in ["Administrator", "Warehouse Manager", "Security Operator"]))

# --- Auth Views ---
def index(request):
    if request.user.is_authenticated:
        return redirect('dashboard:dashboard')
    return render(request, 'dashboard/index.html', {'page_name': 'index'})

def login_view(request):
    if request.user.is_authenticated:
        return redirect('dashboard:dashboard')
    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                user.last_login = timezone.now()
                user.save(update_fields=['last_login'])
                if not form.cleaned_data.get('remember'): # Matches form field name
                    request.session.set_expiry(0)
                else:
                    request.session.set_expiry(settings.SESSION_COOKIE_AGE) # Use Django's default
                next_page = request.GET.get('next')
                return redirect(next_page or 'dashboard:dashboard')
            else:
                messages.error(request, 'Invalid username or password.')
    else:
        form = LoginForm()
    return render(request, 'dashboard/login.html', {'form': form, 'page_name': 'login'})

@login_required
def logout_view(request):
    logout(request)
    messages.info(request, 'You have been logged out.')
    return redirect('dashboard:index')

# --- Core Views ---
@login_required
def dashboard_view(request):
    # FORCE DEBUG OUTPUT - Use print to ensure we see output
    print("🔥 DASHBOARD VIEW CALLED!")
    print(f"🔥 User: {request.user.username}")
    print(f"🔥 Is authenticated: {request.user.is_authenticated}")

    user = request.user
    locations_qs = Location.objects.all()
    if is_admin_check(user):
        accessible_locations = locations_qs
    else:
        accessible_locations = user.accessible_locations.all()

    health = SystemHealth.objects.order_by('-last_updated').first()

    # DEEP DEBUG: Investigate camera count issue
    print(f"🔥 DEBUG: Dashboard - Starting camera count investigation for user {user.username}")

    # First, check total cameras in database
    total_cameras = Camera.objects.all()
    print(f"🔥 DEBUG: Dashboard - Total cameras in database: {total_cameras.count()}")

    # Check cameras with Shinobi monitor IDs
    cameras_with_shinobi = Camera.objects.filter(shinobi_monitor_id__isnull=False)
    print(f"🔥 DEBUG: Dashboard - Cameras with Shinobi monitor IDs: {cameras_with_shinobi.count()}")

    # List all cameras for debugging
    for camera in total_cameras:
        print(f"🔥 DEBUG: Dashboard - Camera: {camera.name}, Shinobi ID: {camera.shinobi_monitor_id}, Status: {camera.status}")

    # Check user's camera groups
    user_camera_groups = user.camera_groups.all()
    print(f"🔥 DEBUG: Dashboard - User camera groups: {[g.name for g in user_camera_groups]} (count: {user_camera_groups.count()})")

    # FIXED: Get camera counts using EXACT same approach as cameras_list view
    if is_admin_check(user):
        # Use same filtering as cameras_list - only cameras with Shinobi monitor IDs
        accessible_cameras = Camera.objects.filter(shinobi_monitor_id__isnull=False)
        print(f"🔥 DEBUG: Dashboard - Admin found {accessible_cameras.count()} cameras with Shinobi IDs")
    else:
        # Use same filtering as cameras_list - only cameras from user groups with Shinobi IDs
        accessible_cameras = Camera.objects.filter(
            groups__in=user_camera_groups,
            shinobi_monitor_id__isnull=False
        ).distinct()
        print(f"🔥 DEBUG: Dashboard - User found {accessible_cameras.count()} cameras from groups with Shinobi IDs")

        # Additional debugging for non-admin users
        if user_camera_groups.count() == 0:
            print(f"🔥 DEBUG: Dashboard - User {user.username} has NO camera groups assigned!")
        else:
            # Check cameras in user's groups (without Shinobi filter)
            cameras_in_groups = Camera.objects.filter(groups__in=user_camera_groups).distinct()
            print(f"🔥 DEBUG: Dashboard - User has {cameras_in_groups.count()} cameras in their groups (before Shinobi filter)")

    camera_count = accessible_cameras.count()
    print(f"🔥 DEBUG: Dashboard - Final camera_count: {camera_count}")

    # SIMPLIFIED: Use database status for now (since Shinobi API has issues)
    online_cameras = accessible_cameras.filter(status=Camera.CameraStatus.ONLINE).count()
    print(f"🔥 DEBUG: Dashboard - Using database status: {online_cameras} online cameras out of {camera_count} total")
    
    loc_ids = [loc.id for loc in accessible_locations]
    incidents = Incident.objects.filter(location_id__in=loc_ids).order_by('-created_at')[:5]
    open_incidents_count = Incident.objects.filter(location_id__in=loc_ids, status='open').count()
    online_locations_count = Location.objects.filter(id__in=loc_ids, vpn_status=True).count()

    # Add camera counts to locations
    locations_with_counts = []
    for location in accessible_locations:
        location.camera_count = Camera.objects.filter(location_name=location.name).count()
        locations_with_counts.append(location)

    context = {
        'locations': locations_with_counts, # For the location cards
        'health': health,
        'camera_count': camera_count,
        'online_cameras': online_cameras,
        'offline_cameras': camera_count - online_cameras,
        'incidents': incidents, # For recent incidents table
        'open_incidents': open_incidents_count, # For the overview card
        'total_locations': len(accessible_locations), # For the overview card
        'online_locations': online_locations_count, # For the overview card
        'offline_locations': len(accessible_locations) - online_locations_count, # For the overview card
        'page_name': 'dashboard',
    }
    return render(request, 'dashboard/dashboard.html', context)

# --- Camera Views ---
@login_required
def cameras_list(request):
    """Display live cameras using shared models approach (like django_web)"""
    user = request.user

    # Fetch locations for filter dropdown
    if is_admin_check(user):
        locations = Location.objects.all()
    else:
        locations = user.accessible_locations.all()

    monitors = []
    error_message = None

    logger.info(f"DEBUG: Shinobi CCTV - User {user.username}, Admin: {is_admin_check(user)}")

    # FIXED: Use EXACT same database-first approach as django_web (WORKING APPROACH)
    if is_admin_check(user):
        # Admins can see all cameras with Shinobi IDs
        cameras = Camera.objects.filter(shinobi_monitor_id__isnull=False).order_by('name')
        logger.info(f"DEBUG: Admin - found {cameras.count()} cameras with Shinobi IDs")
    else:
        # Regular users can only see cameras from their assigned groups
        user_camera_groups = user.camera_groups.all()
        logger.info(f"DEBUG: User camera groups: {[g.name for g in user_camera_groups]}")

        cameras = Camera.objects.filter(
            groups__in=user_camera_groups,
            shinobi_monitor_id__isnull=False
        ).distinct().order_by('name')
        logger.info(f"DEBUG: User - found {cameras.count()} cameras from their groups")

    # ENHANCED: Use EXACT same URL generation as django_web with real monitor data
    shinobi_client_url = "http://localhost:8080"
    api_key = getattr(settings, 'SHINOBI_API_KEY', None)
    group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)

    # ENHANCED: Get real monitor data from Shinobi for display details
    logger.info("DEBUG: Shinobi CCTV - Fetching monitor details from Shinobi...")
    shinobi_monitors_data = {}
    try:
        shinobi_client = ShinobiClient(
            api_key=api_key,
            group_key=group_key,
            host=getattr(settings, 'SHINOBI_HOST', 'http://shinobi-nvr:8080')
        )
        success, shinobi_monitors = shinobi_client.get_monitors()
        if success and isinstance(shinobi_monitors, list):
            # Create a lookup dict for quick access
            for monitor in shinobi_monitors:
                mid = monitor.get('mid')
                if mid:
                    shinobi_monitors_data[mid] = monitor
            logger.info(f"DEBUG: Shinobi CCTV - Retrieved {len(shinobi_monitors_data)} monitor details")
        else:
            logger.warning("DEBUG: Shinobi CCTV - Could not fetch monitor details from Shinobi")
    except Exception as e:
        logger.warning(f"DEBUG: Shinobi CCTV - Error fetching monitor details: {str(e)}")

    for camera in cameras:
        mid = camera.shinobi_monitor_id
        if not mid:
            logger.warning(f"DEBUG: Camera {camera.name} has no Shinobi monitor ID, skipping")
            continue

        logger.info(f"DEBUG: Processing camera {camera.name} ({mid})")

        # FIXED: Use EXACT same URL generation as django_web dashboard
        hls_url = f"{shinobi_client_url}/{api_key}/hls/{group_key}/{mid}/s.m3u8"
        preview_url = f"{shinobi_client_url}/{api_key}/jpeg/{group_key}/{mid}/s.jpg"

        # DEBUG: Log the URLs being generated (same as django_web)
        logger.info(f"DEBUG: Camera {camera.name} ({mid})")
        logger.info(f"DEBUG: HLS URL: {hls_url}")
        logger.info(f"DEBUG: Preview URL: {preview_url}")

        # ENHANCED: Get real monitor details from Shinobi data
        shinobi_monitor = shinobi_monitors_data.get(mid, {})

        # Extract real monitor details or use defaults
        fps = shinobi_monitor.get('fps', 'Unknown')
        width = shinobi_monitor.get('width', '?')
        height = shinobi_monitor.get('height', '?')
        protocol = shinobi_monitor.get('protocol', 'Unknown')
        shinobi_mode = shinobi_monitor.get('mode', 'stop')
        shinobi_status = shinobi_monitor.get('status', 'stop')

        # ENHANCED: Use real Shinobi status if available, fallback to database
        if shinobi_monitor:
            # Use live status from Shinobi
            if shinobi_mode == 'start' or shinobi_status == 'start':
                status = 'online'
                mode = 'start'
            elif shinobi_mode == 'record' or shinobi_status == 'record':
                status = 'recording'
                mode = 'record'
            else:
                status = 'offline'
                mode = 'stop'
        else:
            # Fallback to database status
            status = 'online' if camera.status == Camera.CameraStatus.ONLINE else 'offline'
            mode = 'start' if status == 'online' else 'stop'

        # Get location information
        location = camera.location_name or 'Unknown Location'
        location_id = get_location_id_for_camera(camera)

        monitors.append({
            'mid': mid,
            'name': camera.name,
            'location': location,
            'location_id': location_id,
            'status': status,
            'hls_url': hls_url,
            'preview_url': preview_url,
            'mode': mode,
            'fps': fps,
            'width': width,
            'height': height,
            'protocol': protocol,
        })

    logger.info(f"DEBUG: Shinobi CCTV - Returning {len(monitors)} monitors to template")

    # Always provide a camera_form for the modal
    camera_form = CameraForm()
    context = {
        'monitors': monitors,
        'locations': locations,
        'page_name': 'cameras',
        'error_message': error_message,
        'camera_form': camera_form,
    }
    return render(request, 'dashboard/cameras.html', context)

@login_required
@user_passes_test(can_manage_cameras_check)
def camera_add(request):
    if request.method == 'POST':
        form = CameraForm(request.POST)
        if form.is_valid():
            # Instead of saving to Django DB, create a Shinobi monitor
            name = form.cleaned_data['name']
            rtsp_url = form.cleaned_data['rtsp_url']
            shinobi_client = ShinobiClient(
                api_key=getattr(settings, 'SHINOBI_API_KEY', None),
                group_key=getattr(settings, 'SHINOBI_GROUP_KEY', None),
                host=getattr(settings, 'SHINOBI_HOST', None)
            )
            # Generate a unique Shinobi monitor ID (use uuid or similar)
            import uuid
            monitor_id = str(uuid.uuid4())[:12]
            success, result = shinobi_client.add_monitor_by_url(monitor_id, name, rtsp_url)
            if success:
                messages.success(request, f'Camera "{name}" added to Shinobi successfully.')
                # Optionally, create/update a Django Camera object for metadata
                # Camera.objects.create(name=name, rtsp_url=rtsp_url, shinobi_id=monitor_id, ...)
                return redirect('dashboard:cameras')
            else:
                messages.error(request, f'Failed to add camera to Shinobi: {result}')
        else:
            messages.error(request, 'Invalid form data.')
    else:
        form = CameraForm()
        if not is_admin_check(request.user):
            form.fields['location'].queryset = request.user.accessible_locations.all()
    context = {'form': form, 'title': 'Add New Camera', 'page_name': 'cameras'}
    return render(request, 'dashboard/camera_form.html', context)

@login_required
def camera_detail(request, camera_id):
    """Camera detail view using shared models approach (like django_web)"""
    camera = get_object_or_404(Camera, id=camera_id)

    # Check Camera Group access control AND location access
    location_id = get_location_id_for_camera(camera)
    if not (is_admin_check(request.user) or
            (request.user.has_camera_access(camera) and (location_id is None or request.user.has_access_to_location(location_id)))):
        return HttpResponseForbidden("You do not have permission to view this camera.")

    # FIXED: Generate URLs using EXACT same approach as django_web (WORKING APPROACH)
    mid = camera.shinobi_monitor_id
    if mid:
        shinobi_client_url = "http://localhost:8080"
        api_key = getattr(settings, 'SHINOBI_API_KEY', None)
        group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)

        hls_url = f"{shinobi_client_url}/{api_key}/hls/{group_key}/{mid}/s.m3u8"
        preview_url = f"{shinobi_client_url}/{api_key}/jpeg/{group_key}/{mid}/s.jpg"
        embed_url = f"{shinobi_client_url}/{api_key}/mjpeg/{group_key}/{mid}"
    else:
        hls_url = None
        preview_url = None
        embed_url = None

    # DEBUG: Log the URLs being generated
    logger.info(f"DEBUG: Camera detail for {camera.name} ({mid})")
    logger.info(f"DEBUG: HLS URL: {hls_url}")
    logger.info(f"DEBUG: Preview URL: {preview_url}")
    logger.info(f"DEBUG: Embed URL: {embed_url}")

    context = {
        'camera': camera,
        'hls_url': hls_url,
        'preview_url': preview_url,
        'embed_url': embed_url,
        'page_name': 'cameras',
        'config': {
            'SHINOBI_URL': getattr(settings, 'SHINOBI_URL', 'http://localhost:8080'),
            'SHINOBI_CLIENT_URL': getattr(settings, 'SHINOBI_CLIENT_URL', 'http://localhost:8080')
        }
    }
    return render(request, 'dashboard/camera_detail.html', context)

@login_required
@user_passes_test(can_manage_cameras_check)
def camera_edit(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id)
    location_id = get_location_id_for_camera(camera)
    if not (is_admin_check(request.user) or (location_id is None or request.user.has_access_to_location(location_id))): # Check edit permission
        return HttpResponseForbidden("You do not have permission to edit this camera.")

    if request.method == 'POST':
        form = CameraForm(request.POST, instance=camera)
        if form.is_valid():
            form.save()
            messages.success(request, f'Camera "{camera.name}" updated successfully.')
            return redirect('dashboard:cameras')
    else:
        form = CameraForm(instance=camera)
        if not is_admin_check(request.user):
            form.fields['location'].queryset = request.user.accessible_locations.all()
    context = {'form': form, 'title': f'Edit Camera: {camera.name}', 'instance': camera, 'page_name': 'cameras'}
    return render(request, 'dashboard/camera_form.html', context)

@login_required
@require_POST # Ensure this is a POST request
@user_passes_test(can_manage_cameras_check)
def camera_delete(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id)
    location_id = get_location_id_for_camera(camera)
    if not (is_admin_check(request.user) or (location_id is None or request.user.has_access_to_location(location_id))):
        return HttpResponseForbidden("You do not have permission to delete this camera.")
    camera_name = camera.name
    camera.delete()
    messages.success(request, f'Camera "{camera_name}" deleted successfully.')
    return redirect('dashboard:cameras')


# --- Location Views ---
@login_required
@user_passes_test(can_manage_locations_check)
def locations_list(request):
    user = request.user
    if is_admin_check(user):
        locations = Location.objects.all()
    else:
        locations = user.accessible_locations.all()
    context = {'locations': locations, 'page_name': 'locations'}
    return render(request, 'dashboard/locations.html', context)

@login_required
@user_passes_test(is_admin_check) # Only full admins can add locations
def location_add(request):
    if request.method == 'POST':
        form = LocationForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, f'Location "{form.cleaned_data["name"]}" added successfully.')
            return redirect('dashboard:locations')
    else:
        form = LocationForm()
    context = {'form': form, 'title': 'Add New Location', 'page_name': 'locations'}
    return render(request, 'dashboard/location_form.html', context)

@login_required
@user_passes_test(is_admin_check) # Only full admins can edit locations
def location_edit(request, location_id):
    location = get_object_or_404(Location, id=location_id)
    if request.method == 'POST':
        form = LocationForm(request.POST, instance=location)
        if form.is_valid():
            form.save()
            messages.success(request, f'Location "{location.name}" updated successfully.')
            return redirect('dashboard:locations')
    else:
        form = LocationForm(instance=location)
    context = {'form': form, 'title': f'Edit Location: {location.name}', 'instance': location, 'page_name': 'locations'}
    return render(request, 'dashboard/location_form.html', context)

@login_required
@require_POST
@user_passes_test(is_admin_check)
def location_delete(request, location_id):
    location = get_object_or_404(Location, id=location_id)
    location_name = location.name
    location.delete() # Cameras associated will be deleted due to on_delete=models.CASCADE
    messages.success(request, f'Location "{location_name}" and its cameras deleted successfully.')
    return redirect('dashboard:locations')

# --- User Views ---
@login_required
@user_passes_test(is_admin_check)
def users_list(request):
    users = CustomUser.objects.select_related('role').all()
    available_roles = Role.objects.all()
    context = {
        'users': users,
        'available_roles': available_roles,
        'page_name': 'users'
    }
    return render(request, 'dashboard/users.html', context)

@login_required
@user_passes_test(is_admin_check)
def user_add(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save() # This now handles accessible_locations too
            messages.success(request, f'User "{user.username}" created successfully.')
            return redirect('dashboard:users')
    else:
        form = CustomUserCreationForm()
    context = {'form': form, 'title': 'Add New User', 'page_name': 'users'}
    return render(request, 'dashboard/user_form.html', context)

@login_required
@user_passes_test(is_admin_check)
def user_edit(request, user_id):
    user_obj = get_object_or_404(CustomUser, id=user_id)
    if request.method == 'POST':
        form = CustomUserChangeForm(request.POST, instance=user_obj)
        if form.is_valid():
            form.save()
            messages.success(request, f'User "{user_obj.username}" updated successfully.')
            return redirect('dashboard:users')
    else:
        form = CustomUserChangeForm(instance=user_obj)
    context = {'form': form, 'title': f'Edit User: {user_obj.username}', 'instance': user_obj, 'page_name': 'users'}
    return render(request, 'dashboard/user_form.html', context)

@login_required
@require_POST
@user_passes_test(is_admin_check)
def user_delete(request, user_id):
    user_obj = get_object_or_404(CustomUser, id=user_id)
    if user_obj == request.user: # Prevent self-deletion
        messages.error(request, "You cannot delete your own account.")
        return redirect('dashboard:users')
    username = user_obj.username
    user_obj.delete()
    messages.success(request, f'User "{username}" deleted successfully.')
    return redirect('dashboard:users')


# --- Incident Views ---
@login_required
def incidents_list(request):
    user = request.user
    if is_admin_check(user):
        incidents = Incident.objects.select_related('location', 'camera', 'reporter').all()
    else:
        accessible_loc_ids = user.accessible_locations.values_list('id', flat=True)
        incidents = Incident.objects.select_related('location', 'camera', 'reporter').filter(location_id__in=accessible_loc_ids)
    
    context = {'incidents': incidents.order_by('-created_at'), 'page_name': 'incidents'} # Added page_name
    return render(request, 'dashboard/incidents_list.html', context)


@login_required
def incident_add(request):
    if request.method == 'POST':
        form = IncidentForm(request.POST)
        if form.is_valid():
            incident = form.save(commit=False)
            incident.reporter = request.user
            incident.save()
            messages.success(request, f'Incident "{incident.title}" reported successfully.')
            return redirect('dashboard:incidents_list')
    else:
        form = IncidentForm()
        # Filter locations and cameras based on user access
        if not is_admin_check(request.user):
            form.fields['location'].queryset = request.user.accessible_locations.all()
            # Cameras could be filtered dynamically via JS based on selected location
            # Or show all cameras from accessible locations
            accessible_location_names = request.user.accessible_locations.values_list('name', flat=True)
            form.fields['camera'].queryset = Camera.objects.filter(location_name__in=accessible_location_names)
        
    context = {'form': form, 'title': 'Report New Incident', 'page_name': 'incidents'}
    return render(request, 'dashboard/incident_form.html', context)

@login_required
def incident_detail(request, incident_id):
    incident = get_object_or_404(Incident.objects.select_related('location', 'camera', 'reporter'), id=incident_id)
    if not (is_admin_check(request.user) or request.user.has_access_to_location(incident.location.id)):
        return HttpResponseForbidden("Access Denied")
    context = {'incident': incident, 'page_name': 'incidents'}
    return render(request, 'dashboard/incident_detail.html', context)

@login_required
def incident_edit(request, incident_id):
    incident = get_object_or_404(Incident, id=incident_id)
    if not (is_admin_check(request.user) or request.user == incident.reporter or request.user.has_access_to_location(incident.location.id)): # Allow reporter or admin/location manager
        return HttpResponseForbidden("Access Denied")

    if request.method == 'POST':
        form = IncidentForm(request.POST, instance=incident)
        if form.is_valid():
            form.save()
            messages.success(request, f'Incident "{incident.title}" updated successfully.')
            return redirect('dashboard:incidents_list')
    else:
        form = IncidentForm(instance=incident)
        if not is_admin_check(request.user): # Adjust queryset for non-admins
            form.fields['location'].queryset = request.user.accessible_locations.all()
            accessible_location_names = request.user.accessible_locations.values_list('name', flat=True)
            form.fields['camera'].queryset = Camera.objects.filter(location_name__in=accessible_location_names)

    context = {'form': form, 'title': f'Edit Incident: {incident.title}', 'instance': incident, 'page_name': 'incidents'}
    return render(request, 'dashboard/incident_form.html', context)


# --- System Views ---
@login_required
def system_health_view(request):
    health = SystemHealth.objects.order_by('-last_updated').first()
    user = request.user
    if is_admin_check(user):
        locations = Location.objects.all()
        cameras_qs = Camera.objects.all()
    else:
        locations = user.accessible_locations.all()
        location_names = [loc.name for loc in locations]
        cameras_qs = Camera.objects.filter(location_name__in=location_names)

    total_cameras = cameras_qs.count()
    online_cameras = cameras_qs.filter(status='online').count()

    context = {
        'health': health,
        'locations': locations, # For VPN status list
        'total_cameras': total_cameras,
        'online_cameras': online_cameras,
        'offline_cameras': total_cameras - online_cameras,
        'page_name': 'system_health',
    }
    return render(request, 'dashboard/system-health.html', context)

@login_required
@user_passes_test(is_admin_check)
def settings_view(request):
    # Get global VPN clients for the settings page
    global_vpn_clients = VpnClient.objects.filter(location=None).order_by('-created_at')

    context = {
        'page_name': 'settings',
        'global_vpn_clients': global_vpn_clients
    }
    return render(request, 'dashboard/settings.html', context)

# --- API Views ---
@login_required
@require_GET
def api_camera_status(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id)

    # Check Camera Group access control AND location access
    location_id = get_location_id_for_camera(camera)
    if not (is_admin_check(request.user) or
            (request.user.has_camera_access(camera) and (location_id is None or request.user.has_access_to_location(location_id)))):
        return JsonResponse({'error': 'Access denied'}, status=403)
    shinobi_client = ShinobiClient()
    status_ok, status_data = shinobi_client.get_monitors()
    # Find the monitor for this camera by shinobi_id
    monitor = None
    if status_ok and isinstance(status_data, list):
        monitor = next((m for m in status_data if m.get('mid') == camera.shinobi_id), None)
    if monitor:
        camera.status = 'online' if monitor.get('status') == 'start' else 'offline'
        camera.is_recording = monitor.get('details', {}).get('recording') == '1'
        camera.save(update_fields=['status', 'is_recording', 'last_updated'])
        return JsonResponse({
            'id': camera.id, 'name': camera.name, 'status': camera.status,
            'is_recording': camera.is_recording,
            'last_updated': camera.last_updated.strftime('%Y-%m-%d %H:%M:%S')
        })
    return JsonResponse({'error': 'Camera not found in Shinobi'}, status=404)

@login_required
@require_POST
@csrf_exempt
def api_toggle_recording(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id)

    # Check recording permission AND Camera Group access control AND location access
    location_id = get_location_id_for_camera(camera)
    if not can_control_recording_check(request.user) or \
       not (is_admin_check(request.user) or
            (request.user.has_camera_access(camera) and (location_id is None or request.user.has_access_to_location(location_id)))):
        return JsonResponse({'error': 'Permission denied'}, status=403)
    try:
        data = json.loads(request.body)
        action = data.get('action')
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON request'}, status=400)
    shinobi_client = ShinobiClient()
    if action == 'start':
        success, _ = shinobi_client.start_monitor(camera.shinobi_id)
        if success:
            camera.is_recording = True
            camera.save(update_fields=['is_recording', 'last_updated'])
            return JsonResponse({'success': True, 'message': 'Recording started', 'is_recording': True})
    elif action == 'stop':
        success, _ = shinobi_client.stop_monitor(camera.shinobi_id)
        if success:
            camera.is_recording = False
            camera.save(update_fields=['is_recording', 'last_updated'])
            return JsonResponse({'success': True, 'message': 'Recording stopped', 'is_recording': False})
    return JsonResponse({'error': f'Failed to {action} recording via Shinobi'}, status=500)

@login_required
@require_GET
def api_camera_snapshot(request, camera_id):
    # Placeholder: Implement real snapshot logic if available in Shinobi API
    return JsonResponse({'error': 'Snapshot feature not implemented in real Shinobi API integration.'}, status=501)

@login_required
@require_POST
@csrf_exempt
def api_camera_ptz(request, camera_id):
    # Placeholder: Implement real PTZ logic if available in Shinobi API
    return JsonResponse({'error': 'PTZ feature not implemented in real Shinobi API integration.'}, status=501)


@login_required
@require_GET
def api_dashboard_stats(request):
    user = request.user
    if is_admin_check(user):
        accessible_locations = Location.objects.all()
    else:
        accessible_locations = user.accessible_locations.all()

    health = SystemHealth.objects.order_by('-last_updated').first()

    # FIXED: Use EXACT same camera filtering as dashboard_view
    print(f"🔥 API DEBUG: Dashboard stats for user {user.username}")

    if is_admin_check(user):
        # Use same filtering as dashboard_view - only cameras with Shinobi monitor IDs
        cameras_qs = Camera.objects.filter(shinobi_monitor_id__isnull=False)
        print(f"🔥 API DEBUG: Admin found {cameras_qs.count()} cameras with Shinobi IDs")
    else:
        # Use same filtering as dashboard_view - only cameras from user groups with Shinobi IDs
        user_camera_groups = user.camera_groups.all()
        cameras_qs = Camera.objects.filter(
            groups__in=user_camera_groups,
            shinobi_monitor_id__isnull=False
        ).distinct()
        print(f"🔥 API DEBUG: User found {cameras_qs.count()} cameras from groups with Shinobi IDs")

    total_cams = cameras_qs.count()
    online_cams = cameras_qs.filter(status=Camera.CameraStatus.ONLINE).count()
    print(f"🔥 API DEBUG: Returning total_cams={total_cams}, online_cams={online_cams}")
    
    loc_ids = [loc.id for loc in accessible_locations]
    total_locs = len(loc_ids)
    online_locs = Location.objects.filter(id__in=loc_ids, vpn_status=True).count()
    open_incs = Incident.objects.filter(location_id__in=loc_ids, status='open').count()

    return JsonResponse({
        'system': {
            'cpu': health.cpu_usage if health else 25.0,
            'memory': health.memory_usage if health else 40.0,
            'storage': health.storage_usage if health else 60.0,
            'vpn_connections': health.vpn_connections if health else total_locs # Fallback to location count if no health data
        },
        'cameras': {'total': total_cams, 'online': online_cams, 'offline': total_cams - online_cams},
        'locations': {'total': total_locs, 'online': online_locs, 'offline': total_locs - online_locs},
        'incidents': {'open': open_incs}
    })

@login_required
@require_GET
def api_location_cameras(request, location_id):
    location = get_object_or_404(Location, id=location_id)
    if not (is_admin_check(request.user) or request.user.has_access_to_location(location_id)):
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    cameras = Camera.objects.filter(location_name=location.name).values('id', 'name')
    return JsonResponse(list(cameras), safe=False)

@login_required
@require_POST
@csrf_protect
def api_remove_monitor(request, monitor_id):
    shinobi_client = ShinobiClient(
        api_key=getattr(settings, 'SHINOBI_API_KEY', None),
        group_key=getattr(settings, 'SHINOBI_GROUP_KEY', None),
        host=getattr(settings, 'SHINOBI_HOST', None)
    )
    success, result = shinobi_client.delete_monitor(monitor_id)
    if success:
        return JsonResponse({'success': True})
    return JsonResponse({'success': False, 'error': str(result)}, status=400)

def get_client_ovpn_path(location):
    # Use a unique file per location
    return os.path.join(os.path.dirname(OPENVPN_CLIENT_OVPN), f'client_{location.id}.ovpn')

@login_required
@require_POST
@csrf_exempt  # Use csrf_protect if you want to enforce CSRF for AJAX
def api_vpn_config(request):
    """
    API endpoint to update VPN configuration for a location.
    Expects JSON: {"location_id": int, "vpn_enabled": bool, "vpn_server": str, "vpn_client_config": str}
    """
    try:
        data = json.loads(request.body.decode('utf-8'))
        location_id = data.get('location_id')
        vpn_enabled = data.get('vpn_enabled')
        vpn_server = data.get('vpn_server')
        vpn_client_config = data.get('vpn_client_config')
        if not location_id:
            return JsonResponse({'success': False, 'error': 'location_id is required'}, status=400)
        location = get_object_or_404(Location, id=location_id)
        # Only allow admins or users with access
        if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
            return JsonResponse({'success': False, 'error': 'Permission denied'}, status=403)
        # Save VPN config to location (add fields to model if needed)
        location.vpn_enabled = vpn_enabled == 'true' or vpn_enabled is True
        location.vpn_server = vpn_server
        location.vpn_client_config = vpn_client_config
        location.save()
        # Write to per-location client config file (atomic write)
        client_ovpn_path = get_client_ovpn_path(location)
        if vpn_client_config:
            try:
                with tempfile.NamedTemporaryFile('w', delete=False, dir=os.path.dirname(client_ovpn_path)) as tf:
                    tf.write(vpn_client_config)
                    tempname = tf.name
                shutil.move(tempname, client_ovpn_path)
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Failed to write .ovpn: {e}'}, status=500)
        # Reload OpenVPN Docker container after config change
        try:
            os.system('docker compose restart openvpn')
        except Exception as e:
            return JsonResponse({'success': False, 'error': f'Config saved, but failed to reload OpenVPN: {e}'}, status=500)
        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_GET
@csrf_exempt  # Use csrf_protect if you want to enforce CSRF for AJAX
def api_vpn_config_get(request):
    """
    API endpoint to get VPN configuration for a location.
    Expects GET param: location_id
    Returns: {success: True, vpn_enabled, vpn_server, vpn_client_config} or {success: False, error}
    """
    location_id = request.GET.get('location_id')
    if not location_id:
        return JsonResponse({'success': False, 'error': 'location_id is required'}, status=400)
    try:
        location = get_object_or_404(Location, id=location_id)
        if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
            return JsonResponse({'success': False, 'error': 'Permission denied'}, status=403)
        vpn_enabled = location.vpn_enabled
        vpn_server = location.vpn_server
        vpn_client_config = location.vpn_client_config
        # Use per-location file
        client_ovpn_path = get_client_ovpn_path(location)
        if not vpn_client_config:
            try:
                with open(client_ovpn_path, 'r') as f:
                    vpn_client_config = f.read()
            except Exception:
                vpn_client_config = ''
        if not vpn_server:
            try:
                with open(client_ovpn_path, 'r') as f:
                    for line in f:
                        if line.strip().startswith('remote '):
                            vpn_server = line.strip().split(' ', 1)[1]
                            break
            except Exception:
                pass
        return JsonResponse({
            'success': True,
            'vpn_enabled': vpn_enabled,
            'vpn_server': vpn_server,
            'vpn_client_config': vpn_client_config or ''
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_GET
def download_ovpn(request, location_id):
    """
    Streams the correct .ovpn file for the given location as a download (with permission checks).
    """
    location = get_object_or_404(Location, id=location_id)
    # Only allow admins or users with access
    if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
        return HttpResponseForbidden("Permission denied")
    client_ovpn_path = get_client_ovpn_path(location)
    if not os.path.exists(client_ovpn_path):
        raise Http404("VPN config file not found.")
    response = FileResponse(open(client_ovpn_path, 'rb'), as_attachment=True, filename=f'client_{location.id}.ovpn')
    return response

@login_required
@require_POST
@csrf_protect
def api_vpn_create_client(request):
    """
    API endpoint to create a new VPN client config for a location.
    Uses EasyRSA to generate a unique cert/key and .ovpn for the client.
    """
    location_id = request.POST.get('location_id')
    client_name = request.POST.get('client_name') or ...
    if not location_id:
        return JsonResponse({'success': False, 'error': 'location_id is required'}, status=400)
    location = get_object_or_404(Location, id=location_id)
    if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
        return JsonResponse({'success': False, 'error': 'Permission denied'}, status=403)
    # Generate unique CN for cert
    cert_cn = f"{client_name}_{location_id}_{int(timezone.now().timestamp())}"
    # Paths
    easyrsa_dir = os.path.join(os.path.dirname(OPENVPN_CLIENT_OVPN), 'pki')
    ovpn_dir = os.path.join(os.path.dirname(OPENVPN_CLIENT_OVPN))
    ovpn_path = os.path.join(ovpn_dir, f'{cert_cn}.ovpn')
    # Ensure PKI dir exists
    ensure_dir_exists(easyrsa_dir)
    # Call EasyRSA to build client cert/key
    try:
        # Build client cert (no passphrase)
        subprocess.check_call([
            resolve_easyrsa_path(), 'build-client-full', cert_cn, 'nopass'
        ], cwd=os.path.dirname(easyrsa_dir))   # one level up
        # Generate .ovpn file (assume you have a script or template for this)
        gen_ovpn_script = os.path.join(easyrsa_dir, 'gen_ovpn.sh')
        if os.path.exists(gen_ovpn_script):
            subprocess.check_call([gen_ovpn_script, cert_cn, ovpn_path], cwd=easyrsa_dir)
        else:
            # Fallback: copy template and replace CN (not secure, for demo only)
            template_path = os.path.join(ovpn_dir, 'android_tablet.ovpn')
            with open(template_path, 'r') as src, open(ovpn_path, 'w') as dst:
                dst.write(src.read().replace('client', cert_cn, 1))
        # Store in DB
        vpn_client = VpnClient.objects.create(
            location=location,
            user=request.user,
            client_name=client_name,
            cert_cn=cert_cn,
            ovpn_path=ovpn_path,
        )
        return JsonResponse({'success': True, 'client_name': client_name, 'cert_cn': cert_cn, 'client_id': vpn_client.id})
    except subprocess.CalledProcessError as e:
        return JsonResponse({'success': False, 'error': f'EasyRSA error: {e}'}, status=500)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_GET
def download_vpnclient_ovpn(request, client_id):
    """
    Streams the .ovpn file for a specific VPN client (by id) as a download, with permission checks.
    """
    client = get_object_or_404(VpnClient, id=client_id)
    if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
        return HttpResponseForbidden("Permission denied")
    if not os.path.exists(client.ovpn_path):
        raise Http404("VPN client config file not found.")
    response = FileResponse(open(client.ovpn_path, 'rb'), as_attachment=True, filename=os.path.basename(client.ovpn_path))
    return response

@login_required
@require_POST
@csrf_protect
def api_vpn_revoke_client(request):
    """
    API endpoint to revoke a VPN client certificate by client id.
    Marks as revoked and (optionally) calls EasyRSA to revoke the cert.
    """
    client_id = request.POST.get('client_id')
    if not client_id:
        return JsonResponse({'success': False, 'error': 'client_id is required'}, status=400)

    try:
        client_id = int(client_id)
    except (ValueError, TypeError):
        return JsonResponse({'success': False, 'error': 'Invalid client_id'}, status=400)

    client = get_object_or_404(VpnClient, id=client_id)
    if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
        return JsonResponse({'success': False, 'error': 'Permission denied'}, status=403)
    if client.revoked:
        return JsonResponse({'success': False, 'error': 'Client already revoked.'}, status=400)
    try:
        # Use the new OpenVPN manager to revoke certificate
        manager = OpenVPNManager()
        success, message = manager.revoke_client_certificate(client.cert_cn)

        if not success:
            return JsonResponse({'success': False, 'error': message}, status=500)

        # Mark as revoked in database
        client.revoked = True
        client.save()

        return JsonResponse({'success': True})

    except Exception as e:
        logger.error(f"Error revoking VPN client: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_GET
def download_ovpn_global(request):
    """
    Streams the default/global OpenVPN client config file as a download (for global/system-wide use).
    Only admins can access this endpoint.
    """
    if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
        return HttpResponseForbidden("Permission denied")
    # Use the default global client config (e.g., android_tablet.ovpn)
    global_ovpn_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'openvpn_data', 'android_tablet.ovpn')
    if not os.path.exists(global_ovpn_path):
        raise Http404("Global VPN config file not found.")
    response = FileResponse(open(global_ovpn_path, 'rb'), as_attachment=True, filename='global_client.ovpn')
    return response

@login_required
@require_POST
@csrf_protect
def api_vpn_create_client_global(request):
    """
    API endpoint to create a new global VPN client config (not tied to a location).
    Uses OpenVPN manager to generate a unique cert/key and .ovpn for the client.
    """
    client_name = request.POST.get('client_name') or f"global_client_{int(timezone.now().timestamp())}"
    if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
        return JsonResponse({'success': False, 'error': 'Permission denied'}, status=403)

    cert_cn = f"{client_name}_global_{int(timezone.now().timestamp())}"
    ovpn_dir = os.path.join(os.path.dirname(OPENVPN_CLIENT_OVPN))
    ovpn_path = os.path.join(ovpn_dir, f'{cert_cn}.ovpn')

    try:
        # Use the new OpenVPN manager
        manager = OpenVPNManager()

        # Create client certificate
        success, message = manager.create_client_certificate(cert_cn)
        if not success:
            return JsonResponse({'success': False, 'error': message}, status=500)

        # Generate client configuration
        success, message, config_data = manager.generate_client_config(cert_cn)
        if not success:
            return JsonResponse({'success': False, 'error': message}, status=500)

        # Save the configuration file
        with open(ovpn_path, 'w') as f:
            f.write(config_data)

        # Store in database
        vpn_client = VpnClient.objects.create(
            location=None,
            user=request.user,
            client_name=client_name,
            cert_cn=cert_cn,
            ovpn_path=ovpn_path,
        )

        return JsonResponse({'success': True, 'client_name': client_name, 'cert_cn': cert_cn, 'client_id': vpn_client.id})

    except Exception as e:
        logger.error(f"Error creating VPN client: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_GET
def api_vpn_export_clients_global(request):
    """
    Streams a zip file containing all global VPN client .ovpn files (not tied to a location).
    Only admins can access this endpoint.
    """
    import zipfile
    from io import BytesIO
    if not (request.user.is_superuser or (request.user.role and request.user.role.name == 'Administrator')):
        return HttpResponseForbidden("Permission denied")
    global_clients = VpnClient.objects.filter(location=None)
    if not global_clients.exists():
        from django.http import Http404
        raise Http404("No global VPN clients found.")
    zip_buffer = BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w') as zipf:
        for client in global_clients:
            if os.path.exists(client.ovpn_path):
                zipf.write(client.ovpn_path, arcname=os.path.basename(client.ovpn_path))
    zip_buffer.seek(0)
    response = FileResponse(zip_buffer, as_attachment=True, filename='global_vpn_clients.zip')
    return response

def ensure_dir_exists(path):
    """Ensure a directory exists (recursively)."""
    # Validate that path is within expected boundaries
    base_dir = os.path.dirname(OPENVPN_CLIENT_OVPN)
    abs_path = os.path.abspath(path)
    if not abs_path.startswith(os.path.abspath(base_dir)):
        raise ValueError(f"Path '{path}' is outside allowed directory")
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)
