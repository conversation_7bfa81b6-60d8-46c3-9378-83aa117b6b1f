#!/usr/bin/env python3
"""
🎖️ QUICK TEST PERSON SETUP
Add a test person to see face recognition in action
"""

import requests

def add_test_person():
    """Add a test person for face recognition demo"""
    
    print("🎖️ Adding test person for face recognition demo...")
    
    # Test person data
    person_data = {
        "name": "Test User",
        "employee_id": "TEST001",
        "department": "Demo",
        "role": "Test Subject"
    }
    
    try:
        response = requests.post(
            "http://localhost:8090/persons",
            json=person_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Test person added successfully!")
            print(f"   Name: {result['name']}")
            print(f"   ID: {result['id']}")
            print(f"   Employee ID: {result['employee_id']}")
            
            print(f"\n🎯 Now when face recognition runs, it will:")
            print(f"   1. Detect faces in camera streams")
            print(f"   2. Try to match against '{result['name']}'")
            print(f"   3. Show recognition results with confidence scores")
            print(f"   4. Display visual indicators on camera feeds")
            
            return result['id']
        else:
            print(f"❌ Failed to add test person: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error adding test person: {str(e)}")
        return None

def check_service():
    """Check if face recognition service is running"""
    
    try:
        response = requests.get("http://localhost:8090/health", timeout=5)
        if response.status_code == 200:
            print("✅ Face Recognition Service is healthy")
            return True
        else:
            print("❌ Face Recognition Service is not responding")
            return False
    except:
        print("❌ Cannot connect to Face Recognition Service")
        print("   Make sure the service is running: docker-compose ps face-recognition")
        return False

def main():
    """Main function"""
    
    print("🎖️ FACE RECOGNITION TEST SETUP")
    print("=" * 40)
    
    # Check service
    if not check_service():
        return
    
    # Add test person
    person_id = add_test_person()
    
    if person_id:
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Go to: http://localhost:5000/cameras/")
        print(f"2. Click 'Start Recognition' on any camera")
        print(f"3. Point camera at a face (any face)")
        print(f"4. Watch for recognition results!")
        print(f"\n📋 What you'll see:")
        print(f"   • 👁️ 'Face detected' notification for any face")
        print(f"   • 🎭 'RECOGNIZED: Test User' if system matches the face")
        print(f"   • Visual overlay on camera feed with person name")
        print(f"   • Pulsing green border for recognized faces")
        print(f"   • Yellow border for unknown faces")
        
        print(f"\n🎖️ The system is now ready for face recognition testing!")

if __name__ == "__main__":
    main()
