from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User
from .forms import User<PERSON>reationForm, UserChangeForm

class CustomUserAdmin(UserAdmin):
    add_form = UserCreationForm
    form = UserChangeForm
    model = User
    list_display = ('username', 'email', 'role', 'get_camera_groups', 'is_staff', 'is_active')
    list_filter = ('role', 'camera_groups', 'is_staff', 'is_active')

    def get_camera_groups(self, obj):
        """Display camera groups in list view"""
        if obj.is_admin:
            return "All (Admin)"
        groups = obj.camera_groups.all()
        if groups:
            return ", ".join([group.name for group in groups])
        return "None"
    get_camera_groups.short_description = 'Camera Access'
    fieldsets = (
        (None, {'fields': ('username', 'email', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone_number', 'profile_image')}),
        ('Permissions', {'fields': ('role', 'camera_groups', 'is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'role', 'is_staff', 'is_active')}
        ),
    )
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('username',)
    filter_horizontal = ('camera_groups', 'groups', 'user_permissions')

admin.site.register(User, CustomUserAdmin)