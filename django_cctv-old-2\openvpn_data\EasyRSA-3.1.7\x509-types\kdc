# X509 extensions for a KDC server certificate

basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer:always
extendedKeyUsage = *******.*******
keyUsage = nonRepudiation,digitalSignature,keyEncipherment,keyAgreement
issuerAltName = issuer:copy
subjectAltName = otherName:*******.5.2.2;SEQUENCE:kdc_princ_name

[kdc_princ_name]
realm = EXP:0,GeneralString:${ENV::EASYRSA_KDC_REALM}
principal_name = EXP:1,SEQUENCE:kdc_principal_seq

[kdc_principal_seq]
name_type = EXP:0,INTEGER:1
name_string = EXP:1,SEQUENCE:kdc_principals

[kdc_principals]
princ1 = GeneralString:krbtgt
princ2 = GeneralString:${ENV::EASYRSA_KDC_REALM}
