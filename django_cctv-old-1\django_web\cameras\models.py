from django.db import models
from django.utils.translation import gettext_lazy as _

class CameraGroup(models.Model):
    """Group for organizing cameras and managing permissions."""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name
        
    class Meta:
        ordering = ['name']

class Camera(models.Model):
    """Camera model representing a CCTV camera in the system."""
    
    class CameraType(models.TextChoices):
        IP_CAMERA = 'ip', _('IP Camera')
        RTSP = 'rtsp', _('RTSP Stream')
        HTTP = 'http', _('HTTP Stream')
        HLS = 'hls', _('HLS Stream')
    
    class CameraStatus(models.TextChoices):
        ONLINE = 'online', _('Online')
        OFFLINE = 'offline', _('Offline')
        DISABLED = 'disabled', _('Disabled')
        MAINTENANCE = 'maintenance', _('Maintenance')
    
    # Basic information
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    location = models.CharField(max_length=200)
    type = models.CharField(
        max_length=20,
        choices=CameraType.choices,
        default=CameraType.IP_CAMERA
    )
    
    # Connection details
    stream_url = models.CharField(max_length=255)
    username = models.CharField(max_length=100, blank=True, null=True)
    password = models.CharField(max_length=100, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    port = models.IntegerField(default=80)
    
    # Status and metadata
    status = models.CharField(
        max_length=20,
        choices=CameraStatus.choices,
        default=CameraStatus.OFFLINE
    )
    last_online = models.DateTimeField(blank=True, null=True)
    
    # Shinobi integration
    shinobi_monitor_id = models.CharField(max_length=100, blank=True, null=True)
    
    # Camera grouping
    groups = models.ManyToManyField(CameraGroup, related_name='cameras', blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.location})"
        
    class Meta:
        ordering = ['name']
        
    @property
    def thumbnail_url(self):
        """Return the URL for the camera thumbnail."""
        if not self.shinobi_monitor_id:
            return None

        from .shinobi_client import shinobi_client
        return shinobi_client.get_snapshot_url(self.shinobi_monitor_id)

    @property
    def live_stream_url(self):
        """Return the URL for the live stream."""
        if not self.shinobi_monitor_id:
            return None

        from .shinobi_client import shinobi_client
        return shinobi_client.get_stream_url(self.shinobi_monitor_id)

    @property
    def shinobi_embed_url(self):
        """Return the Shinobi embed URL for iframe integration."""
        if not self.shinobi_monitor_id:
            return None

        from django.conf import settings
        group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)
        if not group_key:
            return None

        return f"{settings.SHINOBI_API_URL}/{group_key}/embed/{self.shinobi_monitor_id}"

    def get_shinobi_monitor_data(self):
        """Get monitor data from Shinobi API"""
        if not self.shinobi_monitor_id:
            return None

        from .shinobi_client import shinobi_client
        success, data = shinobi_client.get_monitor(self.shinobi_monitor_id)
        return data if success else None

    def update_status_from_shinobi(self):
        """Update camera status based on Shinobi monitor status"""
        monitor_data = self.get_shinobi_monitor_data()
        if monitor_data:
            # Map Shinobi status to our status
            shinobi_mode = monitor_data.get('mode', 'stop')
            if shinobi_mode in ['start', 'record']:
                self.status = self.CameraStatus.ONLINE
            else:
                self.status = self.CameraStatus.OFFLINE

            self.save(update_fields=['status'])
            return True
        return False

class CameraPreset(models.Model):
    """Presets for camera positions (for PTZ cameras)."""
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='presets')
    name = models.CharField(max_length=100)
    position_data = models.JSONField()
    
    def __str__(self):
        return f"{self.camera.name} - {self.name}"
        
    class Meta:
        ordering = ['camera', 'name']
        unique_together = ['camera', 'name']

class CameraEvent(models.Model):
    """Events detected by cameras (motion, objects, etc.)."""
    
    class EventType(models.TextChoices):
        MOTION = 'motion', _('Motion Detected')
        OBJECT = 'object', _('Object Detected')
        FACE = 'face', _('Face Detected')
        CUSTOM = 'custom', _('Custom Event')
        OFFLINE = 'offline', _('Camera Offline')
        ONLINE = 'online', _('Camera Online')
    
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(
        max_length=20,
        choices=EventType.choices,
        default=EventType.MOTION
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    snapshot_url = models.URLField(blank=True, null=True)
    details = models.JSONField(blank=True, null=True)
    
    def __str__(self):
        return f"{self.camera.name} - {self.get_event_type_display()} at {self.timestamp}"
        
    class Meta:
        ordering = ['-timestamp']