# Django Services Shared Database Configuration

## ✅ Configuration Complete!

Both `django_web` and `shinobi_cctv_django` services have been successfully configured to:
- **Use the same PostgreSQL database** (`warehouse_shinobi`)
- **Run on different ports** (8000 and 5000 respectively)
- **Have separate session management** to prevent interference

## 📋 Changes Made

### 1. Database Configuration

**django_web/cctv_project/settings.py**:
- ✅ Changed from SQLite to PostgreSQL
- ✅ Added environment variable support for database configuration
- ✅ Added custom session cookie names (`django_web_sessionid`, `django_web_csrftoken`)

**django_web/.env**:
- ✅ Enabled PostgreSQL database settings
- ✅ Set to use shared database `warehouse_shinobi`

**shinobi_cctv_django/shinobi_cctv_django/settings.py**:
- ✅ Added custom session cookie names (`shinobi_cctv_sessionid`, `shinobi_cctv_csrftoken`)
- ✅ Already configured for PostgreSQL

### 2. Docker Configuration

**docker-compose.yml**:
- ✅ Added database dependency for `django_web` service
- ✅ Confirmed port mappings: 8000 for django_web, 5000 for shinobi_cctv_django

### 3. Session Isolation

Both services now use different session cookie names to prevent interference:
- **django_web**: `django_web_sessionid` and `django_web_csrftoken`
- **shinobi_cctv_django**: `shinobi_cctv_sessionid` and `shinobi_cctv_csrftoken`

## 🚀 How to Start the Services

### Option 1: Using the Setup Script (Linux/Mac)
```bash
./setup_services.sh
```

### Option 2: Manual Setup (Windows/All Platforms)
```bash
# 1. Start all services
docker-compose up -d

# 2. Wait for database to be ready (about 30 seconds)

# 3. Run migrations for both services
docker-compose exec web python manage.py migrate
docker-compose exec shinobi_cctv_django python manage.py migrate

# 4. Collect static files
docker-compose exec web python manage.py collectstatic --noinput
docker-compose exec shinobi_cctv_django python manage.py collectstatic --noinput --clear

# 5. Create superusers (optional)
docker-compose exec web python manage.py createsuperuser
docker-compose exec shinobi_cctv_django python manage.py createsuperuser
```

## 🌐 Service Access

| Service | URL | Port | Admin URL |
|---------|-----|------|-----------|
| django_web | http://localhost:8000 | 8000 | http://localhost:8000/admin/ |
| shinobi_cctv_django | http://localhost:5000 | 5000 | http://localhost:5000/admin/ |
| pgAdmin | http://localhost:5050 | 5050 | <EMAIL> / admin123 |

## 🗄️ Database Information

- **Database Type**: PostgreSQL 15
- **Database Name**: `warehouse_shinobi`
- **Host**: `db` (container name)
- **Port**: 5432
- **Username**: `user`
- **Password**: `admin`

## 🔧 Verification

### Test Service Availability
```bash
# Test django_web
curl http://localhost:8000

# Test shinobi_cctv_django
curl http://localhost:5000
```

### Run Verification Script
```bash
python verify_setup.py
```

### Check Database Connection
```bash
# For django_web
docker-compose exec web python test_database_connection.py

# For shinobi_cctv_django
docker-compose exec shinobi_cctv_django python test_database_connection.py
```

## 🔍 Troubleshooting

### Database Connection Issues
```bash
# Check database status
docker-compose ps db

# Check database logs
docker-compose logs db

# Test database connectivity
docker-compose exec db pg_isready -U user -d warehouse_shinobi
```

### Service Issues
```bash
# Check service logs
docker-compose logs web
docker-compose logs shinobi_cctv_django

# Restart services
docker-compose restart web shinobi_cctv_django
```

### Migration Issues
```bash
# Check migration status
docker-compose exec web python manage.py showmigrations
docker-compose exec shinobi_cctv_django python manage.py showmigrations

# Force migrations if needed
docker-compose exec web python manage.py migrate --fake-initial
```

## ✨ Key Benefits

1. **Shared Data**: Both services can access the same database for shared functionality
2. **Isolated Sessions**: Users can be logged into both services simultaneously without conflicts
3. **Different Ports**: Services are clearly separated and can be accessed independently
4. **Scalable**: Easy to add more Django services using the same pattern

## 📝 Important Notes

- Both services use different user models (`users.User` vs `dashboard.CustomUser`)
- Session cookies have different names to prevent interference
- Static files are managed separately for each service
- Database tables are prefixed by Django app names to prevent conflicts

The configuration is now complete and ready for use! 🎉
