from django.urls import path
from dashboard import views

app_name = 'dashboard'

urlpatterns = [
    # Auth
    path('', views.index, name='index'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),

    # Core
    path('dashboard/', views.dashboard_view, name='dashboard'),

    path('cameras/', views.cameras_list, name='cameras'),
    path('camera/add/', views.camera_add, name='camera_add'),
    path('camera/<int:camera_id>/', views.camera_detail, name='camera_detail'),
    path('camera/<int:camera_id>/edit/', views.camera_edit, name='camera_edit'),
    path('camera/<int:camera_id>/delete/', views.camera_delete, name='camera_delete'),


    path('locations/', views.locations_list, name='locations'),
    path('location/add/', views.location_add, name='location_add'),
    path('location/<int:location_id>/edit/', views.location_edit, name='location_edit'),
    path('location/<int:location_id>/delete/', views.location_delete, name='location_delete'),

    path('users/', views.users_list, name='users'),
    path('user/add/', views.user_add, name='user_add'),
    path('user/<int:user_id>/edit/', views.user_edit, name='user_edit'),
    path('user/<int:user_id>/delete/', views.user_delete, name='user_delete'), # Placeholder for delete

    path('incidents/', views.incidents_list, name='incidents_list'),
    path('incident/add/', views.incident_add, name='incident_add'),
    path('incident/<int:incident_id>/', views.incident_detail, name='incident_detail'),
    path('incident/<int:incident_id>/edit/', views.incident_edit, name='incident_edit'),


    # System
    path('system-health/', views.system_health_view, name='system_health'),
    path('settings/', views.settings_view, name='settings'), # Mostly a static page for now

    # API Endpoints
    path('api/camera/<int:camera_id>/status/', views.api_camera_status, name='api_camera_status'),
    path('api/camera/<int:camera_id>/recording/', views.api_toggle_recording, name='api_toggle_recording'),
    path('api/camera/<int:camera_id>/snapshot/', views.api_camera_snapshot, name='api_camera_snapshot'),
    path('api/camera/<int:camera_id>/ptz/', views.api_camera_ptz, name='api_camera_ptz'),
    path('api/dashboard/stats/', views.api_dashboard_stats, name='api_dashboard_stats'),
    path('api/locations/<int:location_id>/cameras/', views.api_location_cameras, name='api_location_cameras'), # For dynamic camera loading in forms
    path('api/remove_monitor/<str:monitor_id>/', views.api_remove_monitor, name='api_remove_monitor'),
    path('api/vpn-config/', views.api_vpn_config, name='api_vpn_config'),
    path('api/vpn-config-get/', views.api_vpn_config_get, name='api_vpn_config_get'),
    path('api/vpn-create-client/', views.api_vpn_create_client, name='api_vpn_create_client'),
    path('api/vpn-revoke-client/', views.api_vpn_revoke_client, name='api_vpn_revoke_client'),

    path('locations/<int:location_id>/download_ovpn/', views.download_ovpn,
         name='download_location_ovpn'),   # single canonical path
    path('api/vpn-create-client-global/', views.api_vpn_create_client_global, name='vpn_create_client_global'),
    path('vpn-client/<int:client_id>/download_ovpn/', views.download_vpnclient_ovpn, name='download_vpnclient_ovpn'),
    path('api/vpn-export-clients-global/', views.api_vpn_export_clients_global, name='vpn_export_clients_global'),
    path('api/vpn-revoke-client/', views.api_vpn_revoke_client, name='vpn_revoke_client'),

    path('vpn/download/', views.download_ovpn_global, name='download_ovpn_global'),
]
