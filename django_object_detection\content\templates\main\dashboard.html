{% extends 'layouts/default/page.html' %}
{% load bootstrap4 i18n %}
{% load static %}
{% block content %}
<style>
    .camera-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }
    .camera-card {
        background: #1a1a1a;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        cursor: pointer;
    }
    .video-wrapper {
        margin-top: 10px; /* Ensure the video feed isn't covered by the dropdown */
        position: relative;
        background-color: #000;
        width: 100%;
        height: 100%;  /* Adjust as needed */
        aspect-ratio: 16/9;
    }
    .video-feed {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: opacity 0.3s ease;
    }

    .video-updating {
        opacity: 0.5;
    }
    .camera-name {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    .status-indicator {
        width: 10px;
        height: 10px;
        background: gray;
        border-radius: 50%;
        display: inline-block;
        transition: background-color 0.3s ease;
    }
    /* Camera Controls Wrapper */
    .camera-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 10px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.7); /* Change this to be more transparent */
        color: white;
        font-size: 0.9rem;
    }

    /* Left section (Status + Camera Name) */
    .camera-info {
        display: flex;
        align-items: center;
        gap: 8px; /* Space between indicator and text */
    }

    /* Right section (Counter & Detection Toggles) */
    .toggle-group {
        display: flex;
        align-items: center;
        gap: 15px; /* Space between toggles and labels */
    }

    /* Toggle Switch Styling */
    .switch {
        position: relative;
        display: inline-block;
        width: 34px;
        height: 20px;
    }

    .switch input { 
        opacity: 0;
        width: 0;
        height: 0;
    }
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }
    .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    input:checked + .slider {
        background-color: #0f0;
    }
    input:checked + .slider:before {
        transform: translateX(14px);
    }
    /* Style for the label selector dropdown */
    .label-selector {
        position: absolute; /* Position it relative to the camera card */
        top: 0px;          /* Adjust this value to set it at the top */
        left: 0px;         /* Adjust this value to set it at the left */
        z-index: 10;        /* Ensure it's above the video wrapper */
        background-color: rgba(0, 0, 0, 0.3);  /* Optional: for contrast */
        padding: 5px;
        border-radius: 5px;
        width: 100%;  /* Adjust width as needed */
    }
    .label-dropdown {
        background-color: rgba(0, 0, 0, 0.3); /* Transparent background */
        color: white; /* Text color */
        border: 1px solid rgba(0, 0, 0, 0.7); /* Light border */
    }

    .label-dropdown option {
        background-color: rgba(50, 50, 50, 0.3); /* Semi-transparent background */
        color: white; /* Keep text visible */
    }

    .label-dropdown option:checked {
        background-color: rgba(0, 0, 0, 0.3) !important; /* Transparent orange */
        color: white;
    }
</style>
<div class="bg-dark">
    <div class="container-fluid">
        <!-- Branch Selection -->
        <div class="form-group">
            <label for="branchSelect" class="text-light">Select Warehouse Branch:</label>
            <select id="branchSelect" class="form-control">
                {% if request.user.is_superuser%}
                    <option value="">All Warehouse Branches</option>
                {% endif %}
                {% for branch in branches %}
                    <option value="{{ branch.id }}" 
                            {% if branch.id|stringformat:"s" == selected_branch_id %}selected{% endif %}>
                        {{ branch.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <!-- Camera Grid -->
        <div class="camera-grid">
            {% for camera in cameras %}
            <div class="camera-card" data-camera-id="{{ camera.id }}">
                <!-- Label Selector (Only Visible When a Branch is Selected) -->
                {% if selected_branch_id %}
                <div class="label-selector">
                    <label for="labelSelect_{{ camera.id }}" class="text-light">Select Labels:</label>
                    <select id="labelSelect_{{ camera.id }}" class="form-control label-dropdown" multiple size="3">
                        {% for class_id, label in yolo_labels.items %}
                            <option value="{{ label }}" selected>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}
                <div class="video-wrapper">
                    <img class="video-feed" 
                        src="{% url 'video_feed' camera.branch.id camera.id %}" 
                        alt="{{ camera.name }}"
                        loading="lazy"
                        data-offline-src="{% static 'offline.png' %}">
                    <video id="cameraFeed_{{ camera.id }}" autoplay></video>
                </div>                
                <!-- Camera Controls (Bottom Row) -->
                <div class="camera-controls">
                    <!-- Left Side: Status + Camera Name -->
                    <div class="camera-info">
                        <span class="status-indicator {% if camera.enable_face_detection %}active{% endif %}"></span>
                        <span>{{ camera.name }} ({{ camera.device_index }})</span>
                    </div>

                    <!-- Counter Toggle -->
                    {% if selected_branch_id %}
                    <div class="counter-toggle">
                        <label style="color: orange;">Enable Counting</label>
                        <label class="switch">
                            <input type="checkbox" class="toggle-counter" data-camera-id="{{ camera.id }}"
                            {% if camera.settings.counter %}checked{% endif %}>
                            <span class="slider round"></span>
                        </label>

                    <label style="color: orange;">Enable AI Model</label>
                    <!-- Face Detection Toggle -->
                    <label class="switch">
                        <input type="checkbox" class="toggle-detection"
                            data-camera-id="{{ camera.id }}"
                            {% if camera.settings.face_detection %}checked{% endif %}>
                        <span class="slider round"></span>
                    </label>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>
</div>
<script>
document.getElementById("branchSelect").addEventListener("change", function () {
    let branchId = this.value;
    let currentUrl = new URL(window.location.href);
    
    if (currentUrl.searchParams.get("branch") !== branchId) {
        let newUrl = branchId ? `?branch=${branchId}` : window.location.pathname;
        window.location.href = newUrl;
    }
});

// Face Detection Toggle
document.querySelectorAll('.toggle-detection').forEach(toggle => {
    toggle.addEventListener('change', async function () {
        const cameraId = this.dataset.cameraId;
        const enabled = this.checked;
        const cameraCard = this.closest('.camera-card');
        const videoFeed = cameraCard.querySelector('.video-feed');
        const labelDropdown = cameraCard.querySelector('.label-dropdown');
        const counterToggle = cameraCard.querySelector('.toggle-counter').checked;

        let selectedLabels = labelDropdown ? Array.from(labelDropdown.selectedOptions).map(opt => opt.value) : [];

        try {
            const response = await fetch(`/toggle-detection/${cameraId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ enable: enabled, labels: selectedLabels, counter: counterToggle })
            });

            if (response.ok) {
                const data = await response.json();
                videoFeed.src = data.new_url + `?t=${Date.now()}`;

                const indicator = cameraCard.querySelector('.status-indicator');
                indicator.classList.toggle('active', enabled);
            } else {
                throw new Error('Failed to update detection status');
            }
        } catch (error) {
            this.checked = !enabled;
            alert(error.message || 'Network error - please try again');
        }
    });
});

document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.video-feed').forEach(img => {
        img.onload = () => {
            let indicator = img.closest('.camera-card').querySelector('.status-indicator');
            if (indicator) indicator.style.backgroundColor = '#0f0'; // Green for active feed
        };
        
        img.onerror = function () {
            let indicator = this.closest('.camera-card').querySelector('.status-indicator');
            if (indicator) indicator.style.backgroundColor = '#f00'; // Red for offline feed

            // Replace the broken image with offline.png
            this.onerror = null;
            this.src = this.dataset.offlineSrc;
        };
    });
});

// Ensure dropdown remains visible in fullscreen mode
document.querySelectorAll('.video-wrapper').forEach(wrapper => {
    wrapper.addEventListener('click', (event) => {
        event.stopPropagation(); // Prevents unintended bubbling

        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            if (wrapper.requestFullscreen) {
                wrapper.requestFullscreen();
            } else if (wrapper.webkitRequestFullscreen) {
                wrapper.webkitRequestFullscreen();
            } else if (wrapper.msRequestFullscreen) {
                wrapper.msRequestFullscreen();
            }
        }
    });
});
</script>
{% endblock content %}