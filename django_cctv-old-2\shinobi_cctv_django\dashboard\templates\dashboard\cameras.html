{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}Cameras - ABC CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Camera Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="toggle-grid-size">
                <i class="bi bi-grid-3x3-gap-fill"></i> Larger Grid
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-cameras">
                <span class="spinner-border spinner-border-sm d-none" id="refresh-spinner" role="status" aria-hidden="true"></span>
                <i class="bi bi-arrow-clockwise"></i> <span id="refresh-text">Refresh</span>
            </button>
        </div>
        <!--
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCameraModal">
            <i class="bi bi-plus-lg"></i> Add Camera
        </button>
        -->
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">Filter by Location</span>
            <select class="form-select" id="location-filter" title="Filter by Location">
                <option value="all" selected>All Locations</option>
                {% for location in locations %}
                <option value="{{ location.id }}">{{ location.name }}</option>
                {% endfor %}
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">Filter by Status</span>
            <select class="form-select" id="status-filter" title="Filter by Status">
                <option value="all" selected>All Statuses</option>
                <option value="online">Online</option>
                <option value="offline">Offline</option>
            </select>
            <input type="text" class="form-control" placeholder="Search cameras..." id="camera-search">
        </div>
    </div>
</div>

<div class="camera-grid">
    {% if error_message %}
        <div class="alert alert-danger" role="alert">
            <strong>Error fetching monitors:</strong> {{ error_message }}
        </div>
    {% elif monitors %}
        {% for monitor in monitors %}
        <div class="camera-card bg-light text-dark border rounded shadow-sm mb-4" data-camera-id="{{ monitor.mid }}" data-status="{{ monitor.status }}" data-location-id="{{ monitor.location_id }}">
            <div class="camera-feed">
                <div class="ratio ratio-16x9 bg-dark rounded-top">
                    <video id="video-{{ monitor.mid }}" width="320" height="240" controls muted autoplay poster="{{ monitor.preview_url }}" class="rounded-top">
                        <source src="{{ monitor.hls_url }}" type="application/x-mpegURL">
                        <span class="text-light">Your browser does not support the video tag.</span>
                    </video>
                </div>
                <!-- Enhanced Status indicator with animations -->
                <span class="camera-status status-{{ monitor.status }} px-2 py-1 rounded position-absolute top-0 end-0 m-2 fw-bold transition-all {% if monitor.status == 'online' %}bg-success text-light{% else %}bg-danger text-light{% endif %}" data-status-indicator="{{ monitor.mid }}">
                    {% if monitor.status == 'online' %}
                        <span class="d-inline-flex align-items-center">
                            <span class="bg-light rounded-circle me-1 pulse-dot" style="width: 8px; height: 8px;"></span>
                            Online
                        </span>
                    {% else %}
                        {{ monitor.status|title }}
                    {% endif %}
                </span>

                <!-- Network Status Overlay (hidden by default) -->
                <div id="network-overlay-{{ monitor.mid }}" class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-75 d-flex align-items-center justify-content-center text-center text-light d-none transition-all" style="z-index: 20;">
                    <div class="network-message p-4">
                        <div class="mb-3">
                            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <h5 class="fw-semibold mb-2">Reconnecting...</h5>
                        <p class="text-light-emphasis small" id="reconnect-message-{{ monitor.mid }}">Attempting to restore connection</p>
                        <button type="button" onclick="manualRetry('{{ monitor.mid }}')" class="btn btn-primary btn-sm mt-2">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            Retry Now
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body bg-white text-dark rounded-bottom">
                <h5 class="card-title fw-semibold text-primary">{{ monitor.name }}</h5>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-sm btn-danger remove-monitor-btn" data-monitor-id="{{ monitor.mid }}">
                        <i class="bi bi-trash"></i> Remove
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12 text-center py-5">
            <h4 class="text-muted">No cameras found</h4>
            <p class="text-secondary">There are no cameras configured for your accessible locations.</p>
            {% if request.user.role.name == "Administrator" or request.user.role.name == "Warehouse Manager" %}
            <!-- Add Camera button removed: Only Shinobi cameras are managed. -->
            {% endif %}
        </div>
    {% endif %}
</div>

<!--
The Add Camera modal and button are now fully deprecated. Only real Shinobi cameras are shown and managed.
-->

{% endblock %}

{% block extrahead %}
    {{ monitors|json_script:"monitors-data" }}
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
<script>
    // 🌐 LEGENDARY NETWORK RESILIENCE SYSTEM - SHINOBI EDITION 🌐
    class ShinobiNetworkResilienceManager {
        constructor() {
            this.connectionStatus = 'online';
            this.retryAttempts = new Map();
            this.maxRetries = 5;
            this.baseRetryDelay = 1000; // 1 second
            this.hlsInstances = new Map();
            this.videoElements = new Map();
            this.cameraCards = [];
            this.init();
        }

        init() {
            this.setupNetworkMonitoring();
            this.setupVideoMonitoring();
            this.setupPeriodicHealthCheck();
            this.createGlobalNetworkIndicator();
            this.addCustomStyles();
            console.log('🎖️ Shinobi Network Resilience Manager initialized');
        }

        addCustomStyles() {
            const style = document.createElement('style');
            style.textContent = `
                .pulse-dot {
                    animation: pulse-dot 2s infinite;
                }
                @keyframes pulse-dot {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.5; }
                }
                .transition-all {
                    transition: all 0.3s ease;
                }
                .bg-opacity-75 {
                    background-color: rgba(0, 0, 0, 0.75) !important;
                }
                .text-light-emphasis {
                    color: rgba(255, 255, 255, 0.75) !important;
                }
                #global-network-indicator {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1050;
                    border-radius: 20px;
                    font-size: 0.875rem;
                    transition: all 0.3s ease;
                }
            `;
            document.head.appendChild(style);
        }

        setupNetworkMonitoring() {
            // Monitor online/offline events
            window.addEventListener('online', () => {
                this.handleConnectionRestore();
            });

            window.addEventListener('offline', () => {
                this.handleConnectionLoss();
            });

            // Monitor network quality changes
            if ('connection' in navigator) {
                navigator.connection.addEventListener('change', () => {
                    this.handleConnectionChange();
                });
            }
        }

        setupVideoMonitoring() {
            const videos = document.querySelectorAll('video');
            videos.forEach(video => {
                const cameraId = video.id.replace('video-', '');
                this.videoElements.set(cameraId, video);

                // Enhanced video event monitoring
                video.addEventListener('error', () => this.handleVideoError(cameraId));
                video.addEventListener('loadstart', () => this.updateCameraStatus(cameraId, 'loading'));
                video.addEventListener('canplay', () => this.handleVideoSuccess(cameraId));
                video.addEventListener('stalled', () => this.updateCameraStatus(cameraId, 'buffering'));
                video.addEventListener('waiting', () => this.updateCameraStatus(cameraId, 'buffering'));
                video.addEventListener('emptied', () => this.handleVideoError(cameraId));
            });
        }

        setupPeriodicHealthCheck() {
            // Check connection health every 15 seconds
            setInterval(() => {
                this.performHealthCheck();
            }, 15000);
        }

        createGlobalNetworkIndicator() {
            // Create global network status indicator
            const indicator = document.createElement('div');
            indicator.id = 'global-network-indicator';
            indicator.className = 'badge text-white d-none';
            document.body.appendChild(indicator);
        }

        async performHealthCheck() {
            try {
                const response = await fetch(window.location.pathname, {
                    method: 'HEAD',
                    cache: 'no-cache'
                });

                if (response.ok) {
                    this.updateGlobalStatus('online');
                } else {
                    this.updateGlobalStatus('degraded');
                }
            } catch (error) {
                this.updateGlobalStatus('offline');
            }
        }

        handleConnectionLoss() {
            this.updateGlobalStatus('offline');
            this.showNotification('🌐 Connection lost. Attempting to reconnect...', 'danger');
            this.startGlobalReconnection();
        }

        handleConnectionRestore() {
            this.updateGlobalStatus('online');
            this.showNotification('🌐 Connection restored!', 'success');
            this.retryAllFailedVideos();
        }

        handleConnectionChange() {
            if ('connection' in navigator) {
                const connection = navigator.connection;
                const quality = this.getConnectionQuality(connection);
                this.adaptToConnectionQuality(quality);
            }
        }

        getConnectionQuality(connection) {
            const downlink = connection.downlink || 0;
            if (downlink >= 10) return 'excellent';
            if (downlink >= 5) return 'good';
            if (downlink >= 1) return 'fair';
            return 'poor';
        }

        adaptToConnectionQuality(quality) {
            if (quality === 'poor') {
                this.showNotification('🌐 Poor connection detected. Optimizing streams...', 'warning');
            }
        }

        handleVideoError(cameraId) {
            console.log(`🚨 Video error for camera ${cameraId}`);
            this.updateCameraStatus(cameraId, 'error');
            this.showNetworkOverlay(cameraId, true);
            this.scheduleVideoRetry(cameraId);
        }

        handleVideoSuccess(cameraId) {
            this.updateCameraStatus(cameraId, 'online');
            this.showNetworkOverlay(cameraId, false);
            this.retryAttempts.delete(cameraId);
        }

        scheduleVideoRetry(cameraId) {
            const attempts = this.retryAttempts.get(cameraId) || 0;

            if (attempts >= this.maxRetries) {
                this.updateCameraStatus(cameraId, 'failed');
                this.updateReconnectMessage(cameraId, `Failed after ${this.maxRetries} attempts`);
                return;
            }

            const delay = this.baseRetryDelay * Math.pow(2, attempts); // Exponential backoff
            this.retryAttempts.set(cameraId, attempts + 1);

            this.updateReconnectMessage(cameraId, `Retry ${attempts + 1}/${this.maxRetries} in ${Math.ceil(delay/1000)}s...`);

            setTimeout(() => {
                this.retryVideoConnection(cameraId);
            }, delay);
        }

        retryVideoConnection(cameraId) {
            const video = this.videoElements.get(cameraId);
            if (video) {
                this.updateCameraStatus(cameraId, 'reconnecting');
                this.updateReconnectMessage(cameraId, 'Reconnecting...');

                // Reload HLS if available
                const hls = this.hlsInstances.get(cameraId);
                if (hls) {
                    hls.destroy();
                    this.setupHLSForVideo(video, cameraId);
                } else {
                    video.load();
                }
            }
        }

        retryAllFailedVideos() {
            this.retryAttempts.clear();
            this.videoElements.forEach((video, cameraId) => {
                const status = this.getCameraStatus(cameraId);
                if (status === 'error' || status === 'failed') {
                    this.retryVideoConnection(cameraId);
                }
            });
        }

        updateCameraStatus(cameraId, status) {
            const statusIndicator = document.querySelector(`[data-status-indicator="${cameraId}"]`);
            if (statusIndicator) {
                // Update Bootstrap classes
                statusIndicator.className = statusIndicator.className.replace(/bg-(success|danger|warning|info|secondary)/, this.getBootstrapStatusClass(status));
                statusIndicator.innerHTML = this.getStatusText(status);
            }
        }

        getBootstrapStatusClass(status) {
            switch (status) {
                case 'online': return 'bg-success';
                case 'loading': return 'bg-warning';
                case 'buffering': return 'bg-info';
                case 'reconnecting': return 'bg-warning';
                case 'error': return 'bg-danger';
                case 'failed': return 'bg-secondary';
                default: return 'bg-secondary';
            }
        }

        getStatusText(status) {
            switch (status) {
                case 'online':
                    return '<span class="d-inline-flex align-items-center"><span class="bg-light rounded-circle me-1 pulse-dot" style="width: 8px; height: 8px;"></span>Online</span>';
                case 'loading':
                    return '<span class="d-inline-flex align-items-center"><div class="spinner-border spinner-border-sm me-1" role="status"></div>Loading</span>';
                case 'buffering':
                    return '<span class="d-inline-flex align-items-center"><div class="spinner-border spinner-border-sm me-1" role="status"></div>Buffering</span>';
                case 'reconnecting':
                    return '<span class="d-inline-flex align-items-center"><div class="spinner-border spinner-border-sm me-1" role="status"></div>Reconnecting</span>';
                case 'error':
                    return 'Connection Lost';
                case 'failed':
                    return 'Failed';
                default:
                    return 'Unknown';
            }
        }

        getCameraStatus(cameraId) {
            const statusIndicator = document.querySelector(`[data-status-indicator="${cameraId}"]`);
            if (statusIndicator) {
                if (statusIndicator.className.includes('bg-success')) return 'online';
                if (statusIndicator.className.includes('bg-danger')) return 'error';
                if (statusIndicator.className.includes('bg-warning')) return 'loading';
                if (statusIndicator.className.includes('bg-info')) return 'buffering';
                if (statusIndicator.className.includes('bg-secondary')) return 'failed';
            }
            return 'unknown';
        }

        showNetworkOverlay(cameraId, show) {
            const overlay = document.getElementById(`network-overlay-${cameraId}`);
            if (overlay) {
                if (show) {
                    overlay.classList.remove('d-none');
                } else {
                    overlay.classList.add('d-none');
                }
            }
        }

        updateReconnectMessage(cameraId, message) {
            const messageElement = document.getElementById(`reconnect-message-${cameraId}`);
            if (messageElement) {
                messageElement.textContent = message;
            }
        }

        updateGlobalStatus(status) {
            this.connectionStatus = status;
            const indicator = document.getElementById('global-network-indicator');
            if (indicator) {
                switch (status) {
                    case 'online':
                        indicator.className = indicator.className.replace(/bg-(success|warning|danger)/, 'bg-success');
                        indicator.textContent = '🌐 Online';
                        indicator.classList.add('d-none');
                        break;
                    case 'degraded':
                        indicator.className = indicator.className.replace(/bg-(success|warning|danger)/, 'bg-warning');
                        indicator.textContent = '🌐 Slow Connection';
                        indicator.classList.remove('d-none');
                        break;
                    case 'offline':
                        indicator.className = indicator.className.replace(/bg-(success|warning|danger)/, 'bg-danger');
                        indicator.textContent = '🌐 Offline';
                        indicator.classList.remove('d-none');
                        break;
                }
            }
        }

        startGlobalReconnection() {
            const reconnectInterval = setInterval(() => {
                if (navigator.onLine) {
                    this.performHealthCheck();
                    if (this.connectionStatus === 'online') {
                        clearInterval(reconnectInterval);
                    }
                }
            }, 2000);
        }

        setupHLSForVideo(video, cameraId) {
            const source = video.querySelector('source');
            if (source && source.src) {
                if (Hls.isSupported()) {
                    const hls = new Hls({
                        enableWorker: false,
                        lowLatencyMode: true,
                        backBufferLength: 90
                    });

                    hls.loadSource(source.src);
                    hls.attachMedia(video);
                    this.hlsInstances.set(cameraId, hls);

                    hls.on(Hls.Events.ERROR, (event, data) => {
                        if (data.fatal) {
                            this.handleVideoError(cameraId);
                        }
                    });
                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                    video.src = source.src;
                }
            }
        }

        showNotification(message, type = 'info') {
            // Create Bootstrap toast notification
            const toastContainer = this.getOrCreateToastContainer();
            const toast = document.createElement('div');
            const bgClass = type === 'success' ? 'bg-success' : type === 'danger' ? 'bg-danger' : type === 'warning' ? 'bg-warning' : 'bg-info';

            toast.className = `toast align-items-center text-white ${bgClass} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            toastContainer.appendChild(toast);

            // Initialize and show toast
            const bsToast = new bootstrap.Toast(toast, { delay: 5000 });
            bsToast.show();

            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', () => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });
        }

        getOrCreateToastContainer() {
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                container.style.zIndex = '1055';
                document.body.appendChild(container);
            }
            return container;
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Initialize Shinobi Network Resilience Manager
        const networkManager = new ShinobiNetworkResilienceManager();

        // Enhanced HLS setup with network resilience
        const cameraCards = Array.from(document.querySelectorAll('.camera-card'));
        networkManager.cameraCards = cameraCards;

        cameraCards.forEach(card => {
            const video = card.querySelector('video');
            const cameraId = video.id.replace('video-', '');
            networkManager.setupHLSForVideo(video, cameraId);
        });

        // Enhanced remove button logic with network awareness
        document.querySelectorAll('.remove-monitor-btn').forEach(function(btn) {
            btn.addEventListener('click', function() {
                const monitorId = this.getAttribute('data-monitor-id');
                const card = document.querySelector(`.camera-card[data-camera-id='${monitorId}']`);
                if (card) {
                    card.style.display = 'none';
                    networkManager.showNotification(`📹 Camera ${monitorId} hidden from view`, 'info');
                }
            });
        });

        // Enhanced filter and search logic
        const locationFilter = document.getElementById('location-filter');
        const statusFilter = document.getElementById('status-filter');
        const cameraSearch = document.getElementById('camera-search');

        function filterCameras() {
            const locationValue = locationFilter.value;
            const statusValue = statusFilter.value;
            const searchValue = cameraSearch.value.toLowerCase();
            let visibleCount = 0;

            cameraCards.forEach(card => {
                // Only show if not hidden by Remove
                if (card.style.display === 'none') return;
                const cardLocationId = card.getAttribute('data-location-id');
                const cardStatus = card.getAttribute('data-status');
                const cardName = card.querySelector('.card-title').textContent.toLowerCase();
                const matchesLocation = locationValue === 'all' || cardLocationId === locationValue;
                const matchesStatus = statusValue === 'all' || cardStatus === statusValue;
                const matchesSearch = !searchValue || cardName.includes(searchValue);
                if (matchesLocation && matchesStatus && matchesSearch) {
                    card.style.display = '';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Show filter results notification
            if (searchValue || locationValue !== 'all' || statusValue !== 'all') {
                networkManager.showNotification(`🔍 Showing ${visibleCount} camera(s)`, 'info');
            }
        }

        locationFilter.addEventListener('change', filterCameras);
        statusFilter.addEventListener('change', filterCameras);
        cameraSearch.addEventListener('input', filterCameras);

        // Enhanced refresh button with network awareness
        const refreshButton = document.getElementById('refresh-cameras');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                const spinner = document.getElementById('refresh-spinner');
                const text = document.getElementById('refresh-text');

                if (spinner) spinner.classList.remove('d-none');
                if (text) text.textContent = 'Refreshing...';

                networkManager.showNotification('🔄 Refreshing camera feeds...', 'info');

                setTimeout(() => {
                    location.reload();
                }, 1000);
            });
        }

        // Initial filter
        filterCameras();

        // Make networkManager globally available
        window.networkManager = networkManager;
    });

    // Global functions for manual retry and enhanced controls
    function manualRetry(cameraId) {
        if (window.networkManager) {
            window.networkManager.retryVideoConnection(cameraId);
            window.networkManager.showNotification('🔄 Manual retry initiated...', 'info');
        }
    }

    // Enhanced refresh function for individual cameras
    function refreshCamera(cameraId) {
        if (window.networkManager) {
            window.networkManager.retryVideoConnection(cameraId);
            window.networkManager.showNotification('🔄 Refreshing camera feed...', 'info');
        }
    }

    // Global network status check function
    function checkNetworkStatus() {
        if (window.networkManager) {
            window.networkManager.performHealthCheck();
            window.networkManager.showNotification('🌐 Checking network status...', 'info');
        }
    }
</script>
{% endblock %}