#!/bin/sh
# Custom init.sh to bypass internal DB setup and use conf.json

echo "[Custom Init] Starting Shinobi with PM2 using custom_pm2.yml..."
echo "[Custom Init] Ensuring /home/<USER>/conf.json is used."

# The CMD from the original Docker image is ["pm2-docker","pm2.yml"]
# We assume WORKDIR is /home/<USER>
# Based on find results, /home/<USER>/pm2.yml seems correct.
exec pm2-docker /home/<USER>/custom_pm2.yml
