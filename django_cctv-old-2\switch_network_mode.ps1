# Network Mode Switcher for CCTV System (PowerShell)
# Supports dual-mode operation: Standalone + Organization VPN

param(
    [Parameter(Position=0)]
    [string]$Mode = "help"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Detect-NetworkInterfaces {
    Write-Status "🔍 Detecting network interfaces..."
    
    Write-Host "📡 Available Network Interfaces:"
    Get-NetAdapter | Where-Object {$_.Status -eq "Up"} | ForEach-Object {
        Write-Host "  - $($_.Name) ($($_.InterfaceDescription))"
    }
    
    Write-Host ""
    Write-Host "🌐 IP Addresses:"
    Get-NetIPAddress | Where-Object {$_.AddressFamily -eq "IPv4" -and $_.IPAddress -ne "127.0.0.1"} | ForEach-Object {
        Write-Host "  - $($_.IPAddress) ($($_.InterfaceAlias))"
    }
}

function Test-VPNConnection {
    Write-Status "🔍 Checking VPN connection..."
    
    # Check for VPN adapters
    $vpnAdapters = Get-NetAdapter | Where-Object {$_.Name -match "VPN|TAP|TUN" -or $_.InterfaceDescription -match "VPN|TAP|TUN"}
    
    # Check for VPN IP ranges (**********/12)
    $vpnIPs = Get-NetIPAddress | Where-Object {$_.IPAddress -match "^172\.(1[6-9]|2[0-9]|3[01])\."}
    
    if ($vpnAdapters.Count -gt 0 -or $vpnIPs.Count -gt 0) {
        Write-Success "✅ VPN connection detected"
        return $true
    } else {
        Write-Warning "⚠️ No VPN connection detected"
        return $false
    }
}

function Update-EnvFiles {
    param([string]$NetworkMode)
    
    Write-Status "📝 Updating environment files for mode: $NetworkMode"
    
    # Update django_web .env
    if (Test-Path "django_web\.env") {
        $content = Get-Content "django_web\.env"
        $content = $content -replace "NETWORK_MODE=.*", "NETWORK_MODE=$NetworkMode"
        
        switch ($NetworkMode) {
            "organization_vpn" {
                $content = $content -replace "ORGANIZATION_VPN_ENABLED=.*", "ORGANIZATION_VPN_ENABLED=true"
            }
            "hybrid" {
                $content = $content -replace "ORGANIZATION_VPN_ENABLED=.*", "ORGANIZATION_VPN_ENABLED=true"
            }
            default {
                $content = $content -replace "ORGANIZATION_VPN_ENABLED=.*", "ORGANIZATION_VPN_ENABLED=false"
            }
        }
        
        Set-Content "django_web\.env" $content
        Write-Success "✅ Updated django_web\.env"
    }
    
    # Update shinobi_cctv_django .env
    if (Test-Path "shinobi_cctv_django\.env") {
        $content = Get-Content "shinobi_cctv_django\.env"
        $content = $content -replace "NETWORK_MODE=.*", "NETWORK_MODE=$NetworkMode"
        
        switch ($NetworkMode) {
            "organization_vpn" {
                $content = $content -replace "ORGANIZATION_VPN_ENABLED=.*", "ORGANIZATION_VPN_ENABLED=true"
            }
            "hybrid" {
                $content = $content -replace "ORGANIZATION_VPN_ENABLED=.*", "ORGANIZATION_VPN_ENABLED=true"
            }
            default {
                $content = $content -replace "ORGANIZATION_VPN_ENABLED=.*", "ORGANIZATION_VPN_ENABLED=false"
            }
        }
        
        Set-Content "shinobi_cctv_django\.env" $content
        Write-Success "✅ Updated shinobi_cctv_django\.env"
    }
}

function Restart-Services {
    Write-Status "🔄 Restarting services..."
    
    # Restart specific services that need network reconfiguration
    docker-compose restart shinobi-nvr django_web shinobi_cctv_django
    
    Write-Success "✅ Services restarted"
}

function Test-NetworkConnectivity {
    Write-Status "🧪 Testing network connectivity..."
    
    # Test local network
    if (Test-Connection -ComputerName "192.168.1.1" -Count 1 -Quiet) {
        Write-Success "✅ Local network connectivity: OK"
    } else {
        Write-Warning "⚠️ Local network connectivity: FAILED"
    }
    
    # Test VPN network (if in VPN mode)
    if (Test-VPNConnection) {
        # Try to ping a common VPN gateway
        if (Test-Connection -ComputerName "**********" -Count 1 -Quiet) {
            Write-Success "✅ VPN network connectivity: OK"
        } else {
            Write-Warning "⚠️ VPN network connectivity: FAILED"
        }
    }
    
    # Test services
    Start-Sleep -Seconds 5  # Wait for services to start
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5 -UseBasicParsing
        Write-Success "✅ Shinobi NVR: OK"
    } catch {
        Write-Warning "⚠️ Shinobi NVR: Not responding"
    }
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 5 -UseBasicParsing
        Write-Success "✅ Django Web: OK"
    } catch {
        Write-Warning "⚠️ Django Web: Not responding"
    }
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5000" -TimeoutSec 5 -UseBasicParsing
        Write-Success "✅ Shinobi CCTV Django: OK"
    } catch {
        Write-Warning "⚠️ Shinobi CCTV Django: Not responding"
    }
}

function Show-Status {
    Write-Host ""
    Write-Host "🎖️ CCTV System Network Status" -ForegroundColor $Green
    Write-Host "================================"
    
    Detect-NetworkInterfaces
    Write-Host ""
    
    # Show current mode from env files
    if (Test-Path "django_web\.env") {
        $envContent = Get-Content "django_web\.env"
        $currentMode = ($envContent | Where-Object {$_ -match "NETWORK_MODE="}) -replace "NETWORK_MODE=", ""
        $vpnEnabled = ($envContent | Where-Object {$_ -match "ORGANIZATION_VPN_ENABLED="}) -replace "ORGANIZATION_VPN_ENABLED=", ""
        
        Write-Host "📋 Current Configuration:"
        Write-Host "  - Network Mode: $currentMode"
        Write-Host "  - VPN Enabled: $vpnEnabled"
    }
    
    Write-Host ""
    Test-VPNConnection
    Write-Host ""
    
    # Show service status
    Write-Host "🐳 Docker Services:"
    docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
}

# Main script logic
switch ($Mode.ToLower()) {
    "standalone" {
        Write-Host "🏠 Switching to Standalone Mode..." -ForegroundColor $Green
        Write-Host "=================================="
        Detect-NetworkInterfaces
        Update-EnvFiles "standalone"
        Restart-Services
        Test-NetworkConnectivity
        Write-Success "🎉 Successfully switched to Standalone Mode"
    }
    "organization" {
        Write-Host "🏢 Switching to Organization VPN Mode..." -ForegroundColor $Green
        Write-Host "========================================"
        Detect-NetworkInterfaces
        
        if (!(Test-VPNConnection)) {
            Write-Error "❌ No VPN connection detected. Please connect to organization VPN first."
            exit 1
        }
        
        Update-EnvFiles "organization_vpn"
        Restart-Services
        Test-NetworkConnectivity
        Write-Success "🎉 Successfully switched to Organization VPN Mode"
    }
    "hybrid" {
        Write-Host "🔄 Switching to Hybrid Mode..." -ForegroundColor $Green
        Write-Host "============================="
        Detect-NetworkInterfaces
        
        if (!(Test-VPNConnection)) {
            Write-Warning "⚠️ No VPN connection detected. Hybrid mode will work with local network only."
        }
        
        Update-EnvFiles "hybrid"
        Restart-Services
        Test-NetworkConnectivity
        Write-Success "🎉 Successfully switched to Hybrid Mode"
    }
    "detect" {
        Write-Host "🔍 Auto-detecting Network Mode..." -ForegroundColor $Green
        Write-Host "================================="
        Detect-NetworkInterfaces
        
        if (Test-VPNConnection) {
            Write-Status "🎯 VPN detected - switching to Hybrid Mode"
            Update-EnvFiles "hybrid"
        } else {
            Write-Status "🎯 No VPN detected - switching to Standalone Mode"
            Update-EnvFiles "standalone"
        }
        
        Restart-Services
        Test-NetworkConnectivity
        Write-Success "🎉 Auto-detection and configuration completed"
    }
    "status" {
        Show-Status
    }
    "test" {
        Write-Host "🧪 Testing Network Connectivity..." -ForegroundColor $Green
        Write-Host "================================="
        Test-NetworkConnectivity
    }
    default {
        Write-Host "🎖️ CCTV System Network Mode Switcher" -ForegroundColor $Green
        Write-Host "===================================="
        Write-Host ""
        Write-Host "Usage: .\switch_network_mode.ps1 {standalone|organization|hybrid|detect|status|test}"
        Write-Host ""
        Write-Host "🏠 standalone    - Use only local network (192.168.x.x)"
        Write-Host "🏢 organization  - Use only organization VPN (172.16.x.x)"
        Write-Host "🔄 hybrid        - Use both local and VPN networks"
        Write-Host "🔍 detect        - Auto-detect and configure best mode"
        Write-Host "📊 status        - Show current network status"
        Write-Host "🧪 test          - Test network connectivity"
        Write-Host ""
        Write-Host "Examples:"
        Write-Host "  .\switch_network_mode.ps1 detect                    # Auto-detect and configure"
        Write-Host "  .\switch_network_mode.ps1 organization              # Switch to VPN mode"
        Write-Host "  .\switch_network_mode.ps1 hybrid                    # Enable dual-mode"
        Write-Host "  .\switch_network_mode.ps1 status                    # Show current status"
        Write-Host ""
    }
}
