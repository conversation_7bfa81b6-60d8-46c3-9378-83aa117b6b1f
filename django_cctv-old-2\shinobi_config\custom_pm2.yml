apps:
  - script   : '/home/<USER>/camera.js'
    name     : 'camera'
    cwd      : '/home/<USER>/' # Explicitly set Current Working Directory
    kill_timeout  : 5000
    env      :
      DB_HOST     : "shinobi_db"
      DB_USER     : "shinobi"
      DB_PASSWORD : "shinobi_password"
      DB_DATABASE : "shinobi_db" # Using DB_DATABASE as it's common
      DB_PORT     : 3306
      DB_TYPE     : "mysql"
  - script   : '/home/<USER>/cron.js'
    name     : 'cron'
    cwd      : '/home/<USER>/' # Explicitly set Current Working Directory
    kill_timeout  : 5000
    env      :
      DB_HOST     : "shinobi_db"
      DB_USER     : "shinobi"
      DB_PASSWORD : "shinobi_password"
      DB_DATABASE : "shinobi_db"
      DB_PORT     : 3306
      DB_TYPE     : "mysql"
