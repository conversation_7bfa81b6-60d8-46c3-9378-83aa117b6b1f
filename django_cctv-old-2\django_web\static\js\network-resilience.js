/**
 * Network Resilience Manager for CCTV System
 * Handles automatic reconnection, retry logic, and network failure recovery
 */

class NetworkResilienceManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.reconnectAttempts = new Map();
        this.maxRetries = 5;
        this.baseDelay = 1000; // 1 second
        this.maxDelay = 30000; // 30 seconds
        this.healthCheckInterval = 10000; // 10 seconds
        this.videoStreams = new Map();
        this.statusIndicators = new Map();
        
        this.init();
    }
    
    init() {
        this.setupNetworkListeners();
        this.startHealthCheck();
        this.setupVideoStreamMonitoring();
        this.setupServiceWorker();
        
        console.log('🛡️ Network Resilience Manager initialized');
    }
    
    setupNetworkListeners() {
        // Browser online/offline events
        window.addEventListener('online', () => {
            this.handleNetworkOnline();
        });
        
        window.addEventListener('offline', () => {
            this.handleNetworkOffline();
        });
        
        // Page visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.handlePageVisible();
            }
        });
        
        // Beforeunload for cleanup
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }
    
    handleNetworkOnline() {
        this.isOnline = true;
        this.showNotification('🌐 Network connection restored', 'success');
        this.reconnectAllStreams();
        this.resetRetryCounters();
    }
    
    handleNetworkOffline() {
        this.isOnline = false;
        this.showNotification('📡 Network connection lost - attempting to reconnect...', 'warning');
        this.markAllStreamsAsOffline();
    }
    
    handlePageVisible() {
        // Refresh streams when user returns to tab
        if (this.isOnline) {
            this.refreshAllStreams();
        }
    }
    
    setupVideoStreamMonitoring() {
        // Monitor all video elements
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
            this.monitorVideoStream(video);
        });
        
        // Monitor for new videos added dynamically
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.tagName === 'VIDEO') {
                        this.monitorVideoStream(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }
    
    monitorVideoStream(video) {
        const cameraId = this.extractCameraId(video);
        if (!cameraId) return;
        
        this.videoStreams.set(cameraId, {
            element: video,
            lastError: null,
            retryCount: 0,
            isHealthy: true
        });
        
        // Video error handling
        video.addEventListener('error', () => {
            this.handleVideoError(cameraId);
        });
        
        // Video load success
        video.addEventListener('loadstart', () => {
            this.handleVideoLoadStart(cameraId);
        });
        
        video.addEventListener('canplay', () => {
            this.handleVideoCanPlay(cameraId);
        });
        
        // HLS.js error handling if available
        if (video.hls) {
            video.hls.on(Hls.Events.ERROR, (event, data) => {
                if (data.fatal) {
                    this.handleVideoError(cameraId, data);
                }
            });
        }
    }
    
    extractCameraId(video) {
        const id = video.id;
        if (id && id.startsWith('video-')) {
            return id.replace('video-', '');
        }
        return null;
    }
    
    handleVideoError(cameraId, errorData = null) {
        const stream = this.videoStreams.get(cameraId);
        if (!stream) return;
        
        stream.isHealthy = false;
        stream.lastError = errorData || new Date();
        
        console.log(`📹 Video error for camera ${cameraId}:`, errorData);
        
        // Show error overlay
        this.showVideoError(cameraId);
        
        // Update status indicator
        this.updateStatusIndicator(cameraId, 'error');
        
        // Schedule retry if network is online
        if (this.isOnline) {
            this.scheduleVideoRetry(cameraId);
        }
    }
    
    handleVideoLoadStart(cameraId) {
        this.updateStatusIndicator(cameraId, 'loading');
        console.log(`📹 Video loading started for camera ${cameraId}`);
    }
    
    handleVideoCanPlay(cameraId) {
        const stream = this.videoStreams.get(cameraId);
        if (stream) {
            stream.isHealthy = true;
            stream.retryCount = 0;
        }
        
        this.hideVideoError(cameraId);
        this.updateStatusIndicator(cameraId, 'online');
        console.log(`📹 Video ready for camera ${cameraId}`);
    }
    
    scheduleVideoRetry(cameraId) {
        const stream = this.videoStreams.get(cameraId);
        if (!stream || stream.retryCount >= this.maxRetries) {
            if (stream && stream.retryCount >= this.maxRetries) {
                this.updateStatusIndicator(cameraId, 'failed');
                this.showNotification(`📹 Camera ${cameraId} failed after ${this.maxRetries} attempts`, 'error');
            }
            return;
        }
        
        const delay = this.calculateRetryDelay(stream.retryCount);
        stream.retryCount++;
        
        console.log(`🔄 Scheduling retry ${stream.retryCount}/${this.maxRetries} for camera ${cameraId} in ${delay}ms`);
        
        setTimeout(() => {
            this.retryVideoStream(cameraId);
        }, delay);
    }
    
    calculateRetryDelay(retryCount) {
        // Exponential backoff with jitter
        const exponentialDelay = Math.min(this.baseDelay * Math.pow(2, retryCount), this.maxDelay);
        const jitter = Math.random() * 1000; // Add up to 1 second jitter
        return exponentialDelay + jitter;
    }
    
    retryVideoStream(cameraId) {
        const stream = this.videoStreams.get(cameraId);
        if (!stream || !this.isOnline) return;
        
        console.log(`🔄 Retrying video stream for camera ${cameraId}`);
        
        this.updateStatusIndicator(cameraId, 'connecting');
        
        // Reload video
        const video = stream.element;
        if (video) {
            // For HLS streams
            if (video.hls) {
                video.hls.destroy();
                this.initializeHLS(video);
            } else {
                // For regular video streams
                video.load();
            }
        }
    }
    
    initializeHLS(video) {
        if (!Hls.isSupported()) return;
        
        const hls = new Hls({
            enableWorker: true,
            lowLatencyMode: true,
            backBufferLength: 90
        });
        
        const source = video.querySelector('source');
        if (source) {
            hls.loadSource(source.src);
            hls.attachMedia(video);
            video.hls = hls;
        }
    }
    
    showVideoError(cameraId) {
        const errorOverlay = document.getElementById(`video-error-${cameraId}`);
        if (errorOverlay) {
            errorOverlay.classList.remove('hidden');
        }
    }
    
    hideVideoError(cameraId) {
        const errorOverlay = document.getElementById(`video-error-${cameraId}`);
        if (errorOverlay) {
            errorOverlay.classList.add('hidden');
        }
    }
    
    updateStatusIndicator(cameraId, status) {
        const indicator = document.querySelector(`[data-status-indicator="${cameraId}"]`);
        if (!indicator) return;
        
        // Remove all status classes
        indicator.className = indicator.className.replace(/bg-(green|red|yellow|gray)-\d+/g, '');
        
        let statusClass, statusText;
        
        switch (status) {
            case 'online':
                statusClass = 'bg-green-500 text-white';
                statusText = '<span class="animate-pulse w-2 h-2 bg-white rounded-full mr-1"></span>Online';
                break;
            case 'loading':
            case 'connecting':
                statusClass = 'bg-yellow-500 text-black';
                statusText = '<span class="animate-spin w-2 h-2 border border-black rounded-full mr-1"></span>Connecting...';
                break;
            case 'error':
                statusClass = 'bg-red-500 text-white';
                statusText = 'Connection Lost';
                break;
            case 'failed':
                statusClass = 'bg-gray-500 text-white';
                statusText = 'Failed';
                break;
            default:
                statusClass = 'bg-gray-500 text-white';
                statusText = 'Unknown';
        }
        
        indicator.className += ` ${statusClass}`;
        indicator.innerHTML = statusText;
    }
    
    startHealthCheck() {
        setInterval(() => {
            this.performHealthCheck();
        }, this.healthCheckInterval);
    }
    
    async performHealthCheck() {
        if (!this.isOnline) return;
        
        try {
            // Check main services
            const services = [
                { name: 'Django Web', url: '/api/health/' },
                { name: 'Shinobi NVR', url: '/api/shinobi/health/' }
            ];
            
            for (const service of services) {
                try {
                    const response = await fetch(service.url, { 
                        method: 'GET',
                        timeout: 5000 
                    });
                    
                    if (!response.ok) {
                        console.warn(`⚠️ ${service.name} health check failed: ${response.status}`);
                    }
                } catch (error) {
                    console.warn(`⚠️ ${service.name} unreachable:`, error);
                }
            }
        } catch (error) {
            console.warn('⚠️ Health check failed:', error);
        }
    }
    
    reconnectAllStreams() {
        console.log('🔄 Reconnecting all video streams...');
        
        this.videoStreams.forEach((stream, cameraId) => {
            if (!stream.isHealthy) {
                stream.retryCount = 0; // Reset retry count
                this.retryVideoStream(cameraId);
            }
        });
    }
    
    refreshAllStreams() {
        console.log('🔄 Refreshing all video streams...');
        
        this.videoStreams.forEach((stream, cameraId) => {
            this.retryVideoStream(cameraId);
        });
    }
    
    markAllStreamsAsOffline() {
        this.videoStreams.forEach((stream, cameraId) => {
            stream.isHealthy = false;
            this.updateStatusIndicator(cameraId, 'error');
        });
    }
    
    resetRetryCounters() {
        this.videoStreams.forEach((stream) => {
            stream.retryCount = 0;
        });
    }
    
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/js/sw.js')
                .then(() => console.log('🔧 Service Worker registered'))
                .catch(err => console.log('⚠️ Service Worker registration failed:', err));
        }
    }
    
    showNotification(message, type = 'info') {
        // Use existing notification system
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`📢 ${message}`);
        }
    }
    
    cleanup() {
        // Clean up resources
        this.videoStreams.clear();
        this.statusIndicators.clear();
    }
}

// Global instance
window.networkResilience = new NetworkResilienceManager();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NetworkResilienceManager;
}
