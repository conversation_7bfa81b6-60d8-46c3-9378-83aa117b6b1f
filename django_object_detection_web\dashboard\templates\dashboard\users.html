{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}User Management - ABC CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">User Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-users">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
        <a href="{% url 'dashboard:user_add' %}" class="btn btn-sm btn-primary">
            <i class="bi bi-person-plus"></i> Add User
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">Filter by Role</span>
            <select class="form-select" id="role-filter">
                <option value="all" selected>All Roles</option>
                {% for role in available_roles %} {# Assuming available_roles is passed in context #}
                    <option value="{{ role.name }}">{{ role.name }}</option>
                {% empty %}
                    <option value="Administrator">Administrator</option>
                    <option value="Warehouse Manager">Warehouse Manager</option>
                    <option value="Security Operator">Security Operator</option>
                    <option value="Viewer">Viewer</option>
                {% endfor %}
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" placeholder="Search users..." id="user-search">
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user_obj in users %} {# Changed user to user_obj to avoid conflict with request.user #}
                    <tr data-user-id="{{ user_obj.id }}" data-role="{{ user_obj.role.name|default:'' }}">
                        <td>{{ user_obj.id }}</td>
                        <td>{{ user_obj.first_name }} {{ user_obj.last_name }}</td>
                        <td>{{ user_obj.username }}</td>
                        <td>{{ user_obj.email }}</td>
                        <td>
                            {% if user_obj.role %}
                            <span class="badge bg-{% if user_obj.role.name == 'Administrator' %}primary{% elif user_obj.role.name == 'Warehouse Manager' %}success{% elif user_obj.role.name == 'Security Operator' %}info{% else %}secondary{% endif %}">
                                {{ user_obj.role.name }}
                            </span>
                            {% else %}
                            <span class="badge bg-light text-dark">No Role</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge {% if user_obj.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                {% if user_obj.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </td>
                        <td>{{ user_obj.last_login|date:"Y-m-d H:i"|default:"Never" }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'dashboard:user_edit' user_id=user_obj.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% if request.user != user_obj %} {# Prevent self-action for delete #}
                                <form method="POST" action="{% url 'dashboard:user_delete' user_id=user_obj.id %}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete user {{ user_obj.username }}?');">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                                {% else %}
                                 <button type="button" class="btn btn-sm btn-outline-secondary" disabled>
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                                </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">No users found</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleFilter = document.getElementById('role-filter');
    const userSearch = document.getElementById('user-search');
    const userRows = document.querySelectorAll('tbody tr[data-user-id]');
    
    function filterUsers() {
        const roleValue = roleFilter.value;
        const searchValue = userSearch.value.toLowerCase();
        
        userRows.forEach(row => {
            const rowRole = row.dataset.role;
            const rowText = row.textContent.toLowerCase();
            
            const matchesRole = roleValue === 'all' || rowRole === roleValue;
            const matchesSearch = searchValue === '' || rowText.includes(searchValue);
            
            if (matchesRole && matchesSearch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    if(roleFilter) roleFilter.addEventListener('change', filterUsers);
    if(userSearch) userSearch.addEventListener('input', filterUsers);
    
    const refreshButton = document.getElementById('refresh-users');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            // In a real app, this would refresh the data, e.g., reload the page or fetch via API
            window.location.reload(); 
        });
    }
});
</script>
{% endblock %}