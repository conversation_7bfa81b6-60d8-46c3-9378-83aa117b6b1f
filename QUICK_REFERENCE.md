# 🎖️ Face Recognition Quick Reference

## ⚡ **INSTANT ACCESS GUIDE**

---

## 🚀 **Service URLs**

| Service | URL | Purpose |
|---------|-----|---------|
| **🧠 Face Recognition API** | http://localhost:8090/api/v1/ | Main API endpoints |
| **📚 API Documentation** | http://localhost:8090/docs | Interactive API explorer |
| **❤️ Health Check** | http://localhost:8090/health | Service status |
| **🎥 Camera Interface** | http://localhost:5000/cameras/ | Live camera feeds |
| **🌐 Management Interface** | `face_recognition_management_web.html` | Web management |

---

## 🎯 **Quick Commands**

### **🎖️ Service Management**
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs face-recognition

# Restart service
docker-compose restart face-recognition

# Test service
python test_real_ai.py
```

### **🔧 Health Checks**
```bash
# Service health
curl http://localhost:8090/health

# API test
curl http://localhost:8090/api/v1/persons/

# Database test
docker-compose exec postgres_db_django pg_isready
```

---

## 👥 **Person Management**

### **🎖️ Web Interface Actions**

| Action | Steps |
|--------|-------|
| **Create Person** | Management Web → "Create Person" tab → Fill form → Create |
| **Edit Person** | Management Web → "Edit Person" tab → Search → Modify → Update |
| **Delete Person** | Management Web → "Delete Person" tab → Select method → Confirm |
| **List Persons** | Management Web → "List Persons" tab → Auto-loads |

### **🔧 API Commands**

#### **Create Person**
```bash
curl -X POST "http://localhost:8090/api/v1/persons/" \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe", "employee_id": "EMP001"}'
```

#### **List Persons**
```bash
curl "http://localhost:8090/api/v1/persons/"
```

#### **Update Person**
```bash
curl -X PUT "http://localhost:8090/api/v1/persons/1" \
  -H "Content-Type: application/json" \
  -d '{"name": "John Smith (Corrected)"}'
```

#### **Delete Person**
```bash
curl -X DELETE "http://localhost:8090/api/v1/persons/1?confirm=true"
```

---

## 🎥 **Camera Operations**

### **🎖️ Start Face Recognition**
1. Go to: http://localhost:5000/cameras/
2. Find your camera
3. Click **"Start Recognition"** (green button)
4. Watch for recognition events

### **🎨 Visual Indicators**
- **🟢 Green border**: Recognized person
- **🟡 Yellow border**: Unknown face
- **📋 Overlay**: Person name + confidence
- **🔵 Badge**: "👁️ Face Recognition Active"

---

## 🧠 **AI Operations**

### **🎖️ Face Detection**
```bash
curl -X POST "http://localhost:8090/api/v1/detection/detect" \
  -F "file=@photo.jpg" \
  -F "confidence_threshold=0.25"
```

### **🎭 Face Recognition**
```bash
curl -X POST "http://localhost:8090/api/v1/recognition/recognize" \
  -F "file=@photo.jpg" \
  -F "recognition_threshold=0.5"
```

---

## 🛠️ **Troubleshooting**

### **🎖️ Common Issues**

| Problem | Solution |
|---------|----------|
| **Service won't start** | `docker-compose restart face-recognition` |
| **API returns 404** | Check URL: `/api/v1/persons/` not `/persons/` |
| **Slow performance** | Enable GPU: `USE_GPU=true` in .env |
| **Database errors** | Reset: `docker-compose down && docker-compose up -d` |
| **Web interface not loading** | Check file path and browser console |

### **🔧 Quick Fixes**
```bash
# Full restart
docker-compose down && docker-compose up -d

# Rebuild service
docker-compose build --no-cache face-recognition

# Check logs
docker-compose logs --tail=50 face-recognition

# Test connectivity
curl -f http://localhost:8090/health || echo "Service DOWN"
```

---

## 📊 **Configuration**

### **🎖️ Key Settings (.env)**
```bash
# Performance
USE_GPU=false          # Enable for RTX cards
MAX_WORKERS=4          # CPU cores to use
BATCH_SIZE=16          # Processing batch size

# AI Thresholds
CONFIDENCE_THRESHOLD=0.25    # Face detection sensitivity
RECOGNITION_THRESHOLD=0.5    # Recognition accuracy

# Debug
DEBUG=true             # Enable debug logging
LOG_LEVEL=INFO         # Logging verbosity
```

---

## 📚 **Documentation Links**

| Document | Purpose |
|----------|---------|
| **[README_FACE_RECOGNITION.md](README_FACE_RECOGNITION.md)** | Main overview |
| **[FACE_RECOGNITION_DOCUMENTATION.md](FACE_RECOGNITION_DOCUMENTATION.md)** | Complete docs |
| **[API_REFERENCE.md](API_REFERENCE.md)** | API details |
| **[FACE_RECOGNITION_COMPLETE_GUIDE.md](FACE_RECOGNITION_COMPLETE_GUIDE.md)** | User guide |

---

## 🎯 **Emergency Procedures**

### **🎖️ Service Recovery**
```bash
# 1. Stop all services
docker-compose down

# 2. Remove problematic containers
docker-compose rm -f face-recognition

# 3. Rebuild and restart
docker-compose build face-recognition
docker-compose up -d

# 4. Verify health
curl http://localhost:8090/health
```

### **🗄️ Database Recovery**
```bash
# 1. Backup current data (if possible)
docker-compose exec postgres_db_django pg_dump -U postgres warehouse_shinobi > backup.sql

# 2. Reset database
docker-compose down
docker volume rm django_cctv_postgres_data

# 3. Restart services
docker-compose up -d

# 4. Restore data (if backup exists)
docker-compose exec -T postgres_db_django psql -U postgres warehouse_shinobi < backup.sql
```

---

## 🎖️ **Performance Monitoring**

### **🚀 Key Metrics**
```bash
# Service response time
time curl -s http://localhost:8090/health

# Memory usage
docker stats face_recognition_service

# Database connections
docker-compose exec postgres_db_django psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;"

# Recognition statistics
curl http://localhost:8090/api/v1/persons/stats
```

### **📊 Optimization Checklist**
- [ ] GPU enabled (`USE_GPU=true`)
- [ ] Appropriate worker count (`MAX_WORKERS`)
- [ ] Optimal batch size (`BATCH_SIZE`)
- [ ] Half precision enabled (`HALF_PRECISION=true`)
- [ ] Redis cache working
- [ ] Database indexes created

---

## 🎖️ **Success Indicators**

### **✅ System Healthy When:**
- Health endpoint returns `{"status": "healthy"}`
- API documentation loads at `/docs`
- Person creation/listing works
- Face detection processes images
- Camera recognition shows visual feedback
- Database queries respond quickly

### **🚀 Ready for Production When:**
- All tests pass (`python test_real_ai.py`)
- Web interface fully functional
- Camera integration working
- Performance optimized
- Monitoring configured
- Backups scheduled

---

**🎖️ FACE RECOGNITION EMPIRE - QUICK REFERENCE**  
*Keep this handy for instant access to all operations!*

---

*Quick Reference Version: 1.0.0*  
*Last Updated: 2024*
