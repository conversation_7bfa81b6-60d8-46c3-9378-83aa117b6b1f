# Enhanced OpenVPN Dockerfile with Auto-Initialization
FROM kylemanna/openvpn:latest

# Install additional tools for initialization
RUN apk add --no-cache \
    bash \
    curl \
    mysql-client \
    && rm -rf /var/cache/apk/*

# Copy initialization script
COPY scripts/init-openvpn.sh /usr/local/bin/init-openvpn.sh
RUN chmod +x /usr/local/bin/init-openvpn.sh

# Set environment for batch mode
ENV EASYRSA_BATCH=1

# Default command with auto-initialization
CMD ["sh", "-c", "if [ ! -f /etc/openvpn/.initialized ]; then echo '🔐 Initializing OpenVPN...'; /usr/local/bin/init-openvpn.sh; fi; echo '🚀 Starting OpenVPN...'; ovpn_run"]
