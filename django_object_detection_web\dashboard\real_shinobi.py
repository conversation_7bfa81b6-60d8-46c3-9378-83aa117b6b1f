import os
import requests
import json
import logging

logger = logging.getLogger(__name__)

class ShinobiClient:
    def __init__(self, api_key=None, group_key=None, host=None):
        self.api_key = api_key or os.getenv("SHINOBI_API_KEY")
        self.group_key = group_key or os.getenv("SHINOBI_GROUP_KEY")
        self.host = host or os.getenv("SHINOBI_HOST", "http://localhost:8080")
        if not self.api_key or not self.group_key:
            logger.warning("Shinobi API or Group key not provided. Set SHINOBI_API_KEY and SHINOBI_GROUP_KEY environment variables.")

    def add_monitor(self, monitor_id, name, rtsp_host, rtsp_port, rtsp_path):
        url = f"{self.host}/{self.group_key}/monitor/{monitor_id}/add"
        data = {
            "mode": "start",
            "mid": monitor_id,
            "name": name,
            "type": "h264",
            "protocol": "rtsp",
            "host": rtsp_host,
            "port": rtsp_port,
            "path": rtsp_path,
            "details": {
                "auto_host_enable": "1",
                "detector": "0",
                "detector_trigger": "1",
                "detector_save": "1",
                "detector_webhook": "0",
                "detector_command_enable": "0",
                "detector_command": "",
                "detector_command_timeout": "10",
                "rtmp_transport": "tcp"
            }
        }
        try:
            response = requests.post(
                url,
                data=json.dumps(data),
                params={"api": self.api_key},
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                logger.info(f"Successfully added monitor: {name}")
                return True, response.json()
            else:
                logger.error(f"Failed to add monitor: {response.text}")
                return False, response.text
        except Exception as e:
            logger.exception(f"Error adding monitor: {str(e)}")
            return False, str(e)

    def add_monitor_by_url(self, monitor_id, name, rtsp_url):
        url = f"{self.host}/{self.group_key}/monitor/{monitor_id}/add"
        data = {
            "mode": "start",
            "mid": monitor_id,
            "name": name,
            "type": "h264",
            "protocol": "rtsp",
            "host": "",
            "port": 0,
            "path": "",
            "details": {
                "auto_host": rtsp_url,
                "auto_host_enable": "1",
                "detector": "0",
                "detector_trigger": "1",
                "detector_save": "1",
                "detector_webhook": "0",
                "detector_command_enable": "0",
                "detector_command": "",
                "detector_command_timeout": "10",
                "rtmp_transport": "tcp",
                "stream_type": "mp4",
                "streaming": "libx264"
            }
        }
        try:
            response = requests.post(
                url,
                data=json.dumps(data),
                params={"api": self.api_key},
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                logger.info(f"Successfully added monitor: {name}")
                return True, response.json()
            else:
                logger.error(f"Failed to add monitor: {response.text}")
                return False, response.text
        except Exception as e:
            logger.exception(f"Error adding monitor: {str(e)}")
            return False, str(e)

    def get_monitors(self):
        url = f"{self.host}/{self.group_key}/monitor"
        try:
            response = requests.get(
                url,
                params={"api": self.api_key},
                timeout=10
            )
            if response.status_code == 200:
                return True, response.json()
            else:
                logger.warning(f"Shinobi API not available (status {response.status_code}). Using database fallback.")
                # Fallback: Get monitors directly from database
                return self._get_monitors_from_database()
        except requests.exceptions.Timeout:
            logger.warning("Shinobi API timeout. Using database fallback.")
            return self._get_monitors_from_database()
        except Exception as e:
            logger.warning(f"Shinobi API error: {str(e)}. Using database fallback.")
            return self._get_monitors_from_database()

    def delete_monitor(self, monitor_id):
        url = f"{self.host}/{self.group_key}/monitor/{monitor_id}/delete"
        try:
            response = requests.get(
                url,
                params={"api": self.api_key}
            )
            if response.status_code == 200:
                logger.info(f"Successfully deleted monitor: {monitor_id}")
                return True, response.json()
            else:
                logger.error(f"Failed to delete monitor: {response.text}")
                return False, response.text
        except Exception as e:
            logger.exception(f"Error deleting monitor: {str(e)}")
            return False, str(e)

    def start_monitor(self, monitor_id):
        url = f"{self.host}/{self.group_key}/monitor/{monitor_id}/start"
        try:
            response = requests.get(
                url,
                params={"api": self.api_key}
            )
            if response.status_code == 200:
                logger.info(f"Successfully started monitor: {monitor_id}")
                return True, response.json()
            else:
                logger.error(f"Failed to start monitor: {response.text}")
                return False, response.text
        except Exception as e:
            logger.exception(f"Error starting monitor: {str(e)}")
            return False, str(e)

    def stop_monitor(self, monitor_id):
        url = f"{self.host}/{self.group_key}/monitor/{monitor_id}/stop"
        try:
            response = requests.get(
                url,
                params={"api": self.api_key}
            )
            if response.status_code == 200:
                logger.info(f"Successfully stopped monitor: {monitor_id}")
                return True, response.json()
            else:
                logger.error(f"Failed to stop monitor: {response.text}")
                return False, response.text
        except Exception as e:
            logger.exception(f"Error stopping monitor: {str(e)}")
            return False, str(e)

    def _get_monitors_from_database(self):
        """Fallback method to get monitors directly from Shinobi database"""
        try:
            import pymysql

            # Connect to Shinobi database
            connection = pymysql.connect(
                host='shinobi_mariadb_for_shinobi',
                database='shinobi_db',
                user='shinobi',
                password='shinobi_password',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )

            cursor = connection.cursor()
            cursor.execute("""
                SELECT mid, ke, name, host, path, port, protocol, mode, type, ext, fps, width, height, details
                FROM Monitors
                WHERE ke = %s
            """, (self.group_key,))

            monitors = cursor.fetchall()
            cursor.close()
            connection.close()

            # Convert to format similar to API response
            formatted_monitors = []
            for monitor in monitors:
                formatted_monitor = {
                    'mid': monitor['mid'],
                    'ke': monitor['ke'],
                    'name': monitor['name'],
                    'host': monitor['host'],
                    'path': monitor['path'],
                    'port': monitor['port'],
                    'protocol': monitor['protocol'],
                    'mode': monitor['mode'] or 'stop',
                    'type': monitor['type'],
                    'ext': monitor['ext'],
                    'fps': monitor['fps'],
                    'width': monitor['width'],
                    'height': monitor['height'],
                    'details': monitor['details'],
                    'status': 'stop',  # Default status since we can't get real-time status
                    'stream_url': f"http://shinobi-nvr:8080/{monitor['ke']}/embed/{monitor['mid']}"
                }
                formatted_monitors.append(formatted_monitor)

            logger.info(f"Retrieved {len(formatted_monitors)} monitors from database fallback")
            return True, formatted_monitors

        except Exception as e:
            logger.error(f"Database fallback failed: {str(e)}")
            return False, f"Database fallback failed: {str(e)}"

# Legacy function for backward compatibility
def create_shinobi_monitor(camera_id, name, rtsp_url):
    client = ShinobiClient()
    return client.add_monitor_by_url(camera_id, name, rtsp_url)
