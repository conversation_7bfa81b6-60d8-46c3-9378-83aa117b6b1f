{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}{{ camera.name }} - Eagle CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ camera.name }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-camera-{{ camera.id }}">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="take-snapshot-{{ camera.id }}">
                <i class="bi bi-camera"></i> Snapshot
            </button>
        </div>
        {% if request.user.role.name in "Administrator,Warehouse Manager" or request.user.is_superuser %} {# #}
        <a href="{% url 'dashboard:camera_edit' camera_id=camera.id %}" class="btn btn-sm btn-outline-primary">
            <i class="bi bi-pencil"></i> Edit Camera
        </a>
        {% endif %}
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Live Feed</h5>
                <span class="camera-status status-{{ camera.status|lower }}" id="camera-status-indicator-{{ camera.id }}">
                    {{ camera.status|capfirst }}
                </span>
            </div>
            <div class="card-body p-0">
                <div class="position-relative">
                    <div class="ratio ratio-16x9">
                        {% if config.SHINOBI_URL and camera.shinobi_group_key and camera.shinobi_id and camera.shinobi_api_key %}
                        <iframe 
                            src="{{ config.SHINOBI_URL }}/embed/{{ camera.shinobi_group_key }}/{{ camera.shinobi_id }}/{{ camera.shinobi_api_key }}"
                            frameborder="0"
                            allowfullscreen
                        ></iframe>
                        {% else %}
                        <div class="bg-light d-flex align-items-center justify-content-center">
                            <p class="text-muted">Video feed configuration missing or unavailable.</p>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge rounded-pill {% if camera.is_recording %}bg-danger{% else %}bg-secondary{% endif %}" 
                              id="camera-recording-indicator-{{ camera.id }}">
                            <i class="bi {% if camera.is_recording %}bi-record-fill{% else %}bi-record{% endif %}"></i>
                            {% if camera.is_recording %}Recording{% else %}Not Recording{% endif %}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        {% if request.user.role.name in "Administrator,Warehouse Manager,Security Operator" or request.user.is_superuser %} {# #}
                        <button type="button" class="btn btn-success btn-sm recording-control {% if camera.is_recording %}d-none{% endif %}" 
                                data-camera-id="{{ camera.id }}" data-action="start" id="start-recording-{{ camera.id }}">
                            <i class="bi bi-record-fill"></i> Start Recording
                        </button>
                        <button type="button" class="btn btn-danger btn-sm recording-control {% if not camera.is_recording %}d-none{% endif %}" 
                                data-camera-id="{{ camera.id }}" data-action="stop" id="stop-recording-{{ camera.id }}">
                            <i class="bi bi-stop-fill"></i> Stop Recording
                        </button>
                        {% endif %}
                    </div>
                    
                    <div class="text-muted small">
                        Last updated: <span id="camera-last-updated-{{ camera.id }}">{{ camera.last_updated|date:"Y-m-d H:i:s" }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        {% if camera.is_ptz %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">PTZ Controls</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-2" id="ptz-status-{{ camera.id }}">Ready</div>
                    
                    <div class="ptz-controls mx-auto mb-3" style="max-width: 200px;">
                        <div class="d-flex justify-content-between mb-1">
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="up-left"><i class="bi bi-arrow-up-left"></i></button>
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="up"><i class="bi bi-arrow-up"></i></button>
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="up-right"><i class="bi bi-arrow-up-right"></i></button>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="left"><i class="bi bi-arrow-left"></i></button>
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="home"><i class="bi bi-house"></i></button>
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="right"><i class="bi bi-arrow-right"></i></button>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="down-left"><i class="bi bi-arrow-down-left"></i></button>
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="down"><i class="bi bi-arrow-down"></i></button>
                            <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="down-right"><i class="bi bi-arrow-down-right"></i></button>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">Zoom</label>
                            <div class="btn-group w-100">
                                <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="zoom-out"><i class="bi bi-zoom-out"></i></button>
                                <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="zoom-in"><i class="bi bi-zoom-in"></i></button>
                            </div>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Focus</label>
                            <div class="btn-group w-100">
                                <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="focus-near"><i class="bi bi-dash-lg"></i></button>
                                <button class="btn btn-outline-secondary ptz-btn" data-camera-id="{{ camera.id }}" data-direction="focus-far"><i class="bi bi-plus-lg"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Camera Details</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Location:</strong>
                        <span>{{ camera.location.name }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Status:</strong>
                        <span class="badge bg-{% if camera.status == 'online' %}success{% else %}danger{% endif %}">
                            {{ camera.status|capfirst }}
                        </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Recording:</strong>
                        <span class="badge {% if camera.is_recording %}bg-danger{% else %}bg-secondary{% endif %}">
                            {% if camera.is_recording %}Active{% else %}Inactive{% endif %}
                        </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Type:</strong>
                        <span>{% if camera.is_ptz %}PTZ Camera{% else %}Fixed Camera{% endif %}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Added On:</strong>
                        <span>{{ camera.created_at|date:"Y-m-d" }}</span>
                    </li>
                </ul>
                
                {% if request.user.role.name == "Administrator" or request.user.is_superuser %} {# #}
                <div class="mt-3">
                    <h6>Technical Details</h6>
                    <div class="mb-2">
                        <strong>RTSP URL:</strong>
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control form-control-sm" value="{{ camera.rtsp_url|default_if_none:'' }}" readonly>
                            <button class="btn btn-outline-secondary btn-sm copy-btn" type="button" data-clipboard-text="{{ camera.rtsp_url|default_if_none:'' }}">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mb-2">
                        <strong>Shinobi ID:</strong>
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control form-control-sm" value="{{ camera.shinobi_id|default_if_none:'' }}" readonly>
                            <button class="btn btn-outline-secondary btn-sm copy-btn" type="button" data-clipboard-text="{{ camera.shinobi_id|default_if_none:'' }}">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Recent Recordings</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="#" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">Motion Recording</h6>
                            <small>10 min ago</small>
                        </div>
                        <small class="text-muted">Duration: 2:45 • Size: 15 MB</small>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">Manual Recording</h6>
                            <small>2 hours ago</small>
                        </div>
                        <small class="text-muted">Duration: 10:12 • Size: 42 MB</small>
                    </a>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="#" class="btn btn-sm btn-outline-primary">View All Recordings (Dummy)</a>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Recent Incidents for this Camera</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    {% with camera_incidents=camera.incident_set.all|slice:":3" %} {# Or equivalent related manager name #}
                        {% if camera_incidents %}
                            {% for incident in camera_incidents %}
                            <a href="{% url 'dashboard:incident_detail' incident_id=incident.id %}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ incident.title }}</h6>
                                    <small>{{ incident.created_at|timesince }} ago</small>
                                </div>
                                <p class="mb-1 small">{{ incident.description|truncatechars:50 }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">Reported by: {{ incident.reporter.username }}</small>
                                    <span class="badge bg-{% if incident.severity == 'critical' %}danger{% elif incident.severity == 'high' %}warning{% elif incident.severity == 'medium' %}info{% else %}secondary{% endif %}">
                                        {{ incident.get_severity_display|default:incident.severity }}
                                    </span>
                                </div>
                            </a>
                            {% endfor %}
                        {% else %}
                            <div class="list-group-item text-center py-3">
                                <p class="mb-0 text-muted">No incidents reported for this camera</p>
                            </div>
                        {% endif %}
                    {% endwith %}
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="{% url 'dashboard:incident_add' %}?camera_id={{ camera.id }}" class="btn btn-sm btn-outline-primary">Report Incident</a>
            </div>
        </div>
    </div>
</div>
<div id="notifications-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;"></div>
{% endblock %}

{% block scripts %}
<script src="{% static 'js/camera-controls.js' %}"></script> {# Assuming this file exists and is adapted #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const cameraId = "{{ camera.id }}";
    const csrfToken = "{{ csrf_token }}"; // For POST requests if camera-controls.js makes them.

    function showNotification(message, type = 'success') {
        const notificationsContainer = document.getElementById('notifications-container');
        if (!notificationsContainer) return;

        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.setAttribute('role', 'alert');
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        notificationsContainer.appendChild(notification);
        setTimeout(() => {
            // Ensure bootstrap's alert 'close' is triggered if available, otherwise just remove
            if (bootstrap && bootstrap.Alert && bootstrap.Alert.getInstance(notification)) {
                 bootstrap.Alert.getInstance(notification).close();
            } else {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    // Refresh Camera Button
    const refreshButton = document.getElementById(`refresh-camera-${cameraId}`);
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            fetch("{% url 'dashboard:api_camera_status' camera_id=camera.id %}")
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showNotification(`Error refreshing camera: ${data.error}`, 'danger');
                        return;
                    }
                    document.getElementById(`camera-status-indicator-${cameraId}`).textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
                    document.getElementById(`camera-status-indicator-${cameraId}`).className = `camera-status status-${data.status.toLowerCase()}`;
                    
                    const recIndicator = document.getElementById(`camera-recording-indicator-${cameraId}`);
                    recIndicator.innerHTML = `<i class="bi ${data.is_recording ? 'bi-record-fill' : 'bi-record'}"></i> ${data.is_recording ? 'Recording' : 'Not Recording'}`;
                    recIndicator.className = `badge rounded-pill ${data.is_recording ? 'bg-danger' : 'bg-secondary'}`;

                    document.getElementById(`camera-last-updated-${cameraId}`).textContent = data.last_updated;

                    // Toggle recording buttons visibility
                    document.getElementById(`start-recording-${cameraId}`).classList.toggle('d-none', data.is_recording);
                    document.getElementById(`stop-recording-${cameraId}`).classList.toggle('d-none', !data.is_recording);

                    showNotification('Camera status refreshed!', 'success');
                })
                .catch(error => {
                    console.error("Error refreshing camera status:", error);
                    showNotification('Failed to refresh camera status.', 'danger');
                })
                .finally(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
                });
        });
    }

    // Snapshot Button
    const snapshotButton = document.getElementById(`take-snapshot-${cameraId}`);
    if (snapshotButton) {
        snapshotButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            fetch("{% url 'dashboard:api_camera_snapshot' camera_id=camera.id %}") // Assumes GET, as per views.py
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(data.message + (data.snapshot_url ? ` URL: ${data.snapshot_url}` : ''), 'success');
                    } else {
                        showNotification(data.error || 'Failed to take snapshot.', 'danger');
                    }
                })
                .catch(error => {
                    console.error("Error taking snapshot:", error);
                    showNotification('Error taking snapshot.', 'danger');
                })
                .finally(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="bi bi-camera"></i> Snapshot';
                });
        });
    }
    
    // Recording Controls
    document.querySelectorAll('.recording-control').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            this.disabled = true;
            const originalHtml = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ...';

            fetch(`{% url 'dashboard:api_toggle_recording' camera_id=0 %}`.replace('0', cameraId), { //
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken 
                },
                body: JSON.stringify({ action: action })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    // Manually trigger a refresh of status to update UI consistently
                    if(refreshButton) refreshButton.click();
                } else {
                    showNotification(data.error || `Failed to ${action} recording.`, 'danger');
                }
            })
            .catch(error => {
                console.error(`Error ${action} recording:`, error);
                showNotification(`Error ${action} recording.`, 'danger');
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = originalHtml;
            });
        });
    });

    // PTZ Controls
    document.querySelectorAll('.ptz-btn').forEach(button => {
        button.addEventListener('click', function() {
            const direction = this.dataset.direction;
            const ptzStatusDiv = document.getElementById(`ptz-status-${cameraId}`);
            ptzStatusDiv.textContent = `Sending ${direction}...`;

            fetch(`{% url 'dashboard:api_camera_ptz' camera_id=0 %}`.replace('0', cameraId), { //
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken 
                },
                body: JSON.stringify({ direction: direction })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    ptzStatusDiv.textContent = data.message;
                    showNotification(data.message, 'success');
                } else {
                    ptzStatusDiv.textContent = `Error: ${data.error || 'Failed'}`;
                    showNotification(data.error || 'PTZ command failed.', 'danger');
                }
                setTimeout(() => { ptzStatusDiv.textContent = 'Ready'; }, 2000);
            })
            .catch(error => {
                console.error("PTZ Error:", error);
                ptzStatusDiv.textContent = 'PTZ command error.';
                showNotification('PTZ command error.', 'danger');
                setTimeout(() => { ptzStatusDiv.textContent = 'Ready'; }, 2000);
            });
        });
    });

    // Initialize copy buttons
    document.querySelectorAll('.copy-btn').forEach(button => {
        button.addEventListener('click', function() {
            const text = this.dataset.clipboardText;
            navigator.clipboard.writeText(text).then(() => {
                const originalHtml = this.innerHTML;
                this.innerHTML = '<i class="bi bi-check"></i>';
                showNotification('Copied to clipboard!', 'info');
                setTimeout(() => {
                    this.innerHTML = originalHtml;
                }, 2000);
            }).catch(err => {
                showNotification('Failed to copy text.', 'warning');
                console.error('Failed to copy text: ', err);
            });
        });
    });
});
</script>
{% endblock %}