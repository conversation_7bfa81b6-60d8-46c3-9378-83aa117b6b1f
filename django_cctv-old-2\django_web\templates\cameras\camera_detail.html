{% extends 'base.html' %}
{% load static %}

{% block title %}{{ camera.name }} - Camera Details{% endblock %}

{% block header_title %}Camera Details{% endblock %}

{% block content %}
<div class="fade-in">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Live Stream Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Live Stream</h2>
            </div>

            {% if hls_url %}
            <div class="aspect-video bg-black">
                <!-- HLS Live Stream -->
                <video id="video-{{ camera.id }}"
                       width="100%"
                       height="100%"
                       controls
                       muted
                       autoplay
                       poster="{{ preview_url }}"
                       class="w-full h-full object-cover">
                    <source src="{{ hls_url }}" type="application/x-mpegURL">
                    <span class="text-white">Your browser does not support the video tag.</span>
                </video>
            </div>

            <!-- Stream Controls -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700">
                <div class="flex space-x-2">
                    <button type="button"
                      onclick="refreshVideo('video-{{ camera.id }}')"
                      class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      🔄 Refresh Stream
                    </button>
                    <button type="button"
                      onclick="toggleFullscreen('video-{{ camera.id }}')"
                      class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                    >
                      ⛶ Fullscreen
                    </button>
                </div>
            </div>
            {% else %}
            <div class="aspect-video bg-gray-900 flex items-center justify-center">
                <div class="text-center text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <p>No live stream available</p>
                    <p class="text-sm mt-2 opacity-75">Camera may be offline or not configured</p>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Camera Information Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Camera Information</h2>
            </div>

            <div class="p-4 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Name:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ camera.name }}</span>
                </div>

                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Status:</span>
                    <span class="flex items-center">
                        <span class="status-indicator {% if camera.status == 'online' %}status-online{% elif camera.status == 'offline' %}status-offline{% elif camera.status == 'disabled' %}status-disabled{% else %}status-maintenance{% endif %} mr-2"></span>
                        {% if camera.status == camera.CameraStatus.ONLINE %}
                            <span class="text-green-600 dark:text-green-400 font-semibold">Online</span>
                        {% else %}
                            <span class="text-red-600 dark:text-red-400 font-semibold">{{ camera.get_status_display }}</span>
                        {% endif %}
                    </span>
                </div>

                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Location:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ camera.location|default:"Not specified" }}</span>
                </div>

                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Groups:</span>
                    <span class="text-gray-900 dark:text-gray-100">
                        {% for group in camera.groups.all %}
                            <span class="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs mr-1">{{ group.name }}</span>
                        {% empty %}
                            <span class="text-gray-500 dark:text-gray-400">No groups assigned</span>
                        {% endfor %}
                    </span>
                </div>

                {% if camera.shinobi_monitor_id %}
                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Shinobi Monitor ID:</span>
                    <span class="text-gray-900 dark:text-gray-100 font-mono">{{ camera.shinobi_monitor_id }}</span>
                </div>
                {% endif %}

                {% if shinobi_data %}
                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Resolution:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ shinobi_data.details.max_width }}x{{ shinobi_data.details.max_height }}</span>
                </div>

                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">FPS:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ shinobi_data.details.max_fps }}</span>
                </div>
                {% endif %}

                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Created:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ camera.created_at|date:"M d, Y H:i" }}</span>
                </div>

                <div class="flex items-center justify-between">
                    <span class="font-semibold text-gray-700 dark:text-gray-200">Last Updated:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ camera.updated_at|date:"M d, Y H:i" }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-6 flex space-x-4">
        <a href="{% url 'cameras:camera_list' %}" class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Camera List
        </a>

        {% if user.is_admin %}
        <a href="{% url 'cameras:camera_edit' camera.id %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit Camera
        </a>
        {% endif %}

        <a href="{% url 'cameras:camera_grid' %}?cameras={{ camera.id }}&layout=1x1" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            View Live
        </a>
    </div>
</div>

<!-- HLS.js for video streaming -->
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // HLS setup
        const video = document.querySelector('video');
        if (video) {
            const hlsUrl = video.querySelector('source').src;
            if (Hls.isSupported()) {
                const hls = new Hls();
                hls.loadSource(hlsUrl);
                hls.attachMedia(video);
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = hlsUrl;
            }
        }
    });

    // Fullscreen function
    function toggleFullscreen(videoId) {
        const video = document.getElementById(videoId);
        if (video) {
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        }
    }

    // Refresh video function
    function refreshVideo(videoId) {
        const video = document.getElementById(videoId);
        if (video) {
            video.load(); // Reload the video
        }
    }
</script>
{% endblock %}
