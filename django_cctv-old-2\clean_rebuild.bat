@echo off
REM Batch script to clean and rebuild Docker services
REM This script will remove all existing containers, images, volumes, and networks
REM Then rebuild everything from scratch

echo.
echo ============================================
echo  Docker Environment Cleanup and Rebuild
echo ============================================
echo.

REM Check if Dock<PERSON> is running
echo [INFO] Checking if Docker is running...
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)
echo [SUCCESS] Docker is running!

REM Step 1: Stop and remove all containers from this project
echo.
echo [INFO] Stopping all containers for this project...
docker-compose down --remove-orphans

echo [INFO] Removing all containers related to this project...
for /f "tokens=*" %%i in ('docker ps -a --filter "name=django_web" --filter "name=shinobi_cctv_django" --filter "name=postgres_db_django" --filter "name=redis_cache" --filter "name=shinobi-nvr" --filter "name=shinobi_mariadb" --filter "name=openvpn" --filter "name=pgadmin" -q 2^>nul') do (
    docker rm -f %%i
)
echo [SUCCESS] Removed project containers

REM Step 2: Remove project-specific images
echo.
echo [INFO] Removing project-specific images...
for /f "tokens=*" %%i in ('docker images --filter "reference=django_cctv*" --filter "reference=*django_web*" --filter "reference=*shinobi_cctv_django*" -q 2^>nul') do (
    docker rmi -f %%i
)
echo [SUCCESS] Removed project images

REM Step 3: Remove project volumes
echo.
echo [INFO] Removing project volumes...
for /f "tokens=*" %%i in ('docker volume ls --filter "name=django_cctv" -q 2^>nul') do (
    docker volume rm %%i
)
echo [SUCCESS] Removed project volumes

REM Step 4: Remove project networks
echo.
echo [INFO] Removing project networks...
for /f "tokens=*" %%i in ('docker network ls --filter "name=django_cctv" --filter "name=cctv_net" -q 2^>nul') do (
    docker network rm %%i
)
echo [SUCCESS] Removed project networks

REM Step 5: Clean up dangling images and build cache
echo.
echo [INFO] Cleaning up dangling images and build cache...
docker image prune -f
docker builder prune -f
echo [SUCCESS] Cleaned up dangling resources

REM Step 6: Show current Docker status
echo.
echo [INFO] Current Docker status after cleanup:
echo Containers:
docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
echo.
echo Images:
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

echo.
echo [SUCCESS] Docker environment cleanup completed!
echo ============================================

REM Step 7: Rebuild services
echo.
echo [INFO] Starting fresh build of all services...
echo This may take several minutes...

REM Build and start services
echo [INFO] Building and starting services with docker-compose...
docker-compose up -d --build --force-recreate

if %errorlevel% neq 0 (
    echo [ERROR] Failed to build services. Check the logs with: docker-compose logs
    pause
    exit /b 1
)

echo [INFO] Waiting for services to initialize (60 seconds)...
timeout /t 60 /nobreak >nul

REM Check if database is ready and run migrations
echo [INFO] Checking database connectivity...
docker-compose exec -T db pg_isready -U user -d warehouse_shinobi
if %errorlevel% equ 0 (
    echo [SUCCESS] Database is ready!
    
    echo [INFO] Running migrations for django_web...
    docker-compose exec -T web python manage.py migrate
    
    echo [INFO] Running migrations for shinobi_cctv_django...
    docker-compose exec -T shinobi_cctv_django python manage.py migrate
    
    echo [INFO] Collecting static files for django_web...
    docker-compose exec -T web python manage.py collectstatic --noinput
    
    echo [INFO] Collecting static files for shinobi_cctv_django...
    docker-compose exec -T shinobi_cctv_django python manage.py collectstatic --noinput --clear
    
    echo [SUCCESS] All services built and configured successfully!
) else (
    echo [WARNING] Database not ready yet. You may need to run migrations manually later.
    echo Use: docker-compose exec web python manage.py migrate
    echo Use: docker-compose exec shinobi_cctv_django python manage.py migrate
)

REM Step 8: Show final status
echo.
echo ============================================
echo [INFO] Final service status:
docker-compose ps

echo.
echo ============================================
echo          Rebuild Process Complete!
echo ============================================
echo Services are now available at:
echo   • django_web: http://localhost:8000
echo   • shinobi_cctv_django: http://localhost:5000
echo   • pgAdmin: http://localhost:5050 (<EMAIL> / admin123)
echo.
echo Next steps:
echo   1. Create superusers: docker-compose exec web python manage.py createsuperuser
echo   2. Create superusers: docker-compose exec shinobi_cctv_django python manage.py createsuperuser
echo   3. Test the services: python verify_setup.py
echo.
pause
