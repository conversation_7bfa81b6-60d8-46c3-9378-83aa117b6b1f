apps:
  - name: camera
    script: /home/<USER>/camera.js
    watch:
      - /home/<USER>/conf.json
      - /home/<USER>/camera.js
    ignore_watch:
      - "*.log"
      - "*.sql"
      - videos
      - streams
    node_args: "--expose-gc --max-old-space-size=2048"
    max_memory_restart: 1G
    cwd: /home/<USER>/
    env: {}
  - name: cron
    script: /home/<USER>/cron.js
    watch:
      - /home/<USER>/conf.json
      - /home/<USER>/cron.js
    ignore_watch:
      - "*.log"
      - "*.sql"
      - videos
      - streams
    node_args: "--expose-gc"
    max_memory_restart: 500M
    cwd: /home/<USER>/
    env: {}
