{% extends 'base.html' %}

{% block content %}
  <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">VPN Client: {{ client.name }}</h1>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Client ID:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ client.client_id }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Status:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ client.get_status_display }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Virtual IP:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ client.virtual_ip }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Connected Since:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ client.connected_since }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Last Seen:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ client.last_seen }}</span>
    </div>
    {% if client.config_file %}
      <div class="mb-4">
        <a href="{% url 'vpn:client_download_config' client.id %}" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 font-semibold">Download Config</a>
      </div>
    {% endif %}
    <h2 class="text-xl font-semibold mt-8 mb-2 text-gray-800 dark:text-gray-100">Recent Logs</h2>
    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
      {% for log in logs %}
        <li class="py-2">
          <span class="text-gray-900 dark:text-gray-100">{{ log.timestamp }} - {{ log.get_event_type_display }}: {{ log.message }}</span>
        </li>
      {% empty %}
        <li class="py-2 text-gray-500 dark:text-gray-400">No logs for this client.</li>
      {% endfor %}
    </ul>
    <div class="mt-8">
      <a href="{% url 'vpn:client_list' %}" class="inline-block px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Back to Client List</a>
    </div>
  </div>
{% endblock %}
