"""
🎖️ FACE RECOGNITION MICROSERVICE CLIENT - <PERSON><PERSON><PERSON><PERSON> CCTV DJANGO
🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 3
⚔️ TACTICAL SHINOBI INTEGRATION CLIENT
"""

import logging
import requests
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from django.conf import settings
from django.core.cache import cache
import io
from PIL import Image
import base64

logger = logging.getLogger(__name__)

class ShinobiFaceRecognitionClient:
    """🎭 Face Recognition Client for Shinobi CCTV Django Integration"""
    
    def __init__(self, base_url: str = None, timeout: int = 30):
        """
        Initialize Face Recognition Client for Shinobi integration
        
        Args:
            base_url: Base URL of the face recognition microservice
            timeout: Request timeout in seconds
        """
        self.base_url = base_url or getattr(settings, 'FACE_RECOGNITION_URL', 'http://localhost:8090')
        self.timeout = timeout
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'User-Agent': 'Shinobi-Django-FaceRecognition-Client/1.0',
            'Accept': 'application/json'
        })
        
        logger.info(f"🎭 Shinobi Face Recognition Client initialized: {self.base_url}")
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Tuple[bool, Dict[str, Any]]:
        """
        Make HTTP request to face recognition microservice
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Tuple of (success, response_data)
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            if response.status_code == 200:
                return True, response.json()
            else:
                logger.error(f"🚨 Face Recognition API error: {response.status_code} - {response.text}")
                return False, {
                    'error': f"HTTP {response.status_code}",
                    'message': response.text
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"🚨 Face Recognition request failed: {e}")
            return False, {
                'error': 'Connection failed',
                'message': str(e)
            }
    
    def health_check(self) -> Tuple[bool, Dict[str, Any]]:
        """
        🩺 Check if face recognition service is healthy
        
        Returns:
            Tuple of (is_healthy, health_data)
        """
        return self._make_request('GET', '/health')
    
    def process_shinobi_frame(self, camera_id: int, monitor_id: str, image_data: bytes) -> Dict[str, Any]:
        """
        📹 Process Shinobi camera frame for face recognition
        
        Args:
            camera_id: Django camera ID
            monitor_id: Shinobi monitor ID
            image_data: Raw image data from Shinobi
            
        Returns:
            Processing results dictionary
        """
        try:
            # Create image file-like object
            image_file = io.BytesIO(image_data)
            image_file.name = f"shinobi_{monitor_id}_frame.jpg"
            
            results = {
                'camera_id': camera_id,
                'monitor_id': monitor_id,
                'timestamp': time.time(),
                'faces_detected': 0,
                'faces_recognized': 0,
                'detections': [],
                'recognitions': [],
                'success': False
            }
            
            # First detect faces
            success, detection_data = self.detect_faces(image_file)
            if success:
                results['faces_detected'] = detection_data.get('detection_info', {}).get('faces_detected', 0)
                results['detections'] = detection_data.get('faces', [])
                results['success'] = True
                
                # If faces detected, try recognition
                if results['faces_detected'] > 0:
                    # Reset file pointer for recognition
                    image_file.seek(0)
                    success, recognition_data = self.recognize_faces(image_file)
                    if success:
                        results['faces_recognized'] = recognition_data.get('recognition_info', {}).get('matches_found', 0)
                        results['recognitions'] = recognition_data.get('faces', [])
            
            return results
            
        except Exception as e:
            logger.error(f"🚨 Shinobi frame processing failed: {e}")
            return {
                'camera_id': camera_id,
                'monitor_id': monitor_id,
                'timestamp': time.time(),
                'success': False,
                'error': str(e)
            }
    
    def detect_faces(self, image_file, confidence_threshold: float = 0.25) -> Tuple[bool, Dict[str, Any]]:
        """
        🎯 Detect faces in image (using recognition endpoint for minimal service)

        Args:
            image_file: Image file or file-like object
            confidence_threshold: Detection confidence threshold

        Returns:
            Tuple of (success, detection_results)
        """
        # For minimal service, use recognize endpoint which includes detection
        files = {'image': image_file}

        success, result = self._make_request('POST', '/recognize', files=files)

        if success:
            # Convert recognition result to detection format
            detection_result = {
                'success': True,
                'detection_info': {
                    'faces_detected': 1 if result.get('recognition', {}).get('is_match') else 0,
                    'model': 'minimal_service'
                },
                'faces': [result.get('recognition', {})] if result.get('recognition', {}).get('is_match') else [],
                'processing_time_ms': result.get('processing_time_ms', 0)
            }
            return True, detection_result

        return success, result
    
    def recognize_faces(self, image_file, recognition_threshold: float = 0.5) -> Tuple[bool, Dict[str, Any]]:
        """
        🎭 Recognize faces in image

        Args:
            image_file: Image file or file-like object
            recognition_threshold: Recognition confidence threshold

        Returns:
            Tuple of (success, recognition_results)
        """
        files = {'image': image_file}

        success, result = self._make_request('POST', '/recognize', files=files)

        if success:
            # Convert to expected format
            recognition_result = {
                'success': True,
                'recognition_info': {
                    'matches_found': 1 if result.get('recognition', {}).get('is_match') else 0,
                    'model': 'minimal_service'
                },
                'faces': [result.get('recognition', {})] if result.get('recognition', {}).get('is_match') else [],
                'processing_time_ms': result.get('processing_time_ms', 0)
            }
            return True, recognition_result

        return success, result
    
    def create_person_from_camera(self, name: str, location_id: int, camera_id: int, 
                                 image_data: bytes) -> Tuple[bool, Dict[str, Any]]:
        """
        👥 Create person from camera capture in Shinobi system
        
        Args:
            name: Person name
            location_id: Location ID from Shinobi Django
            camera_id: Camera ID
            image_data: Face image data
            
        Returns:
            Tuple of (success, person_data)
        """
        try:
            # Create person data
            person_data = {
                'name': name,
                'department': f'Location_{location_id}',
                'role': 'Visitor',
                'notes': f'Created from camera {camera_id} in Shinobi system'
            }
            
            # Create person in face recognition system
            success, person_result = self._make_request('POST', '/api/v1/persons/', json=person_data)
            
            if success:
                person_id = person_result['person']['id']
                
                # Add face image
                image_file = io.BytesIO(image_data)
                image_file.name = f"person_{person_id}_face.jpg"
                
                files = {'file': image_file}
                data = {'is_primary': True}
                
                face_success, face_result = self._make_request(
                    'POST', 
                    f'/api/v1/persons/{person_id}/faces', 
                    files=files, 
                    data=data
                )
                
                if face_success:
                    return True, {
                        'person': person_result['person'],
                        'face_record': face_result['face_record']
                    }
                else:
                    return False, face_result
            else:
                return False, person_result
                
        except Exception as e:
            logger.error(f"🚨 Person creation from camera failed: {e}")
            return False, {'error': str(e)}
    
    def get_location_persons(self, location_id: int) -> Tuple[bool, Dict[str, Any]]:
        """
        👥 Get persons associated with a location
        
        Args:
            location_id: Location ID
            
        Returns:
            Tuple of (success, persons_data)
        """
        return self._make_request('GET', '/api/v1/persons/', params={
            'department': f'Location_{location_id}',
            'limit': 1000
        })
    
    def get_recognition_analytics(self, camera_id: int = None, 
                                location_id: int = None) -> Dict[str, Any]:
        """
        📊 Get face recognition analytics for Shinobi dashboard
        
        Args:
            camera_id: Optional camera filter
            location_id: Optional location filter
            
        Returns:
            Analytics data dictionary
        """
        try:
            # Get statistics from face recognition service
            success, stats_data = self._make_request('GET', '/api/v1/persons/stats')
            
            if success:
                analytics = {
                    'total_persons': stats_data.get('statistics', {}).get('total_persons', 0),
                    'total_faces': stats_data.get('statistics', {}).get('total_face_records', 0),
                    'avg_faces_per_person': stats_data.get('statistics', {}).get('avg_faces_per_person', 0),
                    'recognition_ready': True,
                    'last_updated': time.time()
                }
                
                # Add location-specific data if requested
                if location_id:
                    location_success, location_data = self.get_location_persons(location_id)
                    if location_success:
                        analytics['location_persons'] = len(location_data.get('persons', []))
                
                return analytics
            else:
                return {
                    'recognition_ready': False,
                    'error': stats_data.get('error', 'Unknown error'),
                    'last_updated': time.time()
                }
                
        except Exception as e:
            logger.error(f"🚨 Analytics retrieval failed: {e}")
            return {
                'recognition_ready': False,
                'error': str(e),
                'last_updated': time.time()
            }

# 🎖️ Global client instance for Shinobi
shinobi_face_recognition_client = ShinobiFaceRecognitionClient()

def get_shinobi_face_recognition_client() -> ShinobiFaceRecognitionClient:
    """
    🎯 Get Shinobi face recognition client instance
    
    Returns:
        ShinobiFaceRecognitionClient: Global client instance
    """
    return shinobi_face_recognition_client

logger.info("🎭 Shinobi Face Recognition Client module loaded")
logger.info("🔗 Ready for Shinobi CCTV Django integration")
logger.info("⚔️ Phase 3 Shinobi tactical client operational")
