import os
import cv2
import time
from threading import Lock
import asyncio
from concurrent.futures import ThreadPoolExecutor
import numpy as np
import torch
import math
from ultralytics import YOLO
import pandas as pd
import cvzone

# Load YOLO model once
model = YOLO('yolov8n.pt')  # Specialized face detection model
# For better performance:
model.to('cuda' if torch.cuda.is_available() else 'cpu')
model.fuse()

executor = ThreadPoolExecutor(max_workers=os.cpu_count())  # cpu_count() // 2 More conservative
FRAME_SKIP = 3  # Process every 3rd frame (Adjust as needed)

gap = 20  # 20-pixel gap between areas

distance = 30
speed_threshold = 50

def find_available_camera():
    """Search for an available camera index dynamically"""
    for index in range(5):  # Check first 10 indices
        cap = cv2.VideoCapture(index)
        if cap.isOpened():
            cap.release()
            print(f"Available camera index: {index}")
            return index
    return None  # No available camera found

default_camera_index = find_available_camera()

if default_camera_index is not None:
    cap = cv2.VideoCapture(default_camera_index)

    # Get frame width and height dynamically
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
else:
    cap = None
    frame_width, frame_height = 640, 480  # Default values for black screen

# Function to get the class names (labels)
def get_yolo_labels():
    return model.names  # This is a dictionary {class_id: label_name}

class Tracker:
    def __init__(self):
        # Store the center positions of the objects
        self.center_points = {}
        # Keep the count of the IDs
        # each time a new object id detected, the count will increase by one
        self.id_count = 0


    def update(self, objects_rect):
        # Objects boxes and ids
        objects_bbs_ids = []

        # Get center point of new object
        for rect in objects_rect:
            x, y, w, h = rect
            cx = (x + x + w) // 2
            cy = (y + y + h) // 2

            # Find out if that object was detected already
            same_object_detected = False
            for id, pt in self.center_points.items():
                dist = math.hypot(cx - pt[0], cy - pt[1])

                if dist < 35:
                    self.center_points[id] = (cx, cy)
#                    print(self.center_points)
                    objects_bbs_ids.append([x, y, w, h, id])
                    same_object_detected = True
                    break

            # New object is detected we assign the ID to that object
            if same_object_detected is False:
                self.center_points[self.id_count] = (cx, cy)
                objects_bbs_ids.append([x, y, w, h, self.id_count])
                self.id_count += 1

        # Clean the dictionary by center points to remove IDS not used anymore
        new_center_points = {}
        for obj_bb_id in objects_bbs_ids:
            _, _, _, _, object_id = obj_bb_id
            center = self.center_points[object_id]
            new_center_points[object_id] = center

        # Update dictionary with IDs not used removed
        self.center_points = new_center_points.copy()
        return objects_bbs_ids
    
class WebcamManager:
    _instances = {}
    _lock = Lock()
    
    @classmethod
    def get_camera(cls, device_index):
        with cls._lock:
            if device_index not in cls._instances:
                cls._instances[device_index] = {
                    'cap': cv2.VideoCapture(device_index + cv2.CAP_DSHOW),
                    'last_used': time.time(),
                    'active_sessions': 0  # Track active sessions
                }
            instance = cls._instances[device_index]
            instance['active_sessions'] += 1
            instance['last_used'] = time.time()
            return instance['cap']

    @classmethod
    def release_camera(cls, device_index):
        with cls._lock:
            if device_index in cls._instances:
                cls._instances[device_index]['active_sessions'] -= 1
                if cls._instances[device_index]['active_sessions'] <= 0:
                    cls._instances[device_index]['cap'].release()
                    del cls._instances[device_index]

def putTextTransparent(frame, text, pos, scale=0.4, thickness=1, color=(255, 255, 255), bgColor=(0, 0, 0, 150)):
    x, y = pos
    (w, h), _ = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, scale, thickness)

    # Create an overlay for transparency
    overlay = frame.copy()
    cv2.rectangle(overlay, (x - 5, y - h - 5), (x + w + 5, y + 5), bgColor[:3], -1)
    
    # Blend the overlay with the original frame
    alpha = bgColor[3] / 255.0  # Normalize alpha value
    cv2.addWeighted(overlay, alpha, frame, 1 - alpha, 0, frame)
    
    # Put text on top
    cv2.putText(frame, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX, scale, color, thickness)

async def process_frame(frame, loop, face_detection_enabled, selected_labels, enable_counter,
                        outer_cy1, outer_cy2, outer_cx1, outer_cx2, offset, cy1, cy2, cx1, cx2, upcar, downcar, downbus, upbus, uptruck, downtruck,
                        downbicycle, upbicycle, upmotorcycle, downmotorcycle, going_out, going_in, 
                        countercarup, countercardown, counterbusdown, counterbicycledown, counterbicycleup, 
                        countermotorcycleup, countermotorcycledown, counterbusup, countertruckup, 
                        countertruckdown, counter1, counter2, area1, area2, area3, area4, yolo_labels, tracker0, tracker, 
                        tracker1, tracker2, tracker3, tracker4, upcar_speed, downcar_speed, downbus_speed, 
                        upbus_speed, uptruck_speed, downtruck_speed, downbicycle_speed, upbicycle_speed, 
                        upmotorcycle_speed, downmotorcycle_speed, countercarup_speed, countercardown_speed, 
                        counterbusdown_speed, counterbusup_speed, countertruckup_speed, countertruckdown_speed, 
                        counterbicycledown_speed, counterbicycleup_speed, countermotorcycleup_speed, countermotorcycledown_speed,
                        upcar_speed_dict, downcar_speed_dict, downbus_speed_dict, upbus_speed_dict, uptruck_speed_dict, 
                        downtruck_speed_dict, downbicycle_speed_dict, upbicycle_speed_dict, 
                        upmotorcycle_speed_dict, downmotorcycle_speed_dict):
    
    loop = asyncio.get_event_loop()
    confidence_threshold = 0.5   
    # Assign colors for each label
    colors = {class_id: (0, 255, 0) if label.lower() == "person" else (255, 0, 0) for class_id, label in yolo_labels.items()}

    if face_detection_enabled:
        results = await loop.run_in_executor(executor, lambda: model(frame, verbose=False, conf=0.5))

        filtered_detections = [
            detection for detection in results[0].boxes
            if detection.conf[0] >= confidence_threshold
        ]

        for detection in filtered_detections:
            bbox = detection.xyxy[0]  # Bounding box coordinates
            class_id = int(detection.cls[0])
            label = model.names[class_id]

            if label in selected_labels:
                color = colors[class_id]

                frame = cv2.rectangle(frame, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), color, 2)
                frame = cv2.putText(frame, f"{label} {detection.conf[0]:.2f}", (int(bbox[0]), int(bbox[1]) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                
        # Retrieve the class labels
        class_list = get_yolo_labels()

        if enable_counter:        
            a = results[0].boxes.cpu().data    
            px = pd.DataFrame(a).astype("float")

            list0, list, list1, list2, list3, list4 = [], [], [], [], [], []

            for index, row in px.iterrows():
                x1, y1, x2, y2, _, d = map(int, row[:6])  # Extract bbox coordinates & class ID
                class_name = class_list.get(d, "unknown")  # Get class name safely

                if 'person' in class_name:
                    list0.append([x1, y1, x2, y2])
                elif 'car' in class_name:
                    list.append([x1, y1, x2, y2])                             
                elif 'bus' in class_name:
                    list1.append([x1, y1, x2, y2])
                elif 'truck' in class_name:
                    list2.append([x1, y1, x2, y2])
                elif 'bicycle' in class_name:
                    list3.append([x1, y1, x2, y2])
                elif 'motorcycle' in class_name:
                    list4.append([x1, y1, x2, y2])

            bbox0_idx = tracker0.update(list0)
            bbox_idx=tracker.update(list)
            bbox1_idx=tracker1.update(list1)
            bbox2_idx=tracker2.update(list2)
            bbox3_idx=tracker3.update(list3)
            bbox4_idx=tracker4.update(list4)

            for bbox in bbox0_idx:
                x, y, x0, y0, id = bbox
                result = cv2.pointPolygonTest(np.array(area2, np.int32), ((x0, y0)), False)

                if result >= 0:
                    going_out[id] = (x0, y0)
                if id in going_out:
                    result1 = cv2.pointPolygonTest(np.array(area1, np.int32), ((x0, y0)), False)
                    if result1 >= 0:
                        cv2.circle(frame, (x0, y0), 7, (255, 0, 255), -1)    
                        cv2.rectangle(frame, (x, y), (x0, y0), (255, 255, 255), 2)
                        cvzone.putTextRect(frame, f'{id}', (x, y), 1, 1)
                        if counter1.count(id) == 0:
                            counter1.append(id)

                result2 = cv2.pointPolygonTest(np.array(area1, np.int32), ((x0, y0)), False)
                if result2 >= 0:
                    going_in[id] = (x0, y0)
                if id in going_in:
                    result3 = cv2.pointPolygonTest(np.array(area2, np.int32), ((x0, y0)), False)
                    if result3 >= 0:
                        cv2.circle(frame, (x0, y0), 7, (255, 0, 255), -1)    
                        cv2.rectangle(frame, (x, y), (x0, y0), (255, 0, 0), 2)
                        cvzone.putTextRect(frame, f'{id}', (x, y), 1, 1)
                        if counter2.count(id) == 0:
                            counter2.append(id)

            cv2.polylines(frame,[np.array(area1,np.int32)],True,(0, 0, 255),1)
            cv2.polylines(frame,[np.array(area2,np.int32)],True,(0, 0, 0),1)

            cv2.polylines(frame,[np.array(area3,np.int32)],True,(0, 0, 255),1)
            cv2.polylines(frame,[np.array(area4,np.int32)],True,(0, 0, 0),1)
            ###################### Car Up ########################################### 

            for bbox in bbox_idx:
                x3,y3,x4,y4,id1=bbox
                cx3=int(x3+x4)//2
                cy3=int(y3+y4)//2 
                if outer_cy1<(cy3+offset) and outer_cy1>(cy3-offset):  
                    upcar[id1]=(cx3,cy3) 
                    upcar_speed[id1]=time.time()   # current time when vehichle touch the first line 
                if id1 in upcar:  
                    if outer_cy2<(cy3+offset) and outer_cy2>(cy3-offset): 
                        elapsed_time_up_car=time.time() - upcar_speed[id1] 
                        if id1 not in countercarup_speed:
                            a_speed_ms_upcar = distance / elapsed_time_up_car
                            a_speed_kh_upcar = a_speed_ms_upcar * 3.6  
                            upcar_speed_dict[id1] = int(a_speed_kh_upcar)  # Store speed
                        if countercarup.count(id1)==0:
                            countercarup.append(id1) 
                            
                cv2.circle(frame,(cx3,cy3),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x3, y3), (x4, y4), (0, 255, 0), 2)

                if id1 in upcar_speed_dict and upcar_speed_dict[id1] <= speed_threshold:
                    cv2.putText(frame, f'{upcar_speed_dict[id1]} Km/h', (x4, y4), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)
                if id1 in upcar_speed_dict and upcar_speed_dict[id1] > speed_threshold:
                    cv2.putText(frame, f'{upcar_speed_dict[id1]} Km/h', (x4, y4), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
                ###################### Car Dawn ###########################################   
                if outer_cy2<(cy3+offset) and outer_cy2>(cy3-offset):  
                    downcar[id1]=(cx3,cy3)
                    downcar_speed[id1]=time.time()   # current time when vehichle touch the first line   
                if id1 in downcar:  
                    if outer_cy1<(cy3+offset) and outer_cy1>(cy3-offset): 
                        elapsed_time_down_car=time.time() - downcar_speed[id1]  
                        if id1 not in countercardown_speed:
                            a_speed_ms_downcar = distance / elapsed_time_down_car
                            a_speed_kh_downcar = a_speed_ms_downcar * 3.6 
                            downcar_speed_dict[id1] = int(a_speed_kh_downcar)  # Store speed
                        if countercardown.count(id1)==0: 
                            countercardown.append(id1) 

                cv2.circle(frame,(cx3,cy3),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x3, y3), (x4, y4), (0, 255, 0), 2)

                if id1 in downcar_speed_dict and downcar_speed_dict[id1] <= speed_threshold:
                    cv2.putText(frame, f'{downcar_speed_dict[id1]} Km/h', (x4, y4), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)
                if id1 in downcar_speed_dict and downcar_speed_dict[id1] > speed_threshold:
                    cv2.putText(frame, f'{downcar_speed_dict[id1]} Km/h', (x4, y4), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
            ###################### Bus Up ###########################################     
            for bbox1 in bbox1_idx:  
                x5, y5, x6, y6, id2 = bbox1  
                cx4 = int(x5 + x6) // 2  
                cy4 = int(y5 + y6) // 2  
                if outer_cy1 < (cy4 + offset) and outer_cy1 > (cy4 - offset):  
                    upbus[id2] = (cx4, cy4) 
                    upbus_speed[id2]=time.time()   # current time when vehichle touch the first line  
                if id2 in upbus:  
                    if outer_cy2 < (cy4 + offset) and outer_cy2 > (cy4 - offset):  
                        elapsed_time_up_bus=time.time() - upbus_speed[id2] 
                        if id2 not in counterbusup_speed:
                            a_speed_ms_upbus = distance / elapsed_time_up_bus
                            a_speed_kh_upbus = a_speed_ms_upbus * 3.6 
                            upbus_speed_dict[id2] = int(a_speed_kh_upbus)  # Store speed
                        if counterbusup.count(id2) == 0:  
                            counterbusup.append(id2)

                cv2.circle(frame,(cx4,cy4),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x5, y5), (x6, y6), (0, 255, 0), 2)

                if id2 in upbus_speed_dict and upbus_speed_dict[id2] <= speed_threshold:
                    cv2.putText(frame, f'{upbus_speed_dict[id2]} Km/h', (x6, y6), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)
                if id2 in upbus_speed_dict and upbus_speed_dict[id2] > speed_threshold:
                    cv2.putText(frame, f'{upbus_speed_dict[id2]} Km/h', (x6, y6), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
                ###################### Bus down ###########################################
                if outer_cy2 < (cy4 + offset) and outer_cy2 > (cy4 - offset):  
                    downbus[id2] = (cx4, cy4)
                    downbus_speed[id2]=time.time()   # current time when vehichle touch the first line   
                if id2 in downbus:  
                    if outer_cy1 < (cy4 + offset) and outer_cy1 > (cy4 - offset):
                        elapsed_time_down_bus=time.time() - downbus_speed[id2] 
                        if id2 not in counterbusdown_speed:
                            a_speed_ms_downbus = distance / elapsed_time_down_bus
                            a_speed_kh_downbus = a_speed_ms_downbus * 3.6 
                            downbus_speed_dict[id2] = int(a_speed_kh_downbus)  # Store speed
                        if counterbusdown.count(id2) == 0:  
                            counterbusdown.append(id2)

                cv2.circle(frame,(cx4,cy4),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x5, y5), (x6, y6), (0, 255, 0), 2)

                if id2 in downbus_speed_dict and downbus_speed_dict[id2] <= speed_threshold:
                    cv2.putText(frame, f'{downbus_speed_dict[id2]} Km/h', (x6, y6), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)
                if id2 in downbus_speed_dict and downbus_speed_dict[id2] > speed_threshold:
                    cv2.putText(frame, f'{downbus_speed_dict[id2]} Km/h', (x6, y6), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
            ###################### Truck Up ###########################################     
            for bbox2 in bbox2_idx:  
                x7, y7, x8, y8, id3 = bbox2  
                cx5 = int(x7 + x8) // 2  
                cy5 = int(y7 + y8) // 2  
                if outer_cy1 < (cy5 + offset) and outer_cy1 > (cy5 - offset):  
                    uptruck[id3] = (cx5, cy5) 
                    uptruck_speed[id3]=time.time()   # current time when vehichle touch the first line  
                if id3 in uptruck:  
                    if outer_cy2 < (cy5 + offset) and outer_cy2 > (cy5 - offset): 
                        elapsed_time_up_truck=time.time() - uptruck_speed[id3] 
                        if id3 not in countertruckup_speed:
                            a_speed_ms_uptruck = distance / elapsed_time_up_truck
                            a_speed_kh_uptruck = a_speed_ms_uptruck * 3.6 
                            uptruck_speed_dict[id3] = int(a_speed_kh_uptruck)  # Store speed
                        if countertruckup.count(id3) == 0:  
                            countertruckup.append(id3)

                cv2.circle(frame,(cx5,cy5),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x7, y7), (x8, y8), (0, 255, 0), 2)

                if id3 in uptruck_speed_dict and uptruck_speed_dict[id3] <= speed_threshold:
                    cv2.putText(frame, f'{uptruck_speed_dict[id3]} Km/h', (x8, y8), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)
                if id3 in uptruck_speed_dict and uptruck_speed_dict[id3] > speed_threshold:
                    cv2.putText(frame, f'{uptruck_speed_dict[id3]} Km/h', (x8, y8), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
                ###################### Truck down ###########################################
                if outer_cy2 < (cy5 + offset) and outer_cy2 > (cy5 - offset):  
                    downtruck[id3] = (cx5, cy5) 
                    downtruck_speed[id3]=time.time()   # current time when vehichle touch the first line  
                if id3 in downtruck:  
                    if outer_cy1 < (cy5 + offset) and outer_cy1 > (cy5 - offset): 
                        elapsed_time_down_truck=time.time() - downtruck_speed[id3] 
                        if id3 not in countertruckdown_speed:
                            a_speed_ms_downtruck = distance / elapsed_time_down_truck
                            a_speed_kh_downtruck = a_speed_ms_downtruck * 3.6 
                            downtruck_speed_dict[id3] = int(a_speed_kh_downtruck)  # Store speed
                        if countertruckdown.count(id3) == 0:  
                            countertruckdown.append(id3)

                cv2.circle(frame,(cx5,cy5),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x7, y7), (x8, y8), (0, 255, 0), 2)

                if id3 in downtruck_speed_dict and downtruck_speed_dict[id3] <= speed_threshold:
                    cv2.putText(frame, f'{downtruck_speed_dict[id3]} Km/h', (x8, y8), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2) 
                if id3 in downtruck_speed_dict and downtruck_speed_dict[id3] > speed_threshold:
                    cv2.putText(frame, f'{downtruck_speed_dict[id3]} Km/h', (x8, y8), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2) 
            ###################### bicycle Up ###########################################     
            for bbox3 in bbox3_idx:  
                x9, y9, x10, y10, id4 = bbox3 
                cx6 = int(x9 + x10) // 2  
                cy6 = int(y9 + y10) // 2  
                if outer_cy1 < (cy6 + offset) and outer_cy1 > (cy6 - offset):  
                    upbicycle[id4] = (cx6, cy6)
                    upbicycle_speed[id4]=time.time()   # current time when vehichle touch the first line   
                if id4 in upbicycle:  
                    if outer_cy2 < (cy6 + offset) and outer_cy2 > (cy6 - offset):  
                        elapsed_time_up_bicycle=time.time() - upbicycle_speed[id4]
                        if id4 not in counterbicycleup_speed:
                            a_speed_ms_upbicycle = distance / elapsed_time_up_bicycle
                            a_speed_kh_upbicycle = a_speed_ms_upbicycle * 3.6 
                            upbicycle_speed_dict[id4] = int(a_speed_kh_upbicycle)  # Store speed
                        if counterbicycleup.count(id4) == 0:  
                            counterbicycleup.append(id4)

                cv2.circle(frame,(cx6,cy6),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x9, y9), (x10, y10), (0, 255, 0), 2)

                if id4 in upbicycle_speed_dict and upbicycle_speed_dict[id4] <= speed_threshold:
                    cv2.putText(frame, f'{upbicycle_speed_dict[id4]} Km/h', (x10, y10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)
                if id4 in upbicycle_speed_dict and upbicycle_speed_dict[id4] > speed_threshold:
                    cv2.putText(frame, f'{upbicycle_speed_dict[id4]} Km/h', (x10, y10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
                ###################### bicycle down ###########################################
                if outer_cy2 < (cy6 + offset) and outer_cy2 > (cy6 - offset):  
                    downbicycle[id4] = (cx6, cy6)
                    downbicycle_speed[id4]=time.time()   # current time when vehichle touch the first line   
                if id4 in downbicycle:  
                    if outer_cy1 < (cy6 + offset) and outer_cy1 > (cy6 - offset):
                        elapsed_time_down_bicycle=time.time() - downbicycle_speed[id4]
                        if id4 not in counterbicycledown_speed:
                            a_speed_ms_downbicycle = distance / elapsed_time_down_bicycle
                            a_speed_kh_downbicycle = a_speed_ms_downbicycle * 3.6 
                            downbicycle_speed_dict[id4] = int(a_speed_kh_downbicycle)  # Store speed
                        if counterbicycledown.count(id4) == 0:  
                            counterbicycledown.append(id4)

                cv2.circle(frame,(cx6,cy6),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x9, y9), (x10, y10), (0, 255, 0), 2)

                if id4 in downbicycle_speed_dict and downbicycle_speed_dict[id4] <= speed_threshold:
                    cv2.putText(frame, f'{downbicycle_speed_dict[id4]} Km/h', (x10, y10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)
                if id4 in downbicycle_speed_dict and downbicycle_speed_dict[id4] > speed_threshold:
                    cv2.putText(frame, f'{downbicycle_speed_dict[id4]} Km/h', (x10, y10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
            ###################### motorcycle Up ###########################################     
            for bbox4 in bbox4_idx:  
                x11, y11, x12, y12, id5 = bbox4 
                cx7 = int(x11 + x12) // 2  
                cy7 = int(y11 + y12) // 2  
                if outer_cy1 < (cy7 + offset) and outer_cy1 > (cy7 - offset):  
                    upmotorcycle[id5] = (cx7, cy7)
                    upmotorcycle_speed[id5]=time.time()   # current time when vehichle touch the first line   
                if id5 in upmotorcycle:  
                    if outer_cy2 < (cy7 + offset) and outer_cy2 > (cy7 - offset): 
                        elapsed_time_up_motorcycle=time.time() - upmotorcycle_speed[id5] 
                        if id5 not in countermotorcycleup_speed:
                            a_speed_ms_upmotorcycle = distance / elapsed_time_up_motorcycle
                            a_speed_kh_upmotorcycle = a_speed_ms_upmotorcycle * 3.6 
                            upmotorcycle_speed_dict[id5] = int(a_speed_kh_upmotorcycle)  # Store speed
                        if countermotorcycleup.count(id5) == 0:  
                            countermotorcycleup.append(id5)

                cv2.circle(frame,(cx6,cy6),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x11, y11), (x12, y12), (0, 255, 0), 2)

                if id5 in upmotorcycle_speed_dict and upmotorcycle_speed_dict[id5] <= speed_threshold:
                    cv2.putText(frame, f'{upmotorcycle_speed_dict[id5]} Km/h', (x12, y12), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2) 
                if id5 in upmotorcycle_speed_dict and upmotorcycle_speed_dict[id5] > speed_threshold:
                    cv2.putText(frame, f'{upmotorcycle_speed_dict[id5]} Km/h', (x12, y12), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 2)
                ###################### motorcycle down ###########################################
                if outer_cy2 < (cy7 + offset) and outer_cy2 > (cy7 - offset):  
                    downmotorcycle[id5] = (cx7, cy7) 
                    downmotorcycle_speed[id5]=time.time()   # current time when vehichle touch the first line   
                if id5 in downmotorcycle:  
                    if outer_cy1 < (cy7 + offset) and outer_cy1 > (cy7 - offset): 
                        elapsed_time_down_motorcycle=time.time() - downmotorcycle_speed[id5]
                        if id5 not in countermotorcycledown_speed:
                            a_speed_ms_downmotorcycle = distance / elapsed_time_down_motorcycle
                            a_speed_kh_downmotorcycle = a_speed_ms_downmotorcycle * 3.6 
                            downmotorcycle_speed_dict[id5] = int(a_speed_kh_downmotorcycle)  # Store speed
                        if countermotorcycledown.count(id5) == 0:  
                            countermotorcycledown.append(id5)

                cv2.circle(frame,(cx6,cy6),4,(255,0,0),-1)
                # Draw bounding box and display stored speed if available
                cv2.rectangle(frame, (x11, y11), (x12, y12), (0, 255, 0), 2)

                if id5 in downmotorcycle_speed_dict and downmotorcycle_speed_dict[id5] <= speed_threshold:
                    cv2.putText(frame, f'{downmotorcycle_speed_dict[id5]} Km/h', (x12, y12), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2) 
                if id5 in downmotorcycle_speed_dict and downmotorcycle_speed_dict[id5] > speed_threshold:
                    cv2.putText(frame, f'{downmotorcycle_speed_dict[id5]} Km/h', (x12, y12), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 2)



            person_out = len(counter1)
            person_in = len(counter2)
            cup = len(countercarup)  
            cdown = len(countercardown)  
            cbusup = len(counterbusup)  
            cbusdown = len(counterbusdown) 
            ctruckup = len(countertruckup)  
            ctruckdown = len(countertruckdown)
            cbicycleup = len(counterbicycleup)  
            cbicycledown = len(counterbicycledown) 
            cmotorcycleup = len(countermotorcycleup)  
            cmotorcycledown = len(countermotorcycledown)

            putTextTransparent(frame, f'Person in: {person_in}', (30, 30))
            putTextTransparent(frame, f'Person out: {person_out}', (30, 55))
            putTextTransparent(frame, f'Car in: {cup}', (30, 80))
            putTextTransparent(frame, f'Car out: {cdown}', (30, 105))
            putTextTransparent(frame, f'Bus in: {cbusup}', (30, 130))
            putTextTransparent(frame, f'Bus out: {cbusdown}', (30, 155))
            putTextTransparent(frame, f'Truck in: {ctruckup}', (30, 180))
            putTextTransparent(frame, f'Truck out: {ctruckdown}', (30, 205))
            putTextTransparent(frame, f'Bicycle in: {cbicycleup}', (30, 230))
            putTextTransparent(frame, f'Bicycle out: {cbicycledown}', (30, 255))
            putTextTransparent(frame, f'Motorcycle in: {cmotorcycleup}', (30, 280))
            putTextTransparent(frame, f'Motorcycle out: {cmotorcycledown}', (30, 305))

    return frame


async def generate_frames(device_index, face_detection_enabled, selected_labels, enable_counter, horizontal_pos, vertical_pos, mode):
    loop = asyncio.get_event_loop()
    frame_count = 0
    retry_attempts = 5  # Max number of retry attempts
    initial_delay = 2  # Initial delay in seconds
    max_delay = 10  # Maximum wait time before retrying, total retry time 60 seconds.
    while retry_attempts > 0:
        cap = await loop.run_in_executor(executor, lambda: WebcamManager.get_camera(device_index))
        prev_time = time.time()  # Track previous frame time

        if not cap.isOpened():
            await asyncio.sleep(min(initial_delay * (2 ** (5 - retry_attempts)), max_delay))
            retry_attempts -= 1
            continue  # Retry connection
        try:
            print("mode: ===============", mode)
            cy1= (frame_height // 2) - (gap)
            cy2= (frame_height // 2) + (gap)
            cx1= (frame_width // 2) - (gap)
            cx2= (frame_width // 2) + (gap)

            offset=8
            upcar={}
            downcar={}
            downbus={}
            upbus={}
            uptruck={}
            downtruck={}
            downbicycle={}
            upbicycle={}
            upmotorcycle={}
            downmotorcycle={}
            going_out = {}
            going_in = {} 

            upcar_speed={}
            downcar_speed={}
            downbus_speed={}
            upbus_speed={}
            uptruck_speed={}
            downtruck_speed={}
            downbicycle_speed={}
            upbicycle_speed={}
            upmotorcycle_speed={}
            downmotorcycle_speed={}

            upcar_speed_dict={}
            downcar_speed_dict={}
            downbus_speed_dict={}
            upbus_speed_dict={}
            uptruck_speed_dict={}
            downtruck_speed_dict={}
            downbicycle_speed_dict={}
            upbicycle_speed_dict={}
            upmotorcycle_speed_dict={}
            downmotorcycle_speed_dict={}

            countercarup_speed=[]
            countercardown_speed=[]
            counterbusdown_speed=[]
            counterbusup_speed=[]
            countertruckup_speed=[]
            countertruckdown_speed=[]
            counterbicycledown_speed=[]
            counterbicycleup_speed=[]
            countermotorcycleup_speed=[]
            countermotorcycledown_speed=[]

            countercarup=[]
            countercardown=[]
            counterbusdown=[]
            counterbusup=[]
            countertruckup=[]
            countertruckdown=[]
            counterbicycledown=[]
            counterbicycleup=[]
            countermotorcycleup=[]
            countermotorcycledown=[]
            counter1 = []
            counter2 = [] 

            # Dynamically calculate the midpoint while maintaining the gap
            mid_x = (frame_width // 2) - (gap // 2)
            # Define areas based on the calculated width and height
            area1 = [(2, 2), (2, frame_height - 2), (mid_x, frame_height - 2), (mid_x, 2)]
            area2 = [(mid_x + gap, 2), (mid_x + gap, frame_height - 2), (frame_width - 2, frame_height - 2), (frame_width - 2, 2)]

            mid_y = (frame_height // 2) - (gap // 2)
            area3 = [
                (2, 2), 
                (frame_width - 2, 2), 
                (frame_width - 2, mid_y), 
                (2, mid_y)
            ]
            area4 = [
                (2, mid_y + gap), 
                (frame_width - 2, mid_y + gap), 
                (frame_width - 2, frame_height - 2), 
                (2, frame_height - 2)
            ]

            yolo_labels = get_yolo_labels()
            tracker0=Tracker()
            tracker=Tracker()
            tracker1=Tracker()
            tracker2=Tracker()
            tracker3=Tracker()
            tracker4=Tracker()

            while True:
                if cap and cap.isOpened():
                    start_time = time.time()  # Start time for new frame
                    ret, frame = await loop.run_in_executor(None, cap.read)

                    if not ret:
                        retry_wait = min(initial_delay * (2 ** (5 - retry_attempts)), max_delay)
                        print(f"[WARNING] Camera {device_index} lost connection. Retrying in {retry_wait} seconds...")
                        await asyncio.sleep(retry_wait)
                        retry_attempts -= 1
                        if retry_attempts <= 0:
                            break  # Exit loop after max retries
                        continue

                    # Calculate dynamic FPS
                    elapsed_time = start_time - prev_time
                    prev_time = start_time
                    fps = 1.0 / elapsed_time if elapsed_time > 0 else 30  # Prevent division by zero
                    fps = min(fps, 60)  # Limit FPS to 60 to avoid instability

                    frame_count += 1
                    if frame_count % FRAME_SKIP != 0:  # Skip frames for efficiency
                        continue
                else:
                    # Create a black screen when no camera is available
                    frame = np.zeros((480, 640, 3), dtype=np.uint8)
                    message = f"No camera detected at {device_index}"
                    font = cv2.FONT_HERSHEY_SIMPLEX
                    text_size = cv2.getTextSize(message, font, 1, 2)[0]
                    text_x = (frame.shape[1] - text_size[0]) // 2
                    text_y = (frame.shape[0] + text_size[1]) // 2
                    cv2.putText(frame, message, (text_x, text_y), font, 1, (0, 0, 255), 2, cv2.LINE_AA)

                # Process the frame if face detection is enabled
                if face_detection_enabled:
                    outer_cy1 = int(horizontal_pos.get('outer_cy1', 0.6) * frame_height)
                    outer_cy2 = int(horizontal_pos.get('outer_cy2', 0.7) * frame_height)

                    outer_cx1 = int(vertical_pos.get('outer_cx1', 0.3) * frame_width)
                    outer_cx2 = int(vertical_pos.get('outer_cx2', 0.7) * frame_width)

                    frame = await process_frame(frame, loop, face_detection_enabled, selected_labels, enable_counter,
                        outer_cy1, outer_cy2, outer_cx1, outer_cx2, offset, cy1, cy2, cx1, cx2, upcar, downcar, downbus, upbus, uptruck, downtruck,
                        downbicycle, upbicycle, upmotorcycle, downmotorcycle, going_out, going_in, 
                        countercarup, countercardown, counterbusdown, counterbicycledown, counterbicycleup, 
                        countermotorcycleup, countermotorcycledown, counterbusup, countertruckup, 
                        countertruckdown, counter1, counter2, area1, area2, area3, area4, yolo_labels, tracker0, tracker, 
                        tracker1, tracker2, tracker3, tracker4, upcar_speed, downcar_speed, downbus_speed, 
                        upbus_speed, uptruck_speed, downtruck_speed, downbicycle_speed, upbicycle_speed, 
                        upmotorcycle_speed, downmotorcycle_speed, countercarup_speed, countercardown_speed, 
                        counterbusdown_speed, counterbusup_speed, countertruckup_speed, countertruckdown_speed, 
                        counterbicycledown_speed, counterbicycleup_speed, countermotorcycleup_speed, countermotorcycledown_speed,
                        upcar_speed_dict, downcar_speed_dict, downbus_speed_dict, upbus_speed_dict, uptruck_speed_dict, 
                        downtruck_speed_dict, downbicycle_speed_dict, upbicycle_speed_dict, 
                        upmotorcycle_speed_dict, downmotorcycle_speed_dict)


                # Encode the frame
                ret, buffer = await loop.run_in_executor(
                    executor,
                    lambda: cv2.imencode('.jpg', frame, [int(cv2.IMWRITE_JPEG_QUALITY), 70])
                )
                
                if not ret:
                    continue

                yield (b'--frame\r\n'
                    b'Content-Type: image/jpeg\r\n\r\n' + buffer.tobytes() + b'\r\n')

        finally:
            await loop.run_in_executor(None, WebcamManager.release_camera, device_index)


async def generate_raw_frames(device_index):
    loop = asyncio.get_event_loop()
    frame_count = 0
    retry_attempts = 5  # Max number of retry attempts
    initial_delay = 2  # Initial delay in seconds
    max_delay = 10  # Maximum wait time before retrying, total retry time ~60 Seconds.

    while retry_attempts > 0:
        cap = await loop.run_in_executor(executor, lambda: WebcamManager.get_camera(device_index))
        prev_time = time.time()  # Track previous frame time

        if not cap.isOpened():
            await asyncio.sleep(min(initial_delay * (2 ** (5 - retry_attempts)), max_delay))
            retry_attempts -= 1
            continue  # Retry connection

        try:
            while True:
                if cap and cap.isOpened():
                    start_time = time.time()  # Start time for new frame
                    ret, frame = await loop.run_in_executor(None, cap.read)

                    if not ret:
                        retry_wait = min(initial_delay * (2 ** (5 - retry_attempts)), max_delay)
                        print(f"[WARNING] Camera {device_index} lost connection. Retrying in {retry_wait} seconds...")
                        await asyncio.sleep(retry_wait)
                        retry_attempts -= 1
                        if retry_attempts <= 0:
                            break  # Exit loop after max retries
                        continue

                    # Calculate dynamic FPS
                    elapsed_time = start_time - prev_time
                    prev_time = start_time
                    fps = 1.0 / elapsed_time if elapsed_time > 0 else 30  # Prevent division by zero
                    fps = min(fps, 60)  # Limit FPS to 60 to avoid instability

                    frame_count += 1
                    if frame_count % FRAME_SKIP != 0:  # Skip frames for efficiency
                        continue
                else:
                    # Create a black screen when no camera is available
                    frame = np.zeros((480, 640, 3), dtype=np.uint8)
                    message = f"No camera detected at {device_index}"
                    font = cv2.FONT_HERSHEY_SIMPLEX
                    text_size = cv2.getTextSize(message, font, 1, 2)[0]
                    text_x = (frame.shape[1] - text_size[0]) // 2
                    text_y = (frame.shape[0] + text_size[1]) // 2
                    cv2.putText(frame, message, (text_x, text_y), font, 1, (0, 0, 255), 2, cv2.LINE_AA)

                # Encode the frame
                ret, buffer = await loop.run_in_executor(
                    executor,
                    lambda: cv2.imencode('.jpg', frame, [int(cv2.IMWRITE_JPEG_QUALITY), 70])
                )
                
                if not ret:
                    continue
                    
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' +
                       buffer.tobytes() + b'\r\n')

                await asyncio.sleep(1 / fps)  # Adjust based on FPS

        finally:
            await loop.run_in_executor(executor, lambda: WebcamManager.release_camera(device_index))

