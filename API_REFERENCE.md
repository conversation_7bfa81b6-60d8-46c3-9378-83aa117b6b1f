# 🎖️ Face Recognition API Reference

## 🚀 **Complete API Documentation**

### **Base URL**
```
http://localhost:8090/api/v1/
```

### **Authentication**
Currently using development mode. In production, implement JWT tokens.

---

## 👥 **Person Management API**

### **Create Person**
```http
POST /persons/
```

**Request Body:**
```json
{
  "name": "<PERSON>",
  "employee_id": "EMP001",
  "department": "Security",
  "role": "Security Guard",
  "email": "<EMAIL>",
  "phone": "******-0123",
  "notes": "Additional information"
}
```

**Response:**
```json
{
  "success": true,
  "person": {
    "id": 1,
    "name": "<PERSON>",
    "employee_id": "EMP001",
    "department": "Security",
    "role": "Security Guard",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "status": "active",
    "notes": "Additional information",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z",
    "face_count": 0
  },
  "processing_time_ms": 45.2,
  "timestamp": **********.789
}
```

### **List Persons**
```http
GET /persons/?skip=0&limit=100&status=active&department=Security
```

**Query Parameters:**
- `skip` (int): Number of records to skip (pagination)
- `limit` (int): Maximum records to return (max 100)
- `status` (string): Filter by status (active, inactive, suspended)
- `department` (string): Filter by department

**Response:**
```json
{
  "success": true,
  "pagination": {
    "skip": 0,
    "limit": 100,
    "total": 25
  },
  "filters": {
    "status": "active",
    "department": "Security"
  },
  "persons": [
    {
      "id": 1,
      "name": "John Doe",
      "employee_id": "EMP001",
      "department": "Security",
      "role": "Security Guard",
      "status": "active",
      "face_count": 2,
      "created_at": "2024-01-01T12:00:00Z"
    }
  ],
  "processing_time_ms": 12.5,
  "timestamp": **********.789
}
```

### **Get Person by ID**
```http
GET /persons/{person_id}
```

**Response:**
```json
{
  "success": true,
  "person": {
    "id": 1,
    "name": "John Doe",
    "employee_id": "EMP001",
    "department": "Security",
    "role": "Security Guard",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "status": "active",
    "notes": "Additional information",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z",
    "face_count": 2
  },
  "processing_time_ms": 8.3,
  "timestamp": **********.789
}
```

### **Update Person**
```http
PUT /persons/{person_id}
```

**Request Body (partial updates allowed):**
```json
{
  "name": "John Smith (Corrected)",
  "department": "Updated Department",
  "status": "active"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Person 'John Smith (Corrected)' updated successfully",
  "original_data": {
    "name": "John Doe",
    "department": "Security"
  },
  "updated_person": {
    "id": 1,
    "name": "John Smith (Corrected)",
    "employee_id": "EMP001",
    "department": "Updated Department",
    "status": "active",
    "updated_at": "2024-01-01T12:30:00Z",
    "face_count": 2
  },
  "processing_time_ms": 23.1,
  "timestamp": **********.789
}
```

### **Delete Person**
```http
DELETE /persons/{person_id}?confirm=true
DELETE /persons/by-name/{person_name}?confirm=true
DELETE /persons/by-employee-id/{employee_id}?confirm=true
```

**Query Parameters:**
- `confirm` (boolean): **Required** - Must be `true` to confirm deletion

**Response:**
```json
{
  "success": true,
  "message": "Person 'John Doe' and all associated data deleted successfully",
  "deleted_data": {
    "person_id": 1,
    "name": "John Doe",
    "employee_id": "EMP001",
    "face_records_deleted": 2
  },
  "processing_time_ms": 67.4,
  "timestamp": **********.789
}
```

### **Delete Face Features Only**
```http
DELETE /persons/{person_id}/faces?confirm=true
```

**Response:**
```json
{
  "success": true,
  "message": "Deleted 2 face records for person 'John Doe'",
  "deleted_data": {
    "person_id": 1,
    "person_name": "John Doe",
    "face_records_deleted": 2,
    "person_record_kept": true
  },
  "processing_time_ms": 34.2,
  "timestamp": **********.789
}
```

---

## 🎭 **Face Operations API**

### **Add Face to Person**
```http
POST /persons/{person_id}/faces
```

**Request (multipart/form-data):**
- `file` (file): Image file (JPEG, PNG)
- `is_primary` (boolean): Whether this is the primary face

**Response:**
```json
{
  "success": true,
  "face_record": {
    "id": 1,
    "person_id": 1,
    "image_path": "faces/persons/1/abc123.jpg",
    "image_hash": "abc123def456...",
    "image_size": 245760,
    "bounding_box": [120, 80, 320, 280],
    "detection_confidence": 0.95,
    "feature_version": "enhanced_arcface_v1",
    "is_primary": true,
    "status": "active",
    "created_at": "2024-01-01T12:00:00Z"
  },
  "processing_info": {
    "faces_detected": 1,
    "feature_size": 512
  },
  "processing_time_ms": 2340.5,
  "timestamp": **********.789
}
```

### **Face Detection**
```http
POST /detection/detect
```

**Request (multipart/form-data):**
- `file` (file): Image file
- `confidence_threshold` (float): Detection confidence (default: 0.25)

**Response:**
```json
{
  "success": true,
  "image_info": {
    "filename": "test_image.jpg",
    "size": 245760,
    "dimensions": [640, 480],
    "format": "image/jpeg"
  },
  "detection_info": {
    "model": "YOLOv11",
    "confidence_threshold": 0.25,
    "faces_detected": 2,
    "processing_device": "cpu"
  },
  "faces": [
    {
      "bbox": [120, 80, 320, 280],
      "confidence": 0.95,
      "area": 40000,
      "center": [220, 180]
    },
    {
      "bbox": [400, 100, 580, 300],
      "confidence": 0.87,
      "area": 36000,
      "center": [490, 200]
    }
  ],
  "gpu_info": {
    "cuda_available": false,
    "device": "cpu"
  },
  "processing_time_ms": 2340.5,
  "timestamp": **********.789
}
```

### **Face Recognition**
```http
POST /recognition/recognize
```

**Request (multipart/form-data):**
- `file` (file): Image file
- `recognition_threshold` (float): Recognition confidence (default: 0.5)

**Response:**
```json
{
  "success": true,
  "image_info": {
    "filename": "test_image.jpg",
    "size": 245760,
    "dimensions": [640, 480]
  },
  "recognition_info": {
    "model": "Enhanced ArcFace",
    "recognition_threshold": 0.5,
    "faces_processed": 1,
    "matches_found": 1
  },
  "faces": [
    {
      "detection": {
        "bbox": [120, 80, 320, 280],
        "confidence": 0.95
      },
      "recognition": {
        "person_id": 1,
        "person_name": "John Doe",
        "confidence": 0.87,
        "is_match": true,
        "threshold_used": 0.5
      },
      "features": {
        "feature_size": 512,
        "feature_version": "enhanced_arcface_v1"
      }
    }
  ],
  "processing_time_ms": 3240.8,
  "timestamp": **********.789
}
```

---

## 📊 **System Information API**

### **Health Check**
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "service": "face-recognition-service",
  "version": "1.0.0",
  "timestamp": **********.789
}
```

### **Service Information**
```http
GET /info
```

**Response:**
```json
{
  "service": "face-recognition-service",
  "version": "1.0.0",
  "description": "AI-powered face recognition system",
  "ai_models": {
    "detection": "YOLOv11",
    "recognition": "Enhanced ArcFace"
  },
  "capabilities": [
    "face_detection",
    "face_recognition",
    "person_management",
    "database_operations"
  ],
  "gpu_info": {
    "cuda_available": false,
    "device": "cpu",
    "gpu_name": null
  },
  "timestamp": **********.789
}
```

### **Statistics**
```http
GET /persons/stats
```

**Response:**
```json
{
  "success": true,
  "statistics": {
    "total_persons": 25,
    "status_breakdown": {
      "active": 23,
      "inactive": 2,
      "suspended": 0
    },
    "department_breakdown": {
      "Security": 15,
      "Administration": 8,
      "Maintenance": 2
    },
    "total_face_records": 47,
    "avg_faces_per_person": 1.88
  },
  "processing_time_ms": 15.3,
  "timestamp": **********.789
}
```

---

## 🎥 **Camera Integration API**

### **Process Shinobi Frame**
```http
POST /api/face-recognition/process-shinobi-frame/
```

**Request (multipart/form-data):**
- `camera_id` (string): Camera identifier
- `monitor_id` (string): Shinobi monitor ID
- `image` (file): Captured frame

**Response:**
```json
{
  "success": true,
  "camera_info": {
    "camera_id": "camera_001",
    "monitor_id": "8IeKtyU00l"
  },
  "faces_detected": 1,
  "faces_recognized": 1,
  "recognitions": [
    {
      "detection": {
        "bbox": [120, 80, 320, 280],
        "confidence": 0.95
      },
      "recognition": {
        "person_id": 1,
        "person_name": "John Doe",
        "confidence": 0.87,
        "is_match": true
      }
    }
  ],
  "processing_time_ms": 2840.3,
  "timestamp": **********.789
}
```

---

## 🚨 **Error Responses**

### **Standard Error Format**
```json
{
  "success": false,
  "error": {
    "code": "PERSON_NOT_FOUND",
    "message": "Person with ID 999 not found",
    "details": "The requested person does not exist in the database"
  },
  "timestamp": **********.789
}
```

### **Common Error Codes**

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `PERSON_NOT_FOUND` | 404 | Person does not exist |
| `INVALID_IMAGE` | 400 | Invalid image format |
| `NO_FACES_DETECTED` | 400 | No faces found in image |
| `MULTIPLE_FACES` | 400 | Multiple faces in single-face operation |
| `CONFIRMATION_REQUIRED` | 400 | Deletion requires confirmation |
| `VALIDATION_ERROR` | 422 | Request validation failed |
| `INTERNAL_ERROR` | 500 | Server error |

---

## 🎯 **Rate Limits**

| Endpoint | Limit | Window |
|----------|-------|--------|
| Face Detection | 60 requests | 1 minute |
| Face Recognition | 30 requests | 1 minute |
| Person Management | 100 requests | 1 minute |
| General API | 1000 requests | 1 hour |

---

## 🔧 **SDK Examples**

### **Python SDK**
```python
import requests

class FaceRecognitionClient:
    def __init__(self, base_url="http://localhost:8090/api/v1"):
        self.base_url = base_url
    
    def create_person(self, name, **kwargs):
        data = {"name": name, **kwargs}
        response = requests.post(f"{self.base_url}/persons/", json=data)
        return response.json()
    
    def detect_faces(self, image_path):
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{self.base_url}/detection/detect", files=files)
        return response.json()

# Usage
client = FaceRecognitionClient()
person = client.create_person("John Doe", employee_id="EMP001")
result = client.detect_faces("photo.jpg")
```

### **JavaScript SDK**
```javascript
class FaceRecognitionAPI {
    constructor(baseUrl = 'http://localhost:8090/api/v1') {
        this.baseUrl = baseUrl;
    }
    
    async createPerson(personData) {
        const response = await fetch(`${this.baseUrl}/persons/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(personData)
        });
        return response.json();
    }
    
    async detectFaces(imageFile) {
        const formData = new FormData();
        formData.append('file', imageFile);
        
        const response = await fetch(`${this.baseUrl}/detection/detect`, {
            method: 'POST',
            body: formData
        });
        return response.json();
    }
}

// Usage
const api = new FaceRecognitionAPI();
const person = await api.createPerson({name: 'John Doe', employee_id: 'EMP001'});
const result = await api.detectFaces(imageFile);
```

---

**🎖️ Complete API Reference for Face Recognition Service**  
*Version: 1.0.0 | Last Updated: 2024*
