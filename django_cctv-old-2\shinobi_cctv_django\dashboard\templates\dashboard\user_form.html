{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}{{ title|default:'User Form' }} - ABC CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title|default:'User Form' }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'dashboard:users' %}" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">User Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name {% if form.first_name.field.required %}*{% endif %}</label>
                            {{ form.first_name }} {# Assume widget attrs for class="form-control" etc. #}
                            {% if form.first_name.errors %}<div class="text-danger mt-1">{% for error in form.first_name.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                            {% if form.first_name.help_text %}<div class="form-text">{{ form.first_name.help_text }}</div>{% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name {% if form.last_name.field.required %}*{% endif %}</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}<div class="text-danger mt-1">{% for error in form.last_name.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                            {% if form.last_name.help_text %}<div class="form-text">{{ form.last_name.help_text }}</div>{% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">Username {% if form.username.field.required %}*{% endif %}</label>
                        {{ form.username }}
                        {% if form.username.errors %}<div class="text-danger mt-1">{% for error in form.username.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                        <div class="form-text">{{ form.username.help_text|default:"Username must be unique." }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">Email Address {% if form.email.field.required %}*{% endif %}</label>
                        {{ form.email }}
                        {% if form.email.errors %}<div class="text-danger mt-1">{% for error in form.email.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                        {% if form.email.help_text %}<div class="form-text">{{ form.email.help_text }}</div>{% endif %}
                    </div>
                    
                    {% if form.password %} {# Password fields might not be in CustomUserChangeForm if not changing #}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.password.id_for_label }}" class="form-label">Password {% if form.password.field.required and not instance %}*{% endif %}</label>
                            {{ form.password }}
                            {% if form.password.errors %}<div class="text-danger mt-1">{% for error in form.password.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                            {% if form.password.help_text %}<div class="form-text">{{ form.password.help_text }}</div>{% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if form.password2 %}
                            <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password {% if form.password2.field.required and not instance %}*{% endif %}</label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}<div class="text-danger mt-1">{% for error in form.password2.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                            {% if form.password2.help_text %}<div class="form-text">{{ form.password2.help_text }}</div>{% endif %}
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.role.id_for_label }}" class="form-label">Role {% if form.role.field.required %}*{% endif %}</label>
                        {{ form.role }} {# Assumes this is a ModelChoiceField rendering as select #}
                        {% if form.role.errors %}<div class="text-danger mt-1">{% for error in form.role.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                        <div class="form-text">
                            <strong>Administrator:</strong> Full system access<br>
                            <strong>Warehouse Manager:</strong> Access to assigned warehouses and cameras<br>
                            <strong>Security Operator:</strong> View cameras and control recordings<br>
                            <strong>Viewer:</strong> View-only access to assigned locations
                        </div>
                    </div>
                    
                    <div class="mb-3" id="accessible-locations-div">
                        <label for="{{ form.accessible_locations.id_for_label }}" class="form-label">Accessible Locations</label>
                        {{ form.accessible_locations }} {# Assumes ModelMultipleChoiceField, rendered as multiple select #}
                        {% if form.accessible_locations.errors %}<div class="text-danger mt-1">{% for error in form.accessible_locations.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                        <div class="form-text">
                            Hold Ctrl/Cmd to select multiple locations. This is not applicable if 'Administrator' role is selected (they get all locations).
                            {{ form.accessible_locations.help_text }}
                        </div>
                    </div>
                    
                    {% if form.is_active %}
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                Active User
                            </label>
                             {% if form.is_active.errors %}<div class="text-danger mt-1">{% for error in form.is_active.errors %}<small>{{ error }}</small>{% endfor %}</div>{% endif %}
                            <div class="form-text">Inactive users cannot log in to the system. {{ form.is_active.help_text }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'dashboard:users' %}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">{% if instance %}Update User{% else %}Add User{% endif %}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('{{ form.role.id_for_label }}'); // Use Django form field ID
    const locationsDiv = document.getElementById('accessible-locations-div');
    
    function updateLocationVisibility() {
        if (!roleSelect || !locationsDiv) return; // Ensure elements exist

        const selectedRoleText = roleSelect.options[roleSelect.selectedIndex].text;
        
        // The logic in views.py for CustomUserCreationForm might handle accessible_locations
        // based on role automatically. This JS is for UI sugar.
        if (selectedRoleText === 'Administrator') {
            locationsDiv.style.display = 'none';
        } else {
            locationsDiv.style.display = 'block';
        }
    }
    
    if (roleSelect) {
        updateLocationVisibility(); // Initial check
        roleSelect.addEventListener('change', updateLocationVisibility); // Listen for changes
    }
});
</script>
{% endblock %}