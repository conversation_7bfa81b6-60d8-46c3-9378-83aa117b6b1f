{% load bootstrap4 i18n static %}
{% get_current_language as language_code %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ECC</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="icon" type="image/webp" href="{% static 'images/favicon.webp' %}">   
    <!-- CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
  </head>
  <body>
    <header class="hero">
      <nav class="navbar navbar-expand-lg">
        <div class="container">
          <a class="navbar-brand d-flex align-items-center me-auto" href="#">
            <!-- Logo -->
            <img src="{% static 'images/favicon.webp' %}" alt="DeepSeek Logo">
            <h1 class="sitename">Ethiopian Customs Commission</h1>
          </a>
          <button
            class="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarNav"
          >
            <span class="navbar-toggler-icon"></span>
          </button>
          <div
            class="collapse navbar-collapse justify-content-end"
            id="navbarNav"
          >
            <ul class="navbar-nav">
            {% if request.user.is_authenticated %}
                {% if request.user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:login' %}">{% translate 'Django administration' %}</a>
                    </li>
                {% endif %}
                <li class="nav-item">
                  <a class="nav-link text-white" href="index">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:change_password' %}">{% translate 'Change password' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:change_profile' %}">{% translate 'Change profile' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:change_email' %}">{% translate 'Change email' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:log_out_confirm' %}">{% translate 'Log out' %}</a>
                </li>
                {% else %}
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:log_in' %}">{% translate 'Log in' %}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'cameras:sign_up' %}">{% translate 'Create an account' %}</a>
                </li>
                {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'change_language' %}">{% translate 'Change language' %}</a>
                    </li>
                </ul>            
          </div>
        </div>
      </nav>
    </header>
      {% bootstrap_messages %}

      {% block content %}
          No content.
      {% endblock content %}
  </body>
</html>