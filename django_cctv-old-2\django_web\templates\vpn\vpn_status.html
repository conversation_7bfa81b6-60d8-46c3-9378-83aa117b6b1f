{% extends 'base.html' %}

{% block content %}
  <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">VPN Server Status</h1>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Status:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ status.server_status }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Uptime:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ status.uptime }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Connected Clients:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ status.connected_clients }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Total Traffic In:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ status.total_traffic_in }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Total Traffic Out:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ status.total_traffic_out }}</span>
    </div>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Version:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ status.version }}</span>
    </div>
    <h2 class="text-xl font-semibold mt-8 mb-2 text-gray-800 dark:text-gray-100">Connected Clients</h2>
    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
      {% for client in connected_clients %}
        <li class="py-2 flex items-center justify-between">
          <span class="text-gray-900 dark:text-gray-100">{{ client.name }} ({{ client.client_id }})</span>
          <span class="text-gray-700 dark:text-gray-300">{{ client.virtual_ip }}</span>
        </li>
      {% empty %}
        <li class="py-2 text-gray-500 dark:text-gray-400">No clients currently connected.</li>
      {% endfor %}
    </ul>
    <div class="mt-8">
      <a href="{% url 'dashboard:index' %}" class="inline-block px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Back to Dashboard</a>
    </div>
  </div>
{% endblock %}
