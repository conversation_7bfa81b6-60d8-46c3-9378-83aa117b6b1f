// dashboard/static/dashboard/js/camera-controls.js

document.addEventListener('DOMContentLoaded', function() {
    // These functions are specific to where this script is loaded (e.g., camera_detail or pages with camera-grid)
    if (document.querySelector('.camera-video')) { // Check if video players are on the page
        initVideoPlayers();
    }
    if (document.querySelector('.recording-control') || document.querySelector('.recording-control-grid')) {
        setupRecordingControls();
    }
    if (document.querySelector('.ptz-btn')) {
        setupPTZControls();
    }
    if (document.querySelector('#take-snapshot')) {
        setupSnapshotButton();
    }
    if (document.querySelector('#refresh-camera')) {
        setupRefreshCameraButton();
    }
    if (document.querySelectorAll('.copy-btn').length > 0) {
        setupCopyButtons();
    }
});

function initVideoPlayers() {
    const videoElements = document.querySelectorAll('.camera-video'); // Used in camera-grid
    videoElements.forEach(video => {
        const cameraId = video.dataset.cameraId;
        const rtspUrl = video.dataset.rtspUrl; // This will likely not work directly in most browsers.
                                            // Shinobi's embed iframe is a more robust solution for web.
                                            // This Video.js setup for RTSP is highly dependent on environment.
        if (rtspUrl && videojs) {
            try {
                const player = videojs(video.id, {
                    controls: true,
                    autoplay: false,
                    fluid: true, // Fills the width of the parent, respects aspect ratio
                    preload: 'auto',
                    // liveui: true, // For HLS/DASH, might not apply to RTSP (browser support varies)
                    sources: [{ src: rtspUrl, type: 'application/x-rtsp' }] // Type for RTSP
                });

                player.on('error', function() {
                    console.error(`Video.js error for camera ${cameraId}: RTSP stream likely unsupported or unavailable.`);
                    const errorOverlay = document.createElement('div');
                    errorOverlay.className = 'video-error-overlay'; // Make sure this class is styled
                    errorOverlay.innerHTML = `
                        <div class="error-message">
                            <i class="bi bi-exclamation-triangle-fill text-warning fs-1 mb-2"></i>
                            <h5>Stream Error</h5>
                            <p class="small">Cannot play RTSP stream for camera ${cameraId}. This format may not be supported by your browser or the stream is offline.</p>
                            <button class="btn btn-sm btn-primary retry-stream-btn mt-2">Retry</button>
                        </div>`;
                    
                    const videoContainer = player.el(); // .closest('.camera-feed');
                    if (videoContainer) videoContainer.appendChild(errorOverlay);

                    errorOverlay.querySelector('.retry-stream-btn').addEventListener('click', function() {
                        errorOverlay.remove();
                        player.reset(); // Resets the player
                        player.src({ src: rtspUrl, type: 'application/x-rtsp' });
                        player.load(); // Attempt to load again
                        // player.play(); // Autoplay on retry can be aggressive
                    });
                });
            } catch (e) {
                console.error("Error initializing Video.js for " + video.id, e);
                 const videoContainer = document.getElementById(video.id)?.parentNode;
                 if (videoContainer) {
                    videoContainer.innerHTML = `<div class="p-3 text-center text-white small bg-dark h-100 d-flex align-items-center justify-content-center">Video player for ${cameraId} could not be initialized. RTSP streams might not be playable directly. Consider using Shinobi's iframe embed.</div>`;
                 }
            }
        } else if (!rtspUrl && video.classList.contains('camera-video')) {
             const videoContainer = document.getElementById(video.id)?.parentNode;
             if (videoContainer) {
                videoContainer.innerHTML = `<div class="p-3 text-center text-white small bg-dark h-100 d-flex align-items-center justify-content-center">No stream URL for ${cameraId}.</div>`;
             }
        }
    });
}

function setupRecordingControls() {
    // Handles both .recording-control (detail page) and .recording-control-grid (grid partial)
    const recordingButtons = document.querySelectorAll('.recording-control, .recording-control-grid');
    const csrfToken = getCSRFToken();

    recordingButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cameraId = this.dataset.cameraId;
            const action = this.dataset.action;
            const apiUrl = this.dataset.apiUrl || `/api/camera/${cameraId}/recording/`; // Get from data-api-url or construct

            const originalHtml = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
            this.disabled = true;

            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({ action: action })
            })
            .then(response => response.json().then(data => ({ ok: response.ok, status: response.status, data })))
            .then(({ ok, status, data }) => {
                if (!ok) {
                    throw new Error(data.error || `Failed with status ${status}`);
                }
                
                // Update UI for recording indicator (both on detail and grid)
                const indicators = document.querySelectorAll(`[data-recording-indicator="${cameraId}"], [data-recording-indicator-grid="${cameraId}"]`);
                indicators.forEach(indicator => {
                    if (data.is_recording) {
                        indicator.classList.remove('bg-secondary', 'text-bg-secondary');
                        indicator.classList.add('bg-danger', 'text-bg-danger');
                        indicator.innerHTML = `<i class="bi bi-record-fill"></i> ${indicator.matches('[data-recording-indicator-grid]') ? 'REC' : 'Recording'}`;
                    } else {
                        indicator.classList.remove('bg-danger', 'text-bg-danger');
                        indicator.classList.add('bg-secondary', 'text-bg-secondary');
                        indicator.innerHTML = `<i class="bi bi-record"></i> ${indicator.matches('[data-recording-indicator-grid]') ? 'Ready' : 'Not Recording'}`;
                    }
                });

                // Update button states (both detail and grid)
                document.querySelectorAll(`.recording-control[data-camera-id="${cameraId}"], .recording-control-grid[data-camera-id="${cameraId}"]`).forEach(btn => {
                    if (btn.dataset.action === "start") {
                        btn.classList.toggle('d-none', data.is_recording);
                    } else if (btn.dataset.action === "stop") {
                        btn.classList.toggle('d-none', !data.is_recording);
                    }
                });
                showNotification(data.message || `Recording ${action}ed.`, 'success');
            })
            .catch(error => {
                console.error('Error toggling recording:', error);
                showNotification(error.message || 'Failed to control recording.', 'danger');
            })
            .finally(() => {
                this.innerHTML = originalHtml;
                this.disabled = false;
            });
        });
    });
}

function setupPTZControls() {
    const ptzButtons = document.querySelectorAll('.ptz-btn');
    const csrfToken = getCSRFToken();

    ptzButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cameraId = this.dataset.cameraId;
            const direction = this.dataset.direction;
            const apiUrl = this.dataset.apiUrl || `/api/camera/${cameraId}/ptz/`;
            const ptzStatusEl = document.querySelector(`#ptz-status-${cameraId}`) || document.querySelector(`[data-ptz-status="${cameraId}"]`);


            if (ptzStatusEl) ptzStatusEl.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Moving ${direction}...`;
            
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({ direction: direction, speed: 50 }) // speed is optional
            })
            .then(response => response.json().then(data => ({ ok: response.ok, status: response.status, data })))
            .then(({ok, status, data}) => {
                if (!ok) throw new Error(data.error || `Failed with status ${status}`);
                if (ptzStatusEl) ptzStatusEl.textContent = data.message || `Moved ${direction}`;
                showNotification(data.message || `Camera moved ${direction}`, 'success');
            })
            .catch(error => {
                console.error('PTZ Error:', error);
                if (ptzStatusEl) ptzStatusEl.textContent = 'PTZ Error';
                showNotification(error.message || 'PTZ command failed.', 'danger');
            })
            .finally(() => {
                setTimeout(() => {
                    if (ptzStatusEl) ptzStatusEl.textContent = 'Ready';
                }, 2000);
            });
        });
    });
}

function setupSnapshotButton() {
    const snapshotButton = document.getElementById('take-snapshot');
    if (!snapshotButton) return;

    snapshotButton.addEventListener('click', function() {
        const cameraId = document.querySelector('[data-camera-id]')?.dataset.cameraId || this.dataset.cameraId; // Fallback if main camera ID isn't found
        const apiUrl = this.dataset.apiUrl || `/api/camera/${cameraId}/snapshot/`;
        if (!cameraId) {
            showNotification('Camera ID not found for snapshot.', 'warning');
            return;
        }

        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Snapping...';

        fetch(apiUrl)
            .then(response => response.json().then(data => ({ ok: response.ok, status: response.status, data })))
            .then(({ok, status, data}) => {
                if (!ok) throw new Error(data.error || `Failed with status ${status}`);
                showNotification(data.message + (data.snapshot_url ? ` <a href="${data.snapshot_url}" target="_blank">View</a>` : ''), 'success');
            })
            .catch(error => {
                console.error("Error taking snapshot:", error);
                showNotification(error.message || 'Failed to take snapshot.', 'danger');
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-camera"></i> Snapshot';
            });
    });
}

function setupRefreshCameraButton() {
    const refreshButton = document.getElementById('refresh-camera');
    if (!refreshButton) return;

    refreshButton.addEventListener('click', function() {
        const cameraId = document.querySelector('[data-camera-id]')?.dataset.cameraId || this.dataset.cameraId;
        const apiUrl = this.dataset.apiUrl || `/api/camera/${cameraId}/status/`;
         if (!cameraId) {
            showNotification('Camera ID not found for refresh.', 'warning');
            return;
        }

        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';

        fetch(apiUrl)
            .then(response => response.json().then(data => ({ ok: response.ok, status: response.status, data })))
            .then(({ok, status, data}) => {
                if (!ok) throw new Error(data.error || `Failed with status ${status}`);
                
                const statusIndicator = document.querySelector(`#camera-status-indicator-${cameraId}`) || document.querySelector(`[data-status-indicator="${cameraId}"]`);
                if (statusIndicator) {
                    statusIndicator.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
                    statusIndicator.className = `camera-status status-${data.status.toLowerCase()}`;
                }

                const recIndicator = document.querySelector(`#camera-recording-indicator-${cameraId}`) || document.querySelector(`[data-recording-indicator="${cameraId}"]`);
                 if (recIndicator) {
                    recIndicator.innerHTML = `<i class="bi ${data.is_recording ? 'bi-record-fill' : 'bi-record'}"></i> ${data.is_recording ? 'Recording' : 'Not Recording'}`;
                    recIndicator.className = `badge rounded-pill ${data.is_recording ? 'bg-danger' : 'bg-secondary'}`;
                }


                const lastUpdatedEl = document.querySelector(`#camera-last-updated-${cameraId}`) || document.querySelector(`[data-last-updated="${cameraId}"]`);
                if (lastUpdatedEl) lastUpdatedEl.textContent = data.last_updated;

                // Toggle recording buttons if they exist on the page for this camera
                const startRecBtn = document.querySelector(`.recording-control[data-camera-id="${cameraId}"][data-action="start"]`);
                const stopRecBtn = document.querySelector(`.recording-control[data-camera-id="${cameraId}"][data-action="stop"]`);
                if (startRecBtn) startRecBtn.classList.toggle('d-none', data.is_recording);
                if (stopRecBtn) stopRecBtn.classList.toggle('d-none', !data.is_recording);

                showNotification('Camera status refreshed!', 'success');
            })
            .catch(error => {
                console.error("Error refreshing camera status:", error);
                showNotification(error.message || 'Failed to refresh camera status.', 'danger');
            })
            .finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            });
    });
}

function setupCopyButtons() {
    document.querySelectorAll('.copy-btn').forEach(button => {
        button.addEventListener('click', function() {
            const textToCopy = this.dataset.clipboardText;
            if (navigator.clipboard && textToCopy) {
                navigator.clipboard.writeText(textToCopy)
                    .then(() => {
                        const originalIcon = this.innerHTML;
                        this.innerHTML = '<i class="bi bi-check-lg text-success"></i>';
                        showNotification('Copied to clipboard!', 'info');
                        setTimeout(() => { this.innerHTML = originalIcon; }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy text: ', err);
                        showNotification('Failed to copy.', 'warning');
                    });
            } else {
                showNotification('Clipboard API not available or no text to copy.', 'warning');
            }
        });
    });
}