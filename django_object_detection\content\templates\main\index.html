{% extends 'layouts/default/page.html' %}
{% load i18n %}
{% load static %}
{% block content %}
<style>
    .camera-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }
    .video-wrapper {
        margin-top: 10px;
        position: relative;
        background-color: #000;
        width: 100%;
        height: 100%;
        aspect-ratio: 16/9;
        z-index: 1; /* Explicit lower index than controls */
    }
    .video-feed {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: opacity 0.3s ease;
    }

    .video-updating {
        opacity: 0.5;
    }
    .camera-name {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: red;
        margin-right: 5px;
        display: inline-block;
        transition: background-color 0.3s ease;
    }
    .status-indicator.active {
        background: #0f0;
    }

    .counter-toggle {
        display: flex;
        align-items: center;
        gap: 10px;
        color: white;
    }
    /* Camera Controls Wrapper */
    .camera-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-top: 10px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 5px;
        position: relative;
        z-index: 2; /* Ensure controls stay above video elements */
        gap: 1rem;
        padding: 0.5rem 1rem;
    }

    .camera-info {
        display: flex;
        align-items: center;
        color: white;
        font-size: 10px;
    }

    /* Toggle Switch Styling */
    .switch {
        position: relative;
        display: inline-block;
        width: 34px;
        height: 18px;
    }

    .switch input { 
        opacity: 0;
        width: 0;
        height: 0;
    }
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }
    .slider:before {
        position: absolute;
        content: "";
        height: 14px;
        width: 14px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
    }
    input:checked + .slider {
        background-color: #0f0;
    }
    input:checked + .slider:before {
        transform: translateX(16px);
    }

    .camera-card {
        display: flex;
        flex-direction: column; /* Stack elements vertically */
        align-items: flex-start; /* Align content to the left */
        padding: 10px;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        margin-bottom: 15px;
    }

    .camera-settings {
        display: flex;
        flex-wrap: wrap; /* Allows wrapping if needed */
        gap: 15px; /* Adds spacing between dropdowns */
        width: 100%;
        justify-content: space-between; /* Evenly spaces dropdowns */
        align-items: center;
    }

    .label-selector,
    .direction-selector {
        flex: 1; /* Make all dropdowns take equal space */
        min-width: 200px; /* Ensure dropdowns don’t shrink too much */
    }

    .label-dropdown, 
    .direction-dropdown {
        width: 100%; /* Ensure dropdowns fill their containers */
        background-color: rgba(0, 0, 0, 0.3); /* Transparent background */
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.5); /* Light border */
        padding: 5px;
        border-radius: 5px;
    }

    .label-dropdown option {
        background-color: rgba(50, 50, 50, 0.3); /* Semi-transparent background */
        color: white; /* Keep text visible */
    }

    .label-dropdown option:checked {
        background-color: rgba(0, 0, 0, 0.3) !important; /* Transparent orange */
        color: white;
    }
    @keyframes fadeInBounce {
        0% {
            opacity: 0;
            transform: translateY(-20px);
        }
        50% {
            opacity: 0.5;
            transform: translateY(10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }
    /* Moving & Fading Text */
    .animated-text {
        position: fixed; /* Fixed so it moves separately */
        top: 270px; /* Adjust position */
        left: 50%;
        transform: translateX(-50%);
        font-size: 3rem;
        font-weight: bold;
        color: #000000;
        transition: transform 0.1s ease-out;
        animation: fadeEffect 3s infinite alternate; /* Fading effect */
    }
    /* Add button spacing */
    .counter-toggle button {
        margin-right: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative; /* Ensure buttons are positioned normally */
    }
/* Hover effects */
.counter-toggle button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

/* Active state */
.counter-toggle button:active {
    transform: translateY(1px);
    }

/* Ensure clickable area */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    }

    /* Different colors for vertical lines */
    .line-canvas {
        border: 1px solid rgba(255,255,255,0.2); /* Make canvas visible */
        pointer-events: none; /* Default state */
    }
/* Improved Control Styling */
.btn-compact {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    line-height: 1.2;
}

.button-group {
    display: flex;
    gap: 0.5rem;
}

.toggle-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.toggle-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-label {
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
}

.switch .slider {
    height: 16px;
}

.switch .slider:before {
    width: 12px;
    height: 12px;
    left: 2px;
    bottom: 2px;
}

.mode-selector .btn {
    min-width: 30px;
    margin-right: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative; /* Ensure buttons are positioned normally */
}
    /* Keyframes for Fade In and Out */
    @keyframes fadeEffect {
        0% { opacity: 0.2; }  /* Start with low visibility */
        50% { opacity: 1; }  /* Fully visible */
        100% { opacity: 0.2; } /* Fade out again */
    }
</style>
{% if request.user.is_authenticated %}
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        {% endfor %}
    </div>
    {% endif %}  
<div class="bg-dark">
    <div class="container-fluid">
        <!-- Branch Selection -->
        <div class="form-group">
            <label for="branchSelect" class="text-light">Select Warehouse:</label>
            <select id="branchSelect" class="form-control">
                {% if request.user.is_superuser%}
                    <option value="">All Warehouses</option>
                {% endif %}
                {% for branch in branches %}
                    <option value="{{ branch.id }}" 
                            {% if branch.id|stringformat:"s" == selected_branch_id %}selected{% endif %}>
                        {{ branch.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <!-- Camera Grid -->
        <div class="camera-grid">
            {% for camera in cameras %}
            <div class="camera-card" data-camera-id="{{ camera.id }}">
                {% if selected_branch_id %}
                <div class="camera-settings">
                    <!-- Label Selector -->
                    <div class="label-selector">
                        <label for="labelSelect_{{ camera.id }}" class="text-light">Select Labels:</label>
                        <select id="labelSelect_{{ camera.id }}" class="form-control label-dropdown" multiple size="3">
                            {% for class_id, label in yolo_labels.items %}
                                <option value="{{ label }}" selected>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                {% endif %}
        
                <div class="video-wrapper">
                    <img class="video-feed" 
                        src="{% url 'video_feed' camera.branch.id camera.id %}" 
                        alt="{{ camera.name }}"
                        loading="lazy"
                        data-offline-src="{% static 'offline.png' %}">
                        <canvas class="line-canvas" style="position: absolute; top: 0; left: 0; pointer-events: none;"></canvas>  
                        <video id="cameraFeed_{{ camera.id }}" autoplay></video>           
                    </div>        
                <!-- Camera Controls (Inside Each Camera Card) -->
                <div class="camera-controls">
                    <!-- Left Side: Status + Camera Name -->
                    <div class="camera-info">
                        <span class="status-indicator {% if camera.enable_face_detection %}active{% endif %}"></span>
                        <span>{{ camera.name }} ({{ camera.device_index }})</span>
                    </div>       
                    <!-- Counter Toggle -->
                    {% if selected_branch_id %}
                    <div class="mode-selector">
                        <div class="btn-group btn-group-sm" data-toggle="buttons">
                            <label class="btn btn-secondary {% if camera.settings.mode == 'h' %}active{% endif %}">
                                <input type="radio" name="mode" class="mode-radio" value="h" 
                                       {% if camera.settings.mode == 'h' %}checked{% endif %}> HM
                            </label>
                            <label class="btn btn-secondary {% if camera.settings.mode == 'v' %}active{% endif %}">
                                <input type="radio" name="mode" class="mode-radio" value="v" 
                                       {% if camera.settings.mode == 'v' %}checked{% endif %}> VM
                            </label>
                            <label class="btn btn-secondary {% if camera.settings.mode == 'both' %}active{% endif %}">
                                <input type="radio" name="mode" class="mode-radio" value="both" 
                                       {% if camera.settings.mode == 'both' %}checked{% endif %}> BM
                            </label>
                        </div>
                    </div>
                    <div class="counter-toggle">
                        <!-- Changed from single edit button to three buttons -->
                        <button class="btn btn-sm btn-secondary clear-lines-btn">Clear</button>
                        <button class="btn btn-sm btn-secondary edit-vlines-btn">EditVL</button>
                        <button class="btn btn-sm btn-secondary edit-hlines-btn">EditHL</button>

                        <label style="color: orange;">Count</label>
                        <label class="switch">
                            <input type="checkbox" class="toggle-counter" data-camera-id="{{ camera.id }}"
                            {% if camera.settings.counter %}checked{% endif %}>
                            <span class="slider round"></span>
                        </label>
                    
                        <label style="color: orange;">Model</label>
                        <label class="switch">
                            <input type="checkbox" class="toggle-detection"
                                data-camera-id="{{ camera.id }}"
                                {% if camera.settings.face_detection %}checked{% endif %}>
                            <span class="slider round"></span>
                        </label>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>        
    </div>
</div>
{% else %}
<div class="jumbotron jumbotron-fluid text-center">
    <h1 class="animated-text">{% translate 'Ethiopian Customs Commission' %}</h1>
    <div class="container-fluid">
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <div>
            <h2>Warehouse Camera Monitoring System</h2>
            <p>Please Log in to continue!</p>
            <a href="{% url 'cameras:log_in' %}" class="btn btn-primary btn-lg" role="button">Login -></a>
        </div>
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
    </div>
</div>
{% endif %}
<script>
    document.getElementById("branchSelect").addEventListener("change", function () {
        let branchId = this.value;
        let currentUrl = new URL(window.location.href);
        
        if (currentUrl.searchParams.get("branch") !== branchId) {
            let newUrl = branchId ? `?branch=${branchId}` : window.location.pathname;
            window.location.href = newUrl;
        }
    });
    
    // Face Detection Toggle
    document.querySelectorAll('.toggle-detection').forEach(toggle => {
    toggle.addEventListener('change', async function () {
        const cameraId = this.dataset.cameraId;
        const enabled = this.checked;
        const cameraCard = this.closest('.camera-card');
        const videoFeed = cameraCard.querySelector('.video-feed');
        const labelDropdown = cameraCard.querySelector('.label-dropdown');
        const counterToggle = cameraCard.querySelector('.toggle-counter').checked;

        let selectedLabels = labelDropdown ? Array.from(labelDropdown.selectedOptions).map(opt => opt.value) : [];

        try {
            const response = await fetch(`/toggle-detection/${cameraId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    enable: enabled,
                    labels: selectedLabels,
                    counter: counterToggle
                })
            });

            if (response.ok) {
                const data = await response.json();
                videoFeed.src = data.new_url + `?t=${Date.now()}`;

                const indicator = cameraCard.querySelector('.status-indicator');
                indicator.classList.toggle('active', enabled);
            } else {
                throw new Error('Failed to update detection status');
            }
        } catch (error) {
            this.checked = !enabled;
            alert(error.message || 'Network error - please try again');
        }
    });
});

// Handle mode selection
document.querySelectorAll('.mode-radio').forEach(radio => {
    radio.addEventListener('change', async function() {
        if (this.checked) {
            const cameraId = this.closest('.camera-card').dataset.cameraId;
            const mode = this.value;
            
            try {
                const response = await fetch(`/update-mode/${cameraId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ mode: mode })
                });

                if (response.ok) {
                    // Refresh the video feed
                    const videoFeed = this.closest('.camera-card').querySelector('.video-feed');
                    videoFeed.src = videoFeed.src.split('?')[0] + `?t=${Date.now()}`;
                } else {
                    throw new Error('Failed to update mode');
                }
            } catch (error) {
                console.error('Error updating mode:', error);
            }
        }
    });
});
    
document.querySelectorAll('.edit-hlines-btn, .edit-vlines-btn, .clear-lines-btn').forEach(btn => {
    console.log('Attaching listener to:', btn); // Debug line
    btn.addEventListener('click', async function() {
        console.log('Button clicked:', this); // Debug line
        const cameraId = this.closest('.camera-card').dataset.cameraId;
        const videoWrapper = this.closest('.camera-card').querySelector('.video-wrapper');
        const canvas = videoWrapper.querySelector('.line-canvas');
        const video = videoWrapper.querySelector('.video-feed');

        // Handle clear screen
        if(this.classList.contains('clear-lines-btn')) {
            canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);
            video.src = video.src + '?t=' + Date.now();
            return;
        }

        // Set canvas interaction
        canvas.style.pointerEvents = 'auto';
        const rect = video.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;

        // Fetch current positions
        const response = await fetch(`/api/cameras/${cameraId}/lines/`);
        const {horizontal, vertical} = await response.json();
        const line_positions = {
            ...horizontal,  // Contains outer_cy1/outer_cy2
            ...vertical     // Contains outer_cx1/outer_cx2
        };

        // Initialize lines based on button type
        const isHorizontal = this.classList.contains('edit-hlines-btn');
        const ctx = canvas.getContext('2d');
        
        let lines = [];
        if(isHorizontal) {
            lines = [
                {id: 'outer_cy2', y: (line_positions.outer_cy2 || 0.7) * canvas.height, color: 'blue', orientation: 'h'},
                {id: 'outer_cy1', y: (line_positions.outer_cy1 || 0.6) * canvas.height, color: 'red', orientation: 'h'}
            ];
        } else {
            lines = [
                {id: 'outer_cx1', x: (line_positions.outer_cx1 || 0.3) * canvas.width, color: 'green', orientation: 'v'},
                {id: 'outer_cx2', x: (line_positions.outer_cx2 || 0.7) * canvas.width, color: 'yellow', orientation: 'v'}
            ];
        }

        function drawLines() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            lines.forEach(line => {
                ctx.beginPath();
                if(line.orientation === 'h') {
                    ctx.moveTo(0, line.y);
                    ctx.lineTo(canvas.width, line.y);
                } else {
                    ctx.moveTo(line.x, 0);
                    ctx.lineTo(line.x, canvas.height);
                }
                ctx.strokeStyle = line.color;
                ctx.lineWidth = 2;
                ctx.stroke();
            });
        }
        drawLines();

        // Drag handling
        let dragging = null;
        canvas.addEventListener('mouseup', async (e) => {
            e.stopPropagation(); 
            if (dragging) {
                const positions = {};
                lines.forEach(line => {
                    if(line.orientation === 'h') {
                        positions[line.id] = line.y / canvas.height;
                    } else {
                        positions[line.id] = line.x / canvas.width;
                    }
                });
                
                await fetch(`/api/cameras/${cameraId}/lines/update/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({
                        line_positions: positions,
                        type: isHorizontal ? 'horizontal' : 'vertical'  // Add type parameter
                    })
                });
                
                canvas.style.pointerEvents = 'none';
                video.src = video.src + '?t=' + Date.now();
            }
            dragging = null;
        });

        canvas.addEventListener('mousemove', e => {
            e.stopPropagation();
            if (dragging) {
                const rect = canvas.getBoundingClientRect();
                if(dragging.orientation === 'h') {
                    dragging.y = e.clientY - rect.top;
                } else {
                    dragging.x = e.clientX - rect.left;
                }
                drawLines();
            }
        });

        canvas.addEventListener('mousedown', e => {
            e.stopPropagation();
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            dragging = lines.find(line => {
                if(line.orientation === 'h') {
                    return Math.abs(line.y - y) < 5;
                }
                return Math.abs(line.x - x) < 5;
            });
        });
    });
});

document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.video-feed').forEach(img => {
        img.onload = () => {
            let indicator = img.closest('.camera-card').querySelector('.status-indicator');
            if (indicator) indicator.style.backgroundColor = '#0f0'; // Green for active feed
        };
        
        img.onerror = function () {
            let indicator = this.closest('.camera-card').querySelector('.status-indicator');
            if (indicator) indicator.style.backgroundColor = '#f00'; // Red for offline feed

            // Replace the broken image with offline.png
            this.onerror = null;
            this.src = this.dataset.offlineSrc;
        };
    });
});
        
    document.addEventListener('DOMContentLoaded', function() {
    // Auto-remove lockout messages after timeout
    const lockoutMessages = document.querySelectorAll('.alert-danger');
    lockoutMessages.forEach(msg => {
        setTimeout(() => {
            msg.style.display = 'none';
            // Clear server-side lockout after client-side fade
            fetch('/clear-lockout/', { method: 'POST' });
        }, 5000);  // 5 seconds
    });
});
    // Ensure dropdown remains visible in fullscreen mode
    document.querySelectorAll('.video-wrapper').forEach(wrapper => {
    wrapper.addEventListener('dblclick', (event) => {
        event.stopPropagation(); // Prevents unintended bubbling
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            if (wrapper.requestFullscreen) {
                wrapper.requestFullscreen();
            } else if (wrapper.webkitRequestFullscreen) {
                wrapper.webkitRequestFullscreen();
            } else if (wrapper.msRequestFullscreen) {
                wrapper.msRequestFullscreen();
            }
        }
    });
});

// Listen for fullscreen change events to handle Esc key exits gracefully
document.addEventListener('fullscreenchange', () => {
    if (!document.fullscreenElement) {
        console.log("Exited fullscreen mode for video-wrapper");
    }
});

document.addEventListener('webkitfullscreenchange', () => {
    if (!document.webkitFullscreenElement) {
        console.log("Exited fullscreen mode for video-wrapper (WebKit)");
    }
});

document.addEventListener('msfullscreenchange', () => {
    if (!document.msFullscreenElement) {
        console.log("Exited fullscreen mode for video-wrapper (IE)");
    }
});
</script>
{% endblock content %}