from django.db import models
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator

class District(models.Model):
    name = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

class Branch(models.Model):
    name = models.CharField(max_length=255)
    district = models.ForeignKey(District, on_delete=models.CASCADE, related_name='branches')
    branch_access_id = models.CharField(max_length=50, null=True, blank=True)  # Single field
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.district.name}) ({self.branch_access_id})"
    
class Camera(models.Model):
    name = models.CharField(max_length=100)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name="cameras")
    device_index = models.IntegerField(
        unique=True,  
        validators=[MinValueValidator(0), MaxValueValidator(10)],
        help_text="Use 0 for first webcam, 1 for second, etc."
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    horizontal_line_positions = models.JSONField(default=dict, blank=True)
    vertical_line_positions = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.name} ({self.device_index}) - {self.branch.name}"
