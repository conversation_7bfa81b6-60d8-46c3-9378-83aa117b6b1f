# SecureView: CCTV Monitoring Web Application

SecureView is a comprehensive CCTV monitoring solution that allows you to view camera feeds from multiple locations through a secure OpenVPN connection. The system integrates with Shinobi NVR for video management and provides a user-friendly interface for monitoring and managing your surveillance system.

## Features

- **Secure Remote Access**: View your cameras from anywhere using OpenVPN
- **Live Camera Feeds**: Monitor multiple cameras simultaneously with customizable grid views
- **User Management**: Role-based access control with different permission levels
- **Camera Management**: Add, configure, and group cameras for better organization
- **Event Monitoring**: Track motion detection and other camera events
- **VPN Client Management**: Create and manage VPN clients for secure connections
- **Responsive Design**: Access the system from any device with a web browser

## Architecture

The system consists of several components that work together:

1. **Django Web Application**: The main user interface and control system
2. **Shinobi NVR**: Network Video Recorder for camera management and recording
3. **OpenVPN Server**: Secure connection for remote cameras and users
4. **MariaDB**: Database for Shinobi
5. **DuckDNS**: Dynamic DNS service for accessing the system remotely

## Setup Instructions

### Prerequisites

- Docker and Docker Compose installed on your system
- Basic understanding of networking and port forwarding

### Installation

1. Clone this repository to your server
2. Copy `.env.example` to `.env` and update the values
3. Start the system using Docker Compose:

```bash
docker-compose up -d
```

4. Create a superuser for the Django admin:

```bash
docker-compose exec web python manage.py createsuperuser
```

5. Access the web interface at `http://your-server-ip:8000`

## Camera Setup

1. Create a VPN client for each location with cameras
2. Download the client configuration file
3. Install and configure the OpenVPN client at the camera location
4. Add your cameras to the system through the web interface
5. Configure Shinobi monitors for each camera

## User Guide

### Dashboard

The dashboard provides an overview of your system, including:

- Total number of cameras and their status
- Recent events from all cameras
- Quick access to frequently used cameras
- System status information

### Live View

The Live View page allows you to:

- View multiple camera feeds simultaneously
- Customize the grid layout
- Take snapshots
- View cameras in fullscreen mode

### Camera Management

Manage your cameras through the web interface:

- Add new cameras
- Edit camera settings
- Group cameras for better organization
- Monitor camera status

### User Management

Control who has access to your system:

- Create user accounts with different permission levels
- Assign camera access permissions to users
- Track user activity

### VPN Management

Manage VPN clients for secure connections:

- Create new VPN clients
- Download configuration files
- Monitor connection status
- View connection logs

## Development

### Project Structure

The project is organized into several Django apps:

- `dashboard`: Main dashboard and system overview
- `cameras`: Camera management and live view
- `users`: User management and authentication
- `vpn`: VPN client management and status

### Technology Stack

- **Backend**: Python 3.12, Django 5.0
- **Frontend**: HTML, CSS (Tailwind CSS), JavaScript
- **Database**: SQLite (development), PostgreSQL (production option)
- **Containerization**: Docker, Docker Compose
- **Video Management**: Shinobi NVR
- **Secure Connections**: OpenVPN
- **Dynamic DNS**: DuckDNS

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Shinobi NVR](https://shinobi.video/)
- [OpenVPN](https://openvpn.net/)
- [Django](https://www.djangoproject.com/)
- [Tailwind CSS](https://tailwindcss.com/)