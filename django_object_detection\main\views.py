from django.views.generic import TemplateView
import os
from django.conf import settings
from django.shortcuts import render
from django.conf import settings
from django.contrib import messages
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from django.shortcuts import get_object_or_404, redirect
from django.views.generic.base import TemplateView
from django.http import StreamingHttpResponse
from django.views import View
from django.http import JsonResponse
import json 
import logging 
from django.urls import reverse
from django.views.decorators.http import require_http_methods
from asgiref.sync import sync_to_async
from .models import Camera, Branch
from .utils import generate_frames, generate_raw_frames, get_yolo_labels

# Set up logging
logger = logging.getLogger(__name__)


class IndexPageView(TemplateView):
    template_name = "main/index.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        image_folder = os.path.join(settings.STATICFILES_DIRS[0], "images")

        if os.path.exists(image_folder):
            context["images"] = [
                f"images/{img}"
                for img in os.listdir(image_folder)
                if img.lower().endswith((".jpg", ".jpeg", ".png", ".gif", ".webp"))
            ]
        else:
            context["images"] = []

        return context


class ChangeLanguageView(TemplateView):
    template_name = "main/change_language.html"

class VideoFeedView(View):
    async def get(self, request, branch_id, camera_id):
        try:
            camera = await sync_to_async(Camera.objects.get)(
                id=camera_id, branch_id=branch_id, is_active=True
            )

        except Camera.DoesNotExist:
            logger.error(f"Camera with ID {camera_id} in branch {branch_id} not found.")
            return JsonResponse({"error": "Camera not found"}, status=404)
        except Exception as e:
            logger.exception(f"Unexpected error while retrieving camera {camera_id}: {e}")
            return JsonResponse({"error": "Internal server error"}, status=500)

        session_key_detection = f'face_detection_{camera_id}'
        session_key_labels = f'selected_labels_{camera_id}'
        session_key_counter = f'counter_{camera_id}'

        face_detection_enabled = request.session.get(session_key_detection, False)
        selected_labels = request.session.get(session_key_labels, [])
        counter_enabled = request.session.get(session_key_counter, False)

        # Get line positions
        horizontal_pos = camera.horizontal_line_positions or {}
        vertical_pos = camera.vertical_line_positions or {}

        mode = request.session.get(f'mode_{camera_id}', 'h')

        if face_detection_enabled:
            return StreamingHttpResponse(
                generate_frames(camera.device_index, face_detection_enabled, selected_labels, counter_enabled, horizontal_pos, vertical_pos, mode),
                content_type='multipart/x-mixed-replace; boundary=frame'
            )
        else:
            return StreamingHttpResponse(
                generate_raw_frames(camera.device_index),
                content_type='multipart/x-mixed-replace; boundary=frame'
            )

@require_http_methods(["POST"])
async def toggle_detection(request, camera_id):
    try:
        data = json.loads(request.body)
        new_status = data.get('enable', False)
        selected_labels = data.get('labels', [])
        counter_enabled = data.get('counter', False)

        session_key_detection = f'face_detection_{camera_id}'
        session_key_labels = f'selected_labels_{camera_id}'
        session_key_counter = f'counter_{camera_id}'

        request.session[session_key_detection] = new_status
        request.session[session_key_labels] = selected_labels
        request.session[session_key_counter] = counter_enabled

        request.session.modified = True

        branch_id = request.session.get('current_branch', 1)

        return JsonResponse({
            'status': 'success',
            'enabled': new_status,
            'counter_enabled': counter_enabled,
            'new_url': reverse('video_feed', args=[branch_id, camera_id])
        })
    
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

def camera_dashboard(request):
    if request.session.get('lockout_until'):
        if timezone.now() < request.session['lockout_until']:
            messages.error(request, "Access locked. Please try again later.")
            return redirect('index')
        else:
            del request.session['lockout_until']
    
    selected_branch_id = request.GET.get("branch")
    user = request.user
    
    if not user.is_superuser:
        branches = Branch.objects.filter(is_active=True).order_by("name")
        
        if selected_branch_id:
            if not branches.filter(id=selected_branch_id).exists():
                messages.error(request, "You don't have permission to access this branch.")
                return redirect('index')
            
            verified_branches = request.session.get('verified_branches', [])
            if int(selected_branch_id) not in verified_branches:
                return redirect('branch_access', branch_id=selected_branch_id)
    else:
        branches = Branch.objects.filter(is_active=True).order_by("name")

    if selected_branch_id:
        cameras = Camera.objects.filter(branch_id=selected_branch_id, is_active=True).order_by("name")
    else:
        cameras = Camera.objects.filter(is_active=True).order_by("name")

    for camera in cameras:
        camera.settings = {
            'face_detection': request.session.get(f'face_detection_{camera.id}', False),
            'counter': request.session.get(f'counter_{camera.id}', False),
            'mode': request.session.get(f'mode_{camera.id}', 'h') 
        }

    yolo_labels = get_yolo_labels()

    return render(request, 'main/index.html', {
        'branches': branches,
        'cameras': cameras,
        'selected_branch_id': selected_branch_id,
        'yolo_labels': yolo_labels,
    })


def branch_access(request, branch_id):
    branch = get_object_or_404(Branch, id=branch_id)
    user = request.user
    
    # Prevent direct access without permission
    if not user.is_superuser and not Branch.objects.filter(id=branch_id).exists():
        messages.error(request, "You don't have permission to access this branch.")
        return redirect('index')
    
    # Check lockout status
    if request.session.get('lockout_until'):
        if timezone.now() < request.session['lockout_until']:
            messages.error(request, "Access locked. Please try again later.")
            return redirect('index')
            
    if request.method == 'POST':
        entered_id = request.POST.get('branch_access_id', '')
        attempt_key = f"branch_{branch_id}_attempts"
        
        if entered_id == branch.branch_access_id:
            # Reset attempts on success
            request.session.pop(attempt_key, None)
            verified = request.session.get('verified_branches', [])
            if branch.id not in verified:
                verified.append(branch.id)
                request.session['verified_branches'] = verified
            return redirect(f"{reverse('index')}?branch={branch_id}")
        else:
            # Increment failed attempts
            attempts = request.session.get(attempt_key, 0) + 1
            request.session[attempt_key] = attempts
            
            if attempts >= 3:
                # Set lockout for 15 minutes
                request.session['lockout_until'] = timezone.now() + timedelta(minutes=15)
                messages.error(request, "Too many failed attempts. Access locked for 15 minutes.")
                return redirect('index')
            
            messages.error(request, 
                f"Invalid access ID. {3 - attempts} {'attempts' if (3 - attempts) > 1 else 'attempt'} remaining.")
    
    return render(request, 'main/branch_access.html', {'branch': branch})


@require_http_methods(["GET"])
def get_line_positions(request, camera_id):
    try:
        camera = Camera.objects.get(id=camera_id)
        return JsonResponse({
            'horizontal': camera.horizontal_line_positions,
            'vertical': camera.vertical_line_positions
        })
    except Camera.DoesNotExist:
        return JsonResponse({'error': 'Camera not found'}, status=404)

@require_http_methods(["POST"])
def update_line_positions(request, camera_id):
    try:
        data = json.loads(request.body)
        camera = Camera.objects.get(id=camera_id)
        
        if data.get('type') == 'vertical':
            camera.vertical_line_positions = data.get('line_positions', {})
        else:
            camera.horizontal_line_positions = data.get('line_positions', {})
            
        camera.save()
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
    

@require_http_methods(["POST"])
def update_mode(request, camera_id):
    try:
        data = json.loads(request.body)
        mode = data.get('mode', 'h')
        request.session[f'mode_{camera_id}'] = mode
        request.session.modified = True
        return JsonResponse({'status': 'success', 'mode': mode})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)