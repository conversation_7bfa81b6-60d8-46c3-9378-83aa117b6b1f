{% extends 'layouts/default/page.html' %}
{% load bootstrap4 i18n %}

{% block content %}
{% get_current_language as language_code %}
{% get_available_languages as languages %}
<div class="container d-flex justify-content-center align-items-center" style="min-height: 100vh;">
    <div class="card shadow-lg p-4" style="width: 350px;">
        <h4 class="mb-3 text-center">{% translate 'Change language' %}</h4>

        <form action="{% url 'set_language' %}" method="post">
            {% csrf_token %}
            <input name="next" type="hidden" value="{{ redirect_to }}"/>

            <div class="form-group">
                <label class="form-control-label" for="language-field">{% translate 'Select the language' %}</label>
                <select name="language" class="form-control" id="language-field">
                    {% for code, name in languages %}
                        <option value="{{ code }}" {% if code == language_code %} selected="selected"{% endif %}>
                            {{ name }} ({{ code }})
                        </option>
                    {% endfor %}
                </select>
            </div>

            <button class="btn btn-success btn-block">{% translate 'Change' %}</button>
        </form>
    </div>
</div>
{% endblock content %}
