#!/usr/bin/env python3
"""
Debug script to test Shinobi API connectivity
"""

import requests
import json
import sys

# Shinobi settings from .env (fixed for localhost)
SHINOBI_API_URL = "http://localhost:8080"
SHINOBI_API_KEY = "vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx"
SHINOBI_GROUP_KEY = "VqJe1awj1m"

def test_shinobi_api():
    print("🔍 Testing Shinobi API connectivity...")
    print(f"API URL: {SHINOBI_API_URL}")
    print(f"Group Key: {SHINOBI_GROUP_KEY}")
    print(f"API Key: {SHINOBI_API_KEY[:10]}...")
    print()
    
    # Test 1: Basic connectivity
    print("📡 Test 1: Basic connectivity to Shinobi...")
    try:
        response = requests.get(f"{SHINOBI_API_URL}", timeout=5)
        print(f"✅ Basic connectivity: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Basic connectivity failed: {e}")
        return
    
    # Test 2: API endpoint
    print("\n📡 Test 2: Testing monitors API endpoint...")
    url = f"{SHINOBI_API_URL}/{SHINOBI_GROUP_KEY}/monitor"
    params = {"api": SHINOBI_API_KEY}
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"API URL: {url}")
        print(f"Params: {params}")
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API call successful!")
            print(f"Response type: {type(data)}")
            print(f"Response length: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                print("\n📹 Monitor details:")
                for i, monitor in enumerate(data):
                    print(f"  Monitor {i+1}:")
                    print(f"    MID: {monitor.get('mid', 'N/A')}")
                    print(f"    Name: {monitor.get('name', 'N/A')}")
                    print(f"    Mode: {monitor.get('mode', 'N/A')}")
                    print(f"    Status: {monitor.get('status', 'N/A')}")
                    print(f"    Type: {monitor.get('type', 'N/A')}")
                    print(f"    Host: {monitor.get('host', 'N/A')}")
                    print(f"    All fields: {list(monitor.keys())}")
                    print()
            else:
                print("⚠️ No monitors found in response")
                
        else:
            print(f"❌ API call failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ API call exception: {e}")
    
    # Test 3: Try localhost URL
    print("\n📡 Test 3: Testing with localhost URL...")
    localhost_url = f"http://localhost:8080/{SHINOBI_GROUP_KEY}/monitor"
    
    try:
        response = requests.get(localhost_url, params=params, timeout=10)
        print(f"Localhost URL: {localhost_url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Localhost API call successful!")
            print(f"Monitor count: {len(data) if isinstance(data, list) else 'N/A'}")
        else:
            print(f"❌ Localhost API call failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Localhost API call exception: {e}")

if __name__ == "__main__":
    test_shinobi_api()
