from django.urls import path
from . import views

app_name = 'vpn'

urlpatterns = [
    # VPN client management
    path('clients/', views.client_list, name='client_list'),
    path('clients/create/', views.client_create, name='client_create'),
    path('clients/<int:client_id>/', views.client_detail, name='client_detail'),
    path('clients/<int:client_id>/edit/', views.client_edit, name='client_edit'),
    path('clients/<int:client_id>/delete/', views.client_delete, name='client_delete'),
    path('clients/<int:client_id>/download/', views.client_download_config, name='client_download_config'),
    
    # VPN status and logs
    path('status/', views.vpn_status, name='vpn_status'),
    path('logs/', views.vpn_logs, name='vpn_logs'),
    
    # API endpoints
    path('api/status/', views.vpn_status_api, name='vpn_status_api'),
    path('api/client_status/', views.client_status_update, name='client_status_update'),
]