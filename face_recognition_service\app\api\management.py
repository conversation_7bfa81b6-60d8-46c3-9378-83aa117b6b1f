"""
🎖️ PERSON MANAGEMENT API ENDPOINTS
🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 2
⚔️ TACTICAL PERSON MANAGEMENT SERVICES
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import cv2
import numpy as np
from PIL import Image
import io

from app.models.ai_models import get_face_recognition_pipeline, FaceRecognitionPipeline
from app.core.cache import get_cache, CacheManager
from app.core.database import get_database, AsyncSession
from app.models.database_models import Person, FaceRecord

logger = logging.getLogger(__name__)

# 👥 Create management router
router = APIRouter(prefix="/persons", tags=["Person Management"])

# 📋 Pydantic models for request/response
class PersonCreate(BaseModel):
    name: str
    employee_id: Optional[str] = None
    department: Optional[str] = None
    role: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    notes: Optional[str] = None

class PersonUpdate(BaseModel):
    name: Optional[str] = None
    employee_id: Optional[str] = None
    department: Optional[str] = None
    role: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[str] = None
    notes: Optional[str] = None

class PersonResponse(BaseModel):
    id: int
    name: str
    employee_id: Optional[str]
    department: Optional[str]
    role: Optional[str]
    email: Optional[str]
    phone: Optional[str]
    status: str
    notes: Optional[str]
    created_at: str
    updated_at: str
    face_count: int = 0

@router.post("/", response_model=Dict[str, Any])
async def create_person(
    person_data: PersonCreate,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    👥 Create a new person in the database
    
    Args:
        person_data: Person information
        
    Returns:
        JSON response with created person details
    """
    try:
        start_time = time.time()
        
        # Create new person
        new_person = Person(
            name=person_data.name,
            employee_id=person_data.employee_id,
            department=person_data.department,
            role=person_data.role,
            email=person_data.email,
            phone=person_data.phone,
            notes=person_data.notes,
            status="active"
        )
        
        db.add(new_person)
        await db.commit()
        await db.refresh(new_person)
        
        # Prepare response
        response_data = {
            "success": True,
            "person": {
                "id": new_person.id,
                "name": new_person.name,
                "employee_id": new_person.employee_id,
                "department": new_person.department,
                "role": new_person.role,
                "email": new_person.email,
                "phone": new_person.phone,
                "status": new_person.status,
                "notes": new_person.notes,
                "created_at": new_person.created_at.isoformat(),
                "updated_at": new_person.updated_at.isoformat(),
                "face_count": 0
            },
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }
        
        logger.info(f"👥 Created person: {new_person.name} (ID: {new_person.id})")
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"🚨 Failed to create person: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create person: {str(e)}")

@router.get("/", response_model=Dict[str, Any])
async def list_persons(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    department: Optional[str] = None,
    db: AsyncSession = Depends(get_database)
):
    """
    👥 List all persons with optional filtering
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        status: Filter by status
        department: Filter by department
        
    Returns:
        JSON response with list of persons
    """
    try:
        start_time = time.time()
        
        # Build query
        from sqlalchemy import select, func
        
        query = select(Person, func.count(FaceRecord.id).label('face_count')).outerjoin(FaceRecord)
        
        # Apply filters
        if status:
            query = query.where(Person.status == status)
        if department:
            query = query.where(Person.department == department)
        
        # Apply pagination and grouping
        query = query.group_by(Person.id).offset(skip).limit(limit)
        
        # Execute query
        result = await db.execute(query)
        persons_with_counts = result.all()
        
        # Prepare response
        persons_list = []
        for person, face_count in persons_with_counts:
            persons_list.append({
                "id": person.id,
                "name": person.name,
                "employee_id": person.employee_id,
                "department": person.department,
                "role": person.role,
                "email": person.email,
                "phone": person.phone,
                "status": person.status,
                "notes": person.notes,
                "created_at": person.created_at.isoformat(),
                "updated_at": person.updated_at.isoformat(),
                "face_count": face_count or 0
            })
        
        response_data = {
            "success": True,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": len(persons_list)
            },
            "filters": {
                "status": status,
                "department": department
            },
            "persons": persons_list,
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }
        
        logger.info(f"👥 Listed {len(persons_list)} persons")
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"🚨 Failed to list persons: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list persons: {str(e)}")

@router.get("/{person_id}", response_model=Dict[str, Any])
async def get_person(
    person_id: int,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    👥 Get person details by ID
    
    Args:
        person_id: Person ID
        
    Returns:
        JSON response with person details
    """
    try:
        start_time = time.time()
        
        # Check cache first
        cached_person = await cache.get_person_metadata(person_id)
        if cached_person:
            logger.info(f"🚀 Cache hit for person: {person_id}")
            return JSONResponse(content=cached_person)
        
        # Query database
        from sqlalchemy import select, func
        
        query = select(Person, func.count(FaceRecord.id).label('face_count')).outerjoin(FaceRecord).where(Person.id == person_id).group_by(Person.id)
        
        result = await db.execute(query)
        person_with_count = result.first()
        
        if not person_with_count:
            raise HTTPException(status_code=404, detail="Person not found")
        
        person, face_count = person_with_count
        
        # Prepare response
        response_data = {
            "success": True,
            "person": {
                "id": person.id,
                "name": person.name,
                "employee_id": person.employee_id,
                "department": person.department,
                "role": person.role,
                "email": person.email,
                "phone": person.phone,
                "status": person.status,
                "notes": person.notes,
                "created_at": person.created_at.isoformat(),
                "updated_at": person.updated_at.isoformat(),
                "face_count": face_count or 0
            },
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }
        
        # Cache result
        await cache.set_person_metadata(person_id, response_data, ttl=1800)
        
        logger.info(f"👥 Retrieved person: {person.name} (ID: {person_id})")
        return JSONResponse(content=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Failed to get person: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get person: {str(e)}")

@router.post("/{person_id}/faces")
async def add_person_face(
    person_id: int,
    file: UploadFile = File(...),
    is_primary: bool = Form(False),
    db: AsyncSession = Depends(get_database),
    pipeline: FaceRecognitionPipeline = Depends(get_face_recognition_pipeline),
    cache: CacheManager = Depends(get_cache)
):
    """
    🎭 Add a face image to a person
    
    Args:
        person_id: Person ID
        file: Face image file
        is_primary: Whether this is the primary face
        
    Returns:
        JSON response with face record details
    """
    try:
        start_time = time.time()
        
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Check if person exists
        from sqlalchemy import select
        person_query = select(Person).where(Person.id == person_id)
        person_result = await db.execute(person_query)
        person = person_result.scalar_one_or_none()
        
        if not person:
            raise HTTPException(status_code=404, detail="Person not found")
        
        # Read and process image
        image_bytes = await file.read()
        image = Image.open(io.BytesIO(image_bytes))
        image_np = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # Process image through pipeline
        results = await pipeline.process_image(image_np)
        
        if not results:
            raise HTTPException(status_code=400, detail="No faces detected in image")
        
        if len(results) > 1:
            raise HTTPException(status_code=400, detail="Multiple faces detected. Please upload image with single face")
        
        # Get the detected face
        face_result = results[0]
        
        # Save face image (in production, save to proper storage)
        import hashlib
        image_hash = hashlib.sha256(image_bytes).hexdigest()
        image_path = f"faces/persons/{person_id}/{image_hash}.jpg"
        
        # Create face record
        face_record = FaceRecord(
            person_id=person_id,
            image_path=image_path,
            image_hash=image_hash,
            image_size=len(image_bytes),
            bounding_box=face_result["bbox"],
            detection_confidence=face_result["confidence"],
            feature_vector=face_result["features"].tobytes() if hasattr(face_result["features"], "tobytes") else bytes(face_result["features"]),
            feature_version="arcface_r100",
            is_primary=is_primary,
            status="active"
        )
        
        db.add(face_record)
        await db.commit()
        await db.refresh(face_record)
        
        # Cache the feature vector
        await cache.set_feature_vector(person_id, face_result["features"], ttl=3600)
        
        # Clear person cache
        await cache.delete_person_cache(person_id)
        
        # Prepare response
        response_data = {
            "success": True,
            "face_record": {
                "id": face_record.id,
                "person_id": face_record.person_id,
                "image_path": face_record.image_path,
                "image_hash": face_record.image_hash,
                "image_size": face_record.image_size,
                "bounding_box": face_record.bounding_box,
                "detection_confidence": face_record.detection_confidence,
                "feature_version": face_record.feature_version,
                "is_primary": face_record.is_primary,
                "status": face_record.status,
                "created_at": face_record.created_at.isoformat()
            },
            "processing_info": {
                "faces_detected": 1,
                "feature_size": len(face_result["features"]) if hasattr(face_result["features"], "__len__") else 0
            },
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }
        
        logger.info(f"🎭 Added face for person: {person.name} (ID: {person_id})")
        return JSONResponse(content=response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Failed to add face: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to add face: {str(e)}")

@router.get("/stats")
async def get_person_stats(
    db: AsyncSession = Depends(get_database)
):
    """
    📊 Get person management statistics
    
    Returns:
        JSON response with statistics
    """
    try:
        start_time = time.time()
        
        from sqlalchemy import select, func
        
        # Get person counts by status
        status_query = select(Person.status, func.count(Person.id)).group_by(Person.status)
        status_result = await db.execute(status_query)
        status_counts = dict(status_result.all())
        
        # Get department counts
        dept_query = select(Person.department, func.count(Person.id)).group_by(Person.department)
        dept_result = await db.execute(dept_query)
        dept_counts = dict(dept_result.all())
        
        # Get face record counts
        face_query = select(func.count(FaceRecord.id))
        face_result = await db.execute(face_query)
        total_faces = face_result.scalar()
        
        # Prepare response
        response_data = {
            "success": True,
            "statistics": {
                "total_persons": sum(status_counts.values()),
                "status_breakdown": status_counts,
                "department_breakdown": dept_counts,
                "total_face_records": total_faces,
                "avg_faces_per_person": round(total_faces / max(sum(status_counts.values()), 1), 2)
            },
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }
        
        logger.info("📊 Retrieved person management statistics")
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"🚨 Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")

# ✏️ UPDATE/EDIT ENDPOINTS

@router.put("/{person_id}")
async def update_person(
    person_id: int,
    person_data: PersonUpdate,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    ✏️ Update person information

    Args:
        person_id: Person ID to update
        person_data: Updated person information

    Returns:
        JSON response with updated person details
    """
    try:
        start_time = time.time()

        # Check if person exists
        from sqlalchemy import select
        person_query = select(Person).where(Person.id == person_id)
        person_result = await db.execute(person_query)
        person = person_result.scalar_one_or_none()

        if not person:
            raise HTTPException(status_code=404, detail="Person not found")

        # Store original data for response
        original_data = {
            "name": person.name,
            "employee_id": person.employee_id,
            "department": person.department,
            "role": person.role,
            "email": person.email,
            "phone": person.phone,
            "status": person.status,
            "notes": person.notes
        }

        # Update fields (only if provided)
        if person_data.name is not None:
            person.name = person_data.name
        if person_data.employee_id is not None:
            person.employee_id = person_data.employee_id
        if person_data.department is not None:
            person.department = person_data.department
        if person_data.role is not None:
            person.role = person_data.role
        if person_data.email is not None:
            person.email = person_data.email
        if person_data.phone is not None:
            person.phone = person_data.phone
        if person_data.status is not None:
            person.status = person_data.status
        if person_data.notes is not None:
            person.notes = person_data.notes

        # Save changes
        await db.commit()
        await db.refresh(person)

        # Clear cache
        await cache.delete_person_cache(person_id)

        # Get face count
        from sqlalchemy import func
        face_count_query = select(func.count(FaceRecord.id)).where(FaceRecord.person_id == person_id)
        face_count_result = await db.execute(face_count_query)
        face_count = face_count_result.scalar() or 0

        # Prepare response
        response_data = {
            "success": True,
            "message": f"Person '{person.name}' updated successfully",
            "original_data": original_data,
            "updated_person": {
                "id": person.id,
                "name": person.name,
                "employee_id": person.employee_id,
                "department": person.department,
                "role": person.role,
                "email": person.email,
                "phone": person.phone,
                "status": person.status,
                "notes": person.notes,
                "created_at": person.created_at.isoformat(),
                "updated_at": person.updated_at.isoformat(),
                "face_count": face_count
            },
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }

        logger.info(f"✏️ Updated person: {person.name} (ID: {person_id})")
        return JSONResponse(content=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Failed to update person: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update person: {str(e)}")

@router.patch("/{person_id}")
async def patch_person(
    person_id: int,
    person_data: PersonUpdate,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    ✏️ Partially update person information (same as PUT but semantically different)

    Args:
        person_id: Person ID to update
        person_data: Updated person information (partial)

    Returns:
        JSON response with updated person details
    """
    # Use the same logic as PUT for partial updates
    return await update_person(person_id, person_data, db, cache)

# 🗑️ DELETION ENDPOINTS

@router.delete("/{person_id}")
async def delete_person_by_id(
    person_id: int,
    confirm: bool = False,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    🗑️ Delete person and ALL associated data by ID

    Args:
        person_id: Person ID to delete
        confirm: Confirmation flag (required)

    Returns:
        JSON response with deletion status
    """
    try:
        if not confirm:
            raise HTTPException(
                status_code=400,
                detail="Deletion requires confirmation. Add ?confirm=true to the request"
            )

        start_time = time.time()

        # Check if person exists
        from sqlalchemy import select, func
        person_query = select(Person, func.count(FaceRecord.id).label('face_count')).outerjoin(FaceRecord).where(Person.id == person_id).group_by(Person.id)
        person_result = await db.execute(person_query)
        person_with_count = person_result.first()

        if not person_with_count:
            raise HTTPException(status_code=404, detail="Person not found")

        person, face_count = person_with_count

        # Store info for response
        deleted_info = {
            "person_id": person.id,
            "name": person.name,
            "employee_id": person.employee_id,
            "face_records_deleted": face_count or 0
        }

        # Delete person (cascade will delete face records)
        await db.delete(person)
        await db.commit()

        # Clear cache
        await cache.delete_person_cache(person_id)

        # Prepare response
        response_data = {
            "success": True,
            "message": f"Person '{person.name}' and all associated data deleted successfully",
            "deleted_data": deleted_info,
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }

        logger.info(f"🗑️ Deleted person: {person.name} (ID: {person_id}) with {face_count} face records")
        return JSONResponse(content=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Failed to delete person: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete person: {str(e)}")

@router.delete("/by-name/{person_name}")
async def delete_person_by_name(
    person_name: str,
    confirm: bool = False,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    🗑️ Delete person and ALL associated data by name

    Args:
        person_name: Person name to delete
        confirm: Confirmation flag (required)

    Returns:
        JSON response with deletion status
    """
    try:
        if not confirm:
            raise HTTPException(
                status_code=400,
                detail="Deletion requires confirmation. Add ?confirm=true to the request"
            )

        start_time = time.time()

        # Find person by name
        from sqlalchemy import select, func
        person_query = select(Person, func.count(FaceRecord.id).label('face_count')).outerjoin(FaceRecord).where(Person.name == person_name).group_by(Person.id)
        person_result = await db.execute(person_query)
        person_with_count = person_result.first()

        if not person_with_count:
            raise HTTPException(status_code=404, detail=f"Person with name '{person_name}' not found")

        person, face_count = person_with_count

        # Store info for response
        deleted_info = {
            "person_id": person.id,
            "name": person.name,
            "employee_id": person.employee_id,
            "face_records_deleted": face_count or 0
        }

        # Delete person
        await db.delete(person)
        await db.commit()

        # Clear cache
        await cache.delete_person_cache(person.id)

        # Prepare response
        response_data = {
            "success": True,
            "message": f"Person '{person_name}' and all associated data deleted successfully",
            "deleted_data": deleted_info,
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }

        logger.info(f"🗑️ Deleted person by name: {person_name} (ID: {person.id}) with {face_count} face records")
        return JSONResponse(content=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Failed to delete person by name: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete person by name: {str(e)}")

@router.delete("/by-employee-id/{employee_id}")
async def delete_person_by_employee_id(
    employee_id: str,
    confirm: bool = False,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    🗑️ Delete person and ALL associated data by employee ID

    Args:
        employee_id: Employee ID to delete
        confirm: Confirmation flag (required)

    Returns:
        JSON response with deletion status
    """
    try:
        if not confirm:
            raise HTTPException(
                status_code=400,
                detail="Deletion requires confirmation. Add ?confirm=true to the request"
            )

        start_time = time.time()

        # Find person by employee ID
        from sqlalchemy import select, func
        person_query = select(Person, func.count(FaceRecord.id).label('face_count')).outerjoin(FaceRecord).where(Person.employee_id == employee_id).group_by(Person.id)
        person_result = await db.execute(person_query)
        person_with_count = person_result.first()

        if not person_with_count:
            raise HTTPException(status_code=404, detail=f"Person with employee ID '{employee_id}' not found")

        person, face_count = person_with_count

        # Store info for response
        deleted_info = {
            "person_id": person.id,
            "name": person.name,
            "employee_id": person.employee_id,
            "face_records_deleted": face_count or 0
        }

        # Delete person
        await db.delete(person)
        await db.commit()

        # Clear cache
        await cache.delete_person_cache(person.id)

        # Prepare response
        response_data = {
            "success": True,
            "message": f"Person with employee ID '{employee_id}' and all associated data deleted successfully",
            "deleted_data": deleted_info,
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }

        logger.info(f"🗑️ Deleted person by employee ID: {employee_id} (ID: {person.id}) with {face_count} face records")
        return JSONResponse(content=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Failed to delete person by employee ID: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete person by employee ID: {str(e)}")

@router.delete("/{person_id}/faces")
async def delete_person_faces(
    person_id: int,
    confirm: bool = False,
    db: AsyncSession = Depends(get_database),
    cache: CacheManager = Depends(get_cache)
):
    """
    🗑️ Delete only face features for a person (keep person record)

    Args:
        person_id: Person ID
        confirm: Confirmation flag (required)

    Returns:
        JSON response with deletion status
    """
    try:
        if not confirm:
            raise HTTPException(
                status_code=400,
                detail="Deletion requires confirmation. Add ?confirm=true to the request"
            )

        start_time = time.time()

        # Check if person exists
        from sqlalchemy import select
        person_query = select(Person).where(Person.id == person_id)
        person_result = await db.execute(person_query)
        person = person_result.scalar_one_or_none()

        if not person:
            raise HTTPException(status_code=404, detail="Person not found")

        # Get face records to delete
        face_query = select(FaceRecord).where(FaceRecord.person_id == person_id)
        face_result = await db.execute(face_query)
        face_records = face_result.scalars().all()

        # Delete face records
        for face_record in face_records:
            await db.delete(face_record)

        await db.commit()

        # Clear cache
        await cache.delete_person_cache(person_id)

        # Prepare response
        response_data = {
            "success": True,
            "message": f"Deleted {len(face_records)} face records for person '{person.name}'",
            "deleted_data": {
                "person_id": person_id,
                "person_name": person.name,
                "face_records_deleted": len(face_records),
                "person_record_kept": True
            },
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }

        logger.info(f"🗑️ Deleted {len(face_records)} face records for person: {person.name} (ID: {person_id})")
        return JSONResponse(content=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Failed to delete face records: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete face records: {str(e)}")

logger.info("👥 Person Management API endpoints loaded")
logger.info("🎭 Face registration endpoints active")
logger.info("📊 Person statistics endpoints ready")
logger.info("🗑️ Deletion endpoints active")
