#!/usr/bin/env python3
"""
🎖️ REAL AI FACE RECOGNITION TEST
Test the actual AI capabilities
"""

import requests
import io
from PIL import Image
import time

def test_persons_api():
    """Test persons management API"""
    print("🎖️ Testing Persons API...")
    try:
        r = requests.get('http://localhost:8090/api/v1/persons/')
        print(f"✅ Persons API: HTTP {r.status_code}")
        if r.status_code == 200:
            data = r.json()
            persons_count = len(data.get('persons', []))
            print(f"   Total persons: {persons_count}")
            return True
        else:
            print(f"   Error: {r.text[:100]}")
            return False
    except Exception as e:
        print(f"❌ Persons API: {e}")
        return False

def test_face_detection():
    """Test face detection with real AI"""
    print("🎖️ Testing Face Detection AI...")
    try:
        # Create test image
        img = Image.new('RGB', (640, 480), 'blue')
        img_bytes = io.BytesIO()
        img.save(img_bytes, 'JPEG')
        img_bytes.seek(0)
        
        files = {'file': ('test.jpg', img_bytes, 'image/jpeg')}
        data = {'confidence_threshold': 0.25}
        
        print("   📦 Sending image to AI detection...")
        start_time = time.time()
        
        r = requests.post('http://localhost:8090/api/v1/detection/detect', 
                         files=files, data=data, timeout=60)
        
        processing_time = time.time() - start_time
        print(f"✅ Face Detection API: HTTP {r.status_code} ({processing_time:.2f}s)")
        
        if r.status_code == 200:
            result = r.json()
            success = result.get('success', False)
            faces = result.get('detection_info', {}).get('faces_detected', 0)
            model = result.get('detection_info', {}).get('model', 'unknown')
            
            print(f"   Success: {success}")
            print(f"   Faces detected: {faces}")
            print(f"   AI Model: {model}")
            print(f"   Processing time: {processing_time:.2f}s")
            
            if 'gpu_info' in result:
                gpu_info = result['gpu_info']
                print(f"   GPU Available: {gpu_info.get('cuda_available', False)}")
                print(f"   Device: {gpu_info.get('device', 'unknown')}")
            
            return True
        else:
            print(f"   Error: {r.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Face Detection AI: {e}")
        return False

def test_create_person():
    """Test creating a person"""
    print("🎖️ Testing Person Creation...")
    try:
        person_data = {
            "name": "Test AI Person",
            "employee_id": "AI001",
            "department": "AI Testing",
            "role": "Test Subject"
        }
        
        r = requests.post('http://localhost:8090/api/v1/persons/', 
                         json=person_data, timeout=30)
        
        print(f"✅ Create Person API: HTTP {r.status_code}")
        
        if r.status_code == 200:
            result = r.json()
            if result.get('success'):
                person = result.get('person', {})
                print(f"   Created: {person.get('name')} (ID: {person.get('id')})")
                return person.get('id')
            else:
                print(f"   Failed: {result}")
                return None
        else:
            print(f"   Error: {r.text[:200]}")
            return None
            
    except Exception as e:
        print(f"❌ Create Person: {e}")
        return None

def test_management_web_interface():
    """Test the web management interface"""
    print("🎖️ Testing Web Management Interface...")
    try:
        # Test if the HTML file exists and can be opened
        import os
        html_file = "face_recognition_management_web.html"
        if os.path.exists(html_file):
            print(f"✅ Web Interface: {html_file} is ready")
            print(f"   🌐 Open: file://{os.path.abspath(html_file)}")
            return True
        else:
            print(f"❌ Web Interface: {html_file} not found")
            return False
    except Exception as e:
        print(f"❌ Web Interface: {e}")
        return False

def main():
    """Main test function"""
    print("🎖️ REAL AI FACE RECOGNITION TEST")
    print("=" * 50)
    
    # Test service health
    try:
        r = requests.get('http://localhost:8090/health', timeout=10)
        if r.status_code == 200:
            health = r.json()
            print(f"✅ Service Health: {health.get('status')}")
            print(f"   Service: {health.get('service')}")
            print(f"   Version: {health.get('version')}")
        else:
            print("❌ Service not healthy")
            return
    except:
        print("❌ Cannot connect to Face Recognition Service")
        return
    
    print("\n🧠 TESTING AI CAPABILITIES...")
    print("-" * 30)
    
    # Run tests
    persons_ok = test_persons_api()
    detection_ok = test_face_detection()
    create_ok = test_create_person() is not None
    web_ok = test_management_web_interface()
    
    # Summary
    print(f"\n🎖️ REAL AI TEST SUMMARY")
    print("=" * 30)
    print(f"Service Health: {'✅ OK' if True else '❌ FAIL'}")
    print(f"Persons API: {'✅ OK' if persons_ok else '❌ FAIL'}")
    print(f"Face Detection AI: {'✅ OK' if detection_ok else '❌ FAIL'}")
    print(f"Person Creation: {'✅ OK' if create_ok else '❌ FAIL'}")
    print(f"Web Interface: {'✅ OK' if web_ok else '❌ FAIL'}")
    
    if all([persons_ok, detection_ok, create_ok, web_ok]):
        print("\n🏆 REAL AI FACE RECOGNITION IS FULLY OPERATIONAL!")
        print("🎯 Next Steps:")
        print("   1. Open: http://localhost:8090/docs (API Documentation)")
        print("   2. Open: face_recognition_management_web.html (Web Interface)")
        print("   3. Go to: http://localhost:5000/cameras/ (Camera Integration)")
        print("   4. Click 'Start Recognition' to test live face recognition!")
        print("\n🎖️ THE FACE RECOGNITION EMPIRE IS READY FOR BATTLE!")
    else:
        print("\n⚠️ Some components need attention. Check the logs above.")
    
    return 0

if __name__ == "__main__":
    main()
