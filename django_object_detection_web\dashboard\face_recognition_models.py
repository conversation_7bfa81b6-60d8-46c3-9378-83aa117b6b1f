"""
🎖️ FACE RECOGNITION MODELS - <PERSON>IN<PERSON>BI CCTV DJANGO
🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 3
⚔️ TACTICAL SHINOBI MODELS FOR FACE RECOGNITION
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import json

User = get_user_model()

class RecognizedPerson(models.Model):
    """👤 Recognized person model for Shinobi CCTV system"""
    
    class PersonStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        VISITOR = 'visitor', 'Visitor'
        STAFF = 'staff', 'Staff'
        UNKNOWN = 'unknown', 'Unknown'
    
    # Basic information
    name = models.CharField(max_length=100, help_text="Full name of the person")
    employee_id = models.Char<PERSON>ield(max_length=50, blank=True, null=True,
                                  help_text="Employee or visitor ID")
    
    # Location association (using existing Location model)
    location = models.ForeignKey('dashboard.Location', on_delete=models.CASCADE,
                               related_name='recognized_persons',
                               help_text="Location where person was first recognized")
    
    # Status and classification
    status = models.CharField(max_length=20, choices=PersonStatus.choices, 
                            default=PersonStatus.VISITOR, db_index=True)
    
    # Face recognition service integration
    face_recognition_id = models.IntegerField(blank=True, null=True, unique=True,
                                            help_text="ID in face recognition microservice")
    
    # Recognition statistics
    first_seen = models.DateTimeField(auto_now_add=True)
    last_seen = models.DateTimeField(auto_now=True)
    recognition_count = models.IntegerField(default=0, help_text="Total number of recognitions")
    
    # Additional metadata
    notes = models.TextField(blank=True, null=True, help_text="Additional notes")
    confidence_threshold = models.FloatField(
        default=0.5,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="Recognition confidence threshold for this person"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-last_seen']
        verbose_name = 'Recognized Person'
        verbose_name_plural = 'Recognized Persons'
        indexes = [
            models.Index(fields=['location', 'status']),
            models.Index(fields=['face_recognition_id']),
            models.Index(fields=['last_seen']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.location.name})"
    
    @property
    def is_frequent_visitor(self):
        """Check if person is a frequent visitor (>5 recognitions)"""
        return self.recognition_count > 5

class CameraRecognitionEvent(models.Model):
    """📊 Camera recognition event for Shinobi system"""
    
    class EventType(models.TextChoices):
        DETECTION = 'detection', 'Face Detection'
        RECOGNITION = 'recognition', 'Face Recognition'
        UNKNOWN = 'unknown', 'Unknown Face'
        ALERT = 'alert', 'Recognition Alert'
    
    # Event information
    event_type = models.CharField(max_length=20, choices=EventType.choices, db_index=True)
    timestamp = models.DateTimeField(default=timezone.now, db_index=True)
    
    # Camera and location information
    camera = models.ForeignKey('dashboard.Camera', on_delete=models.CASCADE,
                             related_name='recognition_events')
    location = models.ForeignKey('dashboard.Location', on_delete=models.CASCADE,
                               related_name='recognition_events')
    
    # Person information (null for unknown faces)
    recognized_person = models.ForeignKey(RecognizedPerson, on_delete=models.SET_NULL, 
                                        null=True, blank=True,
                                        related_name='recognition_events')
    
    # Recognition details
    confidence_score = models.FloatField(
        blank=True, null=True,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="Recognition confidence score"
    )
    bounding_box = models.JSONField(blank=True, null=True,
                                  help_text="Face bounding box coordinates")
    
    # Shinobi integration
    shinobi_monitor_id = models.CharField(max_length=100, blank=True, null=True,
                                        help_text="Shinobi monitor ID")
    
    # Image information
    event_image_path = models.CharField(max_length=500, blank=True, null=True,
                                      help_text="Path to event image")
    
    # Processing information
    processing_time_ms = models.FloatField(blank=True, null=True,
                                         help_text="Processing time in milliseconds")
    
    # Additional metadata
    metadata = models.JSONField(blank=True, null=True,
                              help_text="Additional event metadata")
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Camera Recognition Event'
        verbose_name_plural = 'Camera Recognition Events'
        indexes = [
            models.Index(fields=['timestamp', 'location']),
            models.Index(fields=['recognized_person', 'timestamp']),
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['camera', 'timestamp']),
        ]
    
    def __str__(self):
        person_name = self.recognized_person.name if self.recognized_person else "Unknown"
        return f"{self.event_type} - {person_name} at {self.camera.name} ({self.timestamp.strftime('%Y-%m-%d %H:%M')})"

class FaceRecognitionConfig(models.Model):
    """⚙️ Face recognition configuration for Shinobi system"""
    
    # Location-specific settings
    location = models.OneToOneField('dashboard.Location', on_delete=models.CASCADE,
                                  related_name='face_recognition_config')
    
    # Recognition settings
    enabled = models.BooleanField(default=True, help_text="Enable face recognition for this location")
    detection_confidence = models.FloatField(
        default=0.25,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="Face detection confidence threshold"
    )
    recognition_confidence = models.FloatField(
        default=0.5,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="Face recognition confidence threshold"
    )
    
    # Processing settings
    processing_interval = models.IntegerField(
        default=10,
        validators=[MinValueValidator(1), MaxValueValidator(60)],
        help_text="Processing interval in seconds"
    )
    max_faces_per_frame = models.IntegerField(
        default=5,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        help_text="Maximum faces to process per frame"
    )
    
    # Storage settings
    store_unknown_faces = models.BooleanField(
        default=True,
        help_text="Store images of unknown faces"
    )
    retention_days = models.IntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(365)],
        help_text="Days to retain recognition events"
    )
    
    # Alert settings
    alert_on_unknown = models.BooleanField(
        default=False,
        help_text="Send alerts for unknown faces"
    )
    alert_on_frequent_visitor = models.BooleanField(
        default=True,
        help_text="Send alerts for frequent visitors"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Face Recognition Config'
        verbose_name_plural = 'Face Recognition Configs'
    
    def __str__(self):
        return f"Face Recognition Config for {self.location.name}"

class RecognitionAlert(models.Model):
    """🚨 Recognition alert model for notifications"""
    
    class AlertType(models.TextChoices):
        UNKNOWN_FACE = 'unknown_face', 'Unknown Face Detected'
        FREQUENT_VISITOR = 'frequent_visitor', 'Frequent Visitor'
        UNAUTHORIZED_ACCESS = 'unauthorized_access', 'Unauthorized Access'
        SYSTEM_ERROR = 'system_error', 'System Error'
    
    class AlertStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        ACKNOWLEDGED = 'acknowledged', 'Acknowledged'
        RESOLVED = 'resolved', 'Resolved'
        DISMISSED = 'dismissed', 'Dismissed'
    
    # Alert information
    alert_type = models.CharField(max_length=30, choices=AlertType.choices, db_index=True)
    status = models.CharField(max_length=20, choices=AlertStatus.choices, 
                            default=AlertStatus.PENDING, db_index=True)
    
    # Location and camera
    location = models.ForeignKey('dashboard.Location', on_delete=models.CASCADE,
                               related_name='recognition_alerts')
    camera = models.ForeignKey('dashboard.Camera', on_delete=models.CASCADE,
                             related_name='recognition_alerts')
    
    # Associated event
    recognition_event = models.ForeignKey(CameraRecognitionEvent, on_delete=models.CASCADE,
                                        related_name='alerts')
    
    # Alert details
    title = models.CharField(max_length=200, help_text="Alert title")
    message = models.TextField(help_text="Alert message")
    severity = models.CharField(max_length=20, default='medium',
                              choices=[
                                  ('low', 'Low'),
                                  ('medium', 'Medium'),
                                  ('high', 'High'),
                                  ('critical', 'Critical')
                              ])
    
    # User interaction
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, 
                                      null=True, blank=True,
                                      related_name='acknowledged_alerts')
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Recognition Alert'
        verbose_name_plural = 'Recognition Alerts'
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['location', 'status']),
            models.Index(fields=['alert_type', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.alert_type} - {self.location.name} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"
    
    def acknowledge(self, user):
        """Acknowledge the alert"""
        self.status = self.AlertStatus.ACKNOWLEDGED
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        self.save()

class RecognitionStatistics(models.Model):
    """📊 Daily recognition statistics for analytics"""
    
    # Date and location
    date = models.DateField(db_index=True)
    location = models.ForeignKey('dashboard.Location', on_delete=models.CASCADE,
                               related_name='recognition_statistics')
    
    # Statistics
    total_detections = models.IntegerField(default=0)
    total_recognitions = models.IntegerField(default=0)
    unknown_faces = models.IntegerField(default=0)
    unique_persons = models.IntegerField(default=0)
    
    # Performance metrics
    avg_processing_time_ms = models.FloatField(blank=True, null=True)
    avg_confidence_score = models.FloatField(blank=True, null=True)
    
    # Camera activity
    active_cameras = models.IntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['date', 'location']
        ordering = ['-date']
        verbose_name = 'Recognition Statistics'
        verbose_name_plural = 'Recognition Statistics'
        indexes = [
            models.Index(fields=['date', 'location']),
        ]
    
    def __str__(self):
        return f"Recognition Stats - {self.location.name} ({self.date})"
