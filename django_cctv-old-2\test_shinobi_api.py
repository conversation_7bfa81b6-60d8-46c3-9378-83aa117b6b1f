#!/usr/bin/env python3
"""
Test script to debug Shinobi API connectivity from shinobi_cctv_django container
"""

import os
import sys
import requests
import json
import time

# Add Django project to path
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shinobi_cctv_django.settings')

import django
django.setup()

from django.conf import settings

def test_shinobi_api():
    """Test Shinobi API connectivity and authentication"""
    
    print("🔍 Testing Shinobi API Connectivity")
    print("=" * 50)
    
    # Get configuration from Django settings
    api_key = getattr(settings, 'SHINOBI_API_KEY', None)
    group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)
    host = getattr(settings, 'SHINOBI_HOST', None)
    
    print(f"API Key: {api_key}")
    print(f"Group Key: {group_key}")
    print(f"Host: {host}")
    print()
    
    if not api_key or not group_key or not host:
        print("❌ Missing API configuration!")
        return False
    
    # Test 1: Basic connectivity
    print("📡 Test 1: Basic connectivity to Shinobi")
    try:
        response = requests.get(f"{host}", timeout=10)
        print(f"✅ Basic connectivity: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Basic connectivity failed: {e}")
        return False
    
    # Test 2: API endpoint test
    print("\n📡 Test 2: Testing API endpoint")
    api_url = f"{host}/{group_key}/monitor"
    params = {"api": api_key}
    
    print(f"URL: {api_url}")
    print(f"Params: {params}")
    
    try:
        print("Making API request...")
        response = requests.get(api_url, params=params, timeout=30)
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ API call successful!")
                print(f"Response data type: {type(data)}")
                if isinstance(data, list):
                    print(f"Number of monitors: {len(data)}")
                    for i, monitor in enumerate(data[:3]):  # Show first 3 monitors
                        print(f"  Monitor {i+1}: {monitor.get('name', 'Unknown')} (ID: {monitor.get('mid', 'Unknown')})")
                else:
                    print(f"Response data: {data}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}")
                return False
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API request timed out (30 seconds)")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_shinobi_client():
    """Test the ShinobiClient class"""
    print("\n🔧 Test 3: Testing ShinobiClient class")
    
    try:
        from dashboard.real_shinobi import ShinobiClient
        
        client = ShinobiClient()
        print(f"Client API Key: {client.api_key}")
        print(f"Client Group Key: {client.group_key}")
        print(f"Client Host: {client.host}")
        
        print("Calling get_monitors()...")
        success, data = client.get_monitors()
        
        if success:
            print("✅ ShinobiClient.get_monitors() successful!")
            print(f"Data type: {type(data)}")
            if isinstance(data, list):
                print(f"Number of monitors: {len(data)}")
            else:
                print(f"Data: {data}")
            return True
        else:
            print(f"❌ ShinobiClient.get_monitors() failed: {data}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ ShinobiClient error: {e}")
        return False

if __name__ == "__main__":
    print("Starting Shinobi API tests...\n")
    
    # Run tests
    test1_result = test_shinobi_api()
    test2_result = test_shinobi_client()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Basic API Test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"  ShinobiClient Test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed! Shinobi API is working correctly.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Check the configuration and logs.")
        sys.exit(1)
