from django.contrib import admin
from .models import Camera, CameraGroup, CameraPreset, CameraEvent

@admin.register(Camera)
class CameraAdmin(admin.ModelAdmin):
    list_display = ('name', 'location', 'type', 'status', 'ip_address', 'last_online')
    list_filter = ('status', 'type', 'groups')
    search_fields = ('name', 'location', 'ip_address')
    readonly_fields = ('last_online',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'location', 'type', 'groups')
        }),
        ('Connection Details', {
            'fields': ('stream_url', 'username', 'password', 'ip_address', 'port')
        }),
        ('Status Information', {
            'fields': ('status', 'last_online')
        }),
        ('Shinobi Integration', {
            'fields': ('shinobi_monitor_id',)
        }),
    )

@admin.register(CameraGroup)
class CameraGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'camera_count', 'user_count', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')

    def camera_count(self, obj):
        return obj.cameras.count()
    camera_count.short_description = 'Cameras'

    def user_count(self, obj):
        return obj.users.count()
    user_count.short_description = 'Users with Access'

@admin.register(CameraPreset)
class CameraPresetAdmin(admin.ModelAdmin):
    list_display = ('name', 'camera')
    list_filter = ('camera',)
    search_fields = ('name', 'camera__name')

@admin.register(CameraEvent)
class CameraEventAdmin(admin.ModelAdmin):
    list_display = ('camera', 'event_type', 'timestamp')
    list_filter = ('event_type', 'camera')
    search_fields = ('camera__name', 'event_type')
    readonly_fields = ('timestamp', 'camera', 'event_type', 'snapshot_url', 'details')