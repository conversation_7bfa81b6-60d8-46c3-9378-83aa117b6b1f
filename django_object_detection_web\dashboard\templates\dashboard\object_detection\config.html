{% extends 'dashboard/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .config-section {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        background-color: #f8f9fa;
    }
    .config-section h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
    }
    .object-checkbox {
        margin-bottom: 0.5rem;
    }
    .object-checkbox label {
        margin-left: 0.5rem;
        font-weight: normal;
    }
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    .camera-preview {
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
        padding: 2rem;
        text-align: center;
        background-color: #f8f9fa;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">🎯 {{ title }}</h1>
                    <p class="text-muted">Configure object detection settings for {{ camera.name }}</p>
                </div>
                <div>
                    <a href="{% url 'dashboard:object_detection_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Configuration Form -->
        <div class="col-lg-8">
            <form method="post" class="needs-validation" novalidate>
                {% csrf_token %}
                
                <!-- Basic Configuration -->
                <div class="config-section">
                    <h6><i class="fas fa-cog"></i> Basic Configuration</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.camera.id_for_label }}" class="form-label">Camera</label>
                                {{ form.camera|add_class:"form-select" }}
                                {% if form.camera.errors %}
                                    <div class="invalid-feedback d-block">{{ form.camera.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.detection_mode.id_for_label }}" class="form-label">Detection Mode</label>
                                {{ form.detection_mode|add_class:"form-select" }}
                                <div class="form-help">Choose how objects are counted and tracked</div>
                                {% if form.detection_mode.errors %}
                                    <div class="invalid-feedback d-block">{{ form.detection_mode.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-check">
                                {{ form.is_active|add_class:"form-check-input" }}
                                <label for="{{ form.is_active.id_for_label }}" class="form-check-label">
                                    Enable Object Detection for this camera
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detection Settings -->
                <div class="config-section">
                    <h6><i class="fas fa-eye"></i> Detection Settings</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.confidence_threshold.id_for_label }}" class="form-label">Confidence Threshold</label>
                                {{ form.confidence_threshold|add_class:"form-control" }}
                                <div class="form-help">Higher values = fewer false positives, lower values = more detections</div>
                                {% if form.confidence_threshold.errors %}
                                    <div class="invalid-feedback d-block">{{ form.confidence_threshold.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Object Types -->
                <div class="config-section">
                    <h6><i class="fas fa-list"></i> Enabled Object Types</h6>
                    <div class="row">
                        {% for choice in form.enabled_objects %}
                        <div class="col-md-3">
                            <div class="object-checkbox">
                                {{ choice.tag }}
                                <label for="{{ choice.id_for_label }}">{{ choice.choice_label }}</label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="form-help">Select which types of objects to detect and count</div>
                </div>

                <!-- Speed Detection -->
                <div class="config-section">
                    <h6><i class="fas fa-tachometer-alt"></i> Speed Detection Settings</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.speed_limit_kmh.id_for_label }}" class="form-label">Speed Limit (km/h)</label>
                                {{ form.speed_limit_kmh|add_class:"form-control" }}
                                <div class="form-help">Speed limit for violation detection</div>
                                {% if form.speed_limit_kmh.errors %}
                                    <div class="invalid-feedback d-block">{{ form.speed_limit_kmh.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.distance_meters.id_for_label }}" class="form-label">Distance (meters)</label>
                                {{ form.distance_meters|add_class:"form-control" }}
                                <div class="form-help">Distance between detection lines for speed calculation</div>
                                {% if form.distance_meters.errors %}
                                    <div class="invalid-feedback d-block">{{ form.distance_meters.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <a href="{% url 'dashboard:object_detection_dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Configuration
                    </button>
                </div>
            </form>
        </div>

        <!-- Camera Preview & Info -->
        <div class="col-lg-4">
            <!-- Camera Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-video"></i> Camera Information</h6>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">Name:</dt>
                        <dd class="col-sm-8">{{ camera.name }}</dd>
                        
                        <dt class="col-sm-4">Location:</dt>
                        <dd class="col-sm-8">{{ camera.branch.name }}</dd>
                        
                        <dt class="col-sm-4">Monitor ID:</dt>
                        <dd class="col-sm-8">{{ camera.monitor_id|default:"Not set" }}</dd>
                        
                        <dt class="col-sm-4">Status:</dt>
                        <dd class="col-sm-8">
                            {% if camera.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>

            <!-- Camera Preview -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-eye"></i> Camera Preview</h6>
                </div>
                <div class="card-body">
                    <div class="camera-preview">
                        {% if camera.monitor_id %}
                            <img src="http://localhost:8080/{{ camera.monitor_id }}/s.jpg" 
                                 alt="Camera Preview" 
                                 class="img-fluid"
                                 style="max-height: 200px;"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display: none;">
                                <i class="fas fa-video-slash fa-3x text-muted mb-2"></i>
                                <p class="text-muted">Preview not available</p>
                            </div>
                        {% else %}
                            <i class="fas fa-video-slash fa-3x text-muted mb-2"></i>
                            <p class="text-muted">No monitor ID configured</p>
                        {% endif %}
                    </div>
                    <small class="text-muted">
                        Live preview from Shinobi CCTV system
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-refresh camera preview every 10 seconds
setInterval(function() {
    const img = document.querySelector('.camera-preview img');
    if (img && img.style.display !== 'none') {
        const src = img.src;
        img.src = '';
        img.src = src + '?t=' + new Date().getTime();
    }
}, 10000);
</script>
{% endblock %}
