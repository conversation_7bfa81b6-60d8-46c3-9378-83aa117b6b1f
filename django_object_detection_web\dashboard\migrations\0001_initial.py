# Generated by Django 5.2 on 2025-06-12 17:21

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='profile_images/')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('date_modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'db_table': 'shared_users',
                'managed': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Camera',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('location_name', models.CharField(blank=True, max_length=200)),
                ('type', models.CharField(choices=[('ip', 'IP Camera'), ('rtsp', 'RTSP Stream'), ('http', 'HTTP Stream'), ('hls', 'HLS Stream')], default='ip', max_length=20)),
                ('stream_url', models.CharField(max_length=255)),
                ('rtsp_url', models.CharField(blank=True, max_length=255, null=True)),
                ('username', models.CharField(blank=True, max_length=100, null=True)),
                ('password', models.CharField(blank=True, max_length=100, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('port', models.IntegerField(default=80)),
                ('status', models.CharField(choices=[('online', 'Online'), ('offline', 'Offline'), ('disabled', 'Disabled'), ('maintenance', 'Maintenance')], default='offline', max_length=20)),
                ('last_online', models.DateTimeField(blank=True, null=True)),
                ('shinobi_monitor_id', models.CharField(blank=True, max_length=100, null=True)),
                ('shinobi_id', models.CharField(blank=True, max_length=100, null=True)),
                ('shinobi_api_key', models.CharField(blank=True, max_length=100, null=True)),
                ('shinobi_group_key', models.CharField(blank=True, max_length=100, null=True)),
                ('is_recording', models.BooleanField(default=False)),
                ('is_ptz', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'shared_cameras',
                'ordering': ['name'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='CameraGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'shared_camera_groups',
                'ordering': ['name'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='CameraRecognitionEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('face_detected', 'Face Detected'), ('person_recognized', 'Person Recognized'), ('unknown_face', 'Unknown Face'), ('recognition_error', 'Recognition Error')], max_length=20)),
                ('confidence_score', models.FloatField(help_text='Recognition confidence (0.0 - 1.0)')),
                ('recognition_log_id', models.IntegerField(help_text='ID from Face Recognition service log')),
                ('snapshot_url', models.URLField(blank=True, null=True)),
                ('face_crop_path', models.CharField(blank=True, max_length=500, null=True)),
                ('bounding_box', models.JSONField(blank=True, help_text='Face bounding box [x1, y1, x2, y2]', null=True)),
                ('processing_time_ms', models.FloatField(blank=True, null=True)),
                ('extra_data', models.JSONField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Camera Recognition Event',
                'verbose_name_plural': 'Camera Recognition Events',
                'db_table': 'shared_camera_recognition_events',
                'ordering': ['-timestamp'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Person',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('face_recognition_id', models.IntegerField(help_text='ID from Face Recognition service', unique=True)),
                ('name', models.CharField(max_length=255)),
                ('employee_id', models.CharField(blank=True, max_length=100, null=True)),
                ('department', models.CharField(blank=True, max_length=100, null=True)),
                ('role', models.CharField(blank=True, max_length=100, null=True)),
                ('email', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(default='active', max_length=50)),
                ('last_sync', models.DateTimeField(auto_now=True)),
                ('sync_status', models.CharField(default='synced', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Person',
                'verbose_name_plural': 'Persons',
                'db_table': 'shared_persons',
                'ordering': ['name'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('permissions', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'shared_roles',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('zipcode', models.CharField(blank=True, max_length=20, null=True)),
                ('vpn_status', models.BooleanField(default=False)),
                ('last_online', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('vpn_enabled', models.BooleanField(default=False)),
                ('vpn_server', models.CharField(blank=True, max_length=255, null=True)),
                ('vpn_client_config', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='SystemHealth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cpu_usage', models.FloatField(blank=True, null=True)),
                ('memory_usage', models.FloatField(blank=True, null=True)),
                ('storage_usage', models.FloatField(blank=True, null=True)),
                ('vpn_connections', models.IntegerField(blank=True, null=True)),
                ('online_cameras', models.IntegerField(blank=True, null=True)),
                ('offline_cameras', models.IntegerField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name_plural': 'System Health Records',
            },
        ),
        migrations.CreateModel(
            name='Incident',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('snapshot_url', models.CharField(blank=True, max_length=255, null=True)),
                ('status', models.CharField(choices=[('open', 'Open'), ('closed', 'Closed'), ('investigating', 'Investigating')], default='open', max_length=20)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('camera', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incidents', to='dashboard.camera')),
                ('reporter', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reported_incidents', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incidents', to='dashboard.location')),
            ],
        ),
        migrations.CreateModel(
            name='LocationAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('granted_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.location')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'location')},
            },
        ),
        migrations.AddField(
            model_name='location',
            name='users_with_access',
            field=models.ManyToManyField(blank=True, related_name='accessible_locations', through='dashboard.LocationAccess', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='VpnClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.CharField(max_length=128)),
                ('cert_cn', models.CharField(max_length=128, unique=True)),
                ('ovpn_path', models.CharField(max_length=256)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('revoked', models.BooleanField(default=False)),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dashboard.location')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RecognitionAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('authorized_entry', 'Authorized Entry'), ('unauthorized_entry', 'Unauthorized Entry'), ('unknown_person', 'Unknown Person'), ('recognition_error', 'Recognition Error'), ('suspicious_activity', 'Suspicious Activity')], max_length=25)),
                ('alert_status', models.CharField(choices=[('active', 'Active'), ('acknowledged', 'Acknowledged'), ('resolved', 'Resolved'), ('false_positive', 'False Positive')], default='active', max_length=15)),
                ('confidence_score', models.FloatField(help_text='Recognition confidence (0.0 - 1.0)')),
                ('recognition_log_id', models.IntegerField(help_text='ID from Face Recognition service log')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True, null=True)),
                ('snapshot_url', models.URLField(blank=True, null=True)),
                ('face_crop_path', models.CharField(blank=True, max_length=500, null=True)),
                ('bounding_box', models.JSONField(blank=True, help_text='Face bounding box [x1, y1, x2, y2]', null=True)),
                ('processing_time_ms', models.FloatField(blank=True, null=True)),
                ('extra_data', models.JSONField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('camera', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recognition_alerts', to='dashboard.camera')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recognition_alerts', to='dashboard.location')),
                ('person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dashboard.person')),
            ],
            options={
                'verbose_name': 'Recognition Alert',
                'verbose_name_plural': 'Recognition Alerts',
                'db_table': 'dashboard_recognition_alert',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['location', '-timestamp'], name='dashboard_r_locatio_736b62_idx'), models.Index(fields=['camera', '-timestamp'], name='dashboard_r_camera__d84459_idx'), models.Index(fields=['alert_type', 'alert_status'], name='dashboard_r_alert_t_db1daf_idx'), models.Index(fields=['person', '-timestamp'], name='dashboard_r_person__036bae_idx')],
            },
        ),
    ]
