# Generated by Django 5.0.3 on 2025-06-04 18:15

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='VpnClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('client_id', models.CharField(max_length=100, unique=True)),
                ('virtual_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('real_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('connected_since', models.DateTimeField(blank=True, null=True)),
                ('last_seen', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('revoked', 'Revoked')], default='inactive', max_length=20)),
                ('config_file', models.FileField(blank=True, null=True, upload_to='vpn_configs/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VpnLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('connect', 'Connected'), ('disconnect', 'Disconnected'), ('auth_failure', 'Authentication Failure'), ('error', 'Error'), ('info', 'Information')], default='info', max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('message', models.TextField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('additional_data', models.JSONField(blank=True, null=True)),
                ('client', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='vpn.vpnclient')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
