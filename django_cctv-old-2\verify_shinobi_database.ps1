# PowerShell script to verify Shinobi database setup
Write-Host "🔍 Verifying Shinobi Database Setup..." -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Test 1: Check if MariaDB container is running
Write-Status "Checking MariaDB container status..."
try {
    $mariadbStatus = docker ps --filter "name=shinobi_mariadb_for_shinobi" --format "{{.Status}}"
    if ($mariadbStatus -like "*Up*") {
        Write-Success "MariaDB container is running: $mariadbStatus"
    } else {
        Write-Error "MariaDB container is not running"
        exit 1
    }
} catch {
    Write-Error "Failed to check MariaDB container status"
    exit 1
}

# Test 2: Check database connectivity
Write-Status "Testing database connectivity..."
try {
    $dbTest = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -e "SELECT 1 as test;" 2>$null
    if ($dbTest -like "*test*") {
        Write-Success "Database is accessible"
    } else {
        Write-Error "Database is not accessible"
        exit 1
    }
} catch {
    Write-Error "Failed to connect to database"
    exit 1
}

# Test 3: Check if shinobi_db database exists
Write-Status "Checking if shinobi_db database exists..."
try {
    $dbExists = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -e "SHOW DATABASES LIKE 'shinobi_db';" 2>$null
    if ($dbExists -like "*shinobi_db*") {
        Write-Success "shinobi_db database exists"
    } else {
        Write-Error "shinobi_db database does not exist"
        exit 1
    }
} catch {
    Write-Error "Failed to check database existence"
    exit 1
}

# Test 4: Check if all required tables exist
Write-Status "Checking if all required tables exist..."
$requiredTables = @("API", "Users", "Monitors", "Videos", "Events", "Files", "Logs", "LoginTokens", "Presets", "Schedules")
try {
    $existingTables = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -D shinobi_db -e "SHOW TABLES;" 2>$null
    
    $missingTables = @()
    foreach ($table in $requiredTables) {
        if ($existingTables -notlike "*$table*") {
            $missingTables += $table
        }
    }
    
    if ($missingTables.Count -eq 0) {
        Write-Success "All required tables exist: $($requiredTables -join ', ')"
    } else {
        Write-Error "Missing tables: $($missingTables -join ', ')"
        exit 1
    }
} catch {
    Write-Error "Failed to check table existence"
    exit 1
}

# Test 5: Check if super admin user exists
Write-Status "Checking if super admin user exists..."
try {
    $superUser = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -D shinobi_db -e "SELECT mail, accountType FROM Users WHERE mail = '<EMAIL>';" 2>$null
    if ($superUser -like "*<EMAIL>*" -and $superUser -like "*1*") {
        Write-Success "Super admin user exists with correct account type"
    } else {
        Write-Warning "Super admin user may not exist or have wrong account type"
    }
} catch {
    Write-Warning "Could not verify super admin user"
}

# Test 6: Check Shinobi NVR container status
Write-Status "Checking Shinobi NVR container status..."
try {
    $shinobiStatus = docker ps --filter "name=shinobi-nvr" --format "{{.Status}}"
    if ($shinobiStatus -like "*Up*") {
        Write-Success "Shinobi NVR container is running: $shinobiStatus"
    } else {
        Write-Warning "Shinobi NVR container is not running"
    }
} catch {
    Write-Warning "Failed to check Shinobi NVR container status"
}

# Test 7: Test Shinobi web interface accessibility
Write-Status "Testing Shinobi web interface accessibility..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/super" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Success "Shinobi web interface is accessible at http://localhost:8080/super"
    } else {
        Write-Warning "Shinobi web interface returned status code: $($response.StatusCode)"
    }
} catch {
    Write-Warning "Shinobi web interface is not accessible: $($_.Exception.Message)"
}

# Test 8: Show database statistics
Write-Status "Database statistics..."
try {
    $tableCount = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -D shinobi_db -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'shinobi_db';" 2>$null
    $userCount = docker exec shinobi_mariadb_for_shinobi mysql -u root -pa_very_secret_root_password_for_shinobi_db -D shinobi_db -e "SELECT COUNT(*) as user_count FROM Users;" 2>$null
    
    Write-Host "   • Total tables: $($tableCount -replace '\D','')" -ForegroundColor White
    Write-Host "   • Total users: $($userCount -replace '\D','')" -ForegroundColor White
} catch {
    Write-Warning "Could not retrieve database statistics"
}

Write-Host "`n=====================================" -ForegroundColor Cyan
Write-Success "Shinobi Database Verification Complete!"
Write-Host "`n🎯 Access Information:" -ForegroundColor Cyan
Write-Host "   • Shinobi Super Admin: http://localhost:8080/super" -ForegroundColor White
Write-Host "   • Login: <EMAIL>" -ForegroundColor White
Write-Host "   • Password: sU5EjCH63wRMSo048y1tOdvm3B6xGk" -ForegroundColor White
Write-Host "`n📊 Database Details:" -ForegroundColor Cyan
Write-Host "   • Database: shinobi_db" -ForegroundColor White
Write-Host "   • User: shinobi" -ForegroundColor White
Write-Host "   • Password: shinobi_password" -ForegroundColor White
Write-Host "   • Host: shinobi_mariadb_for_shinobi" -ForegroundColor White
Write-Host "`n✨ The Shinobi database is properly initialized and ready!" -ForegroundColor Green
