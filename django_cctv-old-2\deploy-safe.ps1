# CCTV Warehouse Monitoring System - SAFE Deployment Script
# Preserves existing OpenVPN data and configurations

param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Show-Help {
    Write-Host "🎖️  CCTV Warehouse Monitoring System - SAFE Deployment" -ForegroundColor $Green
    Write-Host "=================================================================="
    Write-Host "🛡️  This script preserves existing OpenVPN data and configurations"
    Write-Host ""
    Write-Host "📋 Available commands:"
    Write-Host ""
    Write-Host "  .\deploy-safe.ps1 status    - Check current system status"
    Write-Host "  .\deploy-safe.ps1 backup    - Backup existing configurations"
    Write-Host "  .\deploy-safe.ps1 deploy    - Safe deployment (preserves data)"
    Write-Host "  .\deploy-safe.ps1 test      - Test system functionality"
    Write-Host "  .\deploy-safe.ps1 logs      - Show logs from all services"
    Write-Host ""
    Write-Host "🚀 Recommended: .\deploy-safe.ps1 deploy" -ForegroundColor $Green
    Write-Host "=================================================================="
}

function Check-ExistingData {
    Write-Status "🔍 Checking existing OpenVPN data..."
    
    $dataLocations = @(
        ".\openvpn_data",
        ".\django_web\openvpn_data",
        ".\openvpn_data-2"
    )
    
    foreach ($location in $dataLocations) {
        if (Test-Path $location) {
            Write-Warning "📁 Found OpenVPN data at: $location"
            
            # Check for important files
            $importantFiles = @(
                "$location\pki\ca.crt",
                "$location\android_tablet.ovpn",
                "$location\client_registry.json"
            )
            
            foreach ($file in $importantFiles) {
                if (Test-Path $file) {
                    Write-Success "  ✅ $file"
                } else {
                    Write-Status "  ❌ $file (missing)"
                }
            }
            
            # Check for VPN clients
            $clientFiles = Get-ChildItem "$location\*.ovpn" -ErrorAction SilentlyContinue
            if ($clientFiles) {
                Write-Success "  📋 VPN Clients found: $($clientFiles.Count)"
                foreach ($client in $clientFiles) {
                    Write-Status "    - $($client.Name)"
                }
            }
        }
    }
}

function Backup-Configurations {
    Write-Status "💾 Creating backup of existing configurations..."
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupDir = ".\backup_$timestamp"
    
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    # Backup OpenVPN data
    if (Test-Path ".\openvpn_data") {
        Copy-Item -Path ".\openvpn_data" -Destination "$backupDir\openvpn_data" -Recurse -Force
        Write-Success "✅ Backed up main OpenVPN data"
    }
    
    # Backup Django OpenVPN data
    if (Test-Path ".\django_web\openvpn_data") {
        Copy-Item -Path ".\django_web\openvpn_data" -Destination "$backupDir\django_web_openvpn_data" -Recurse -Force
        Write-Success "✅ Backed up Django OpenVPN data"
    }
    
    # Backup Shinobi config
    if (Test-Path ".\shinobi_config") {
        Copy-Item -Path ".\shinobi_config" -Destination "$backupDir\shinobi_config" -Recurse -Force
        Write-Success "✅ Backed up Shinobi configuration"
    }
    
    # Backup environment files
    $envFiles = @(
        ".\django_web\.env",
        ".\shinobi_cctv_django\.env"
    )
    
    foreach ($envFile in $envFiles) {
        if (Test-Path $envFile) {
            Copy-Item -Path $envFile -Destination $backupDir -Force
            Write-Success "✅ Backed up $envFile"
        }
    }
    
    Write-Success "💾 Backup completed: $backupDir"
    return $backupDir
}

function Deploy-Safe {
    Write-Status "🛡️ Starting SAFE deployment..."
    Write-Status "This deployment will preserve all existing OpenVPN data and configurations"
    
    # Check existing data first
    Check-ExistingData
    
    # Create backup
    $backupDir = Backup-Configurations
    
    Write-Status "🚀 Deploying with existing data preservation..."
    
    # Deploy using existing docker-compose (which already uses ./openvpn_data)
    Write-Status "📦 Building and starting services..."
    docker-compose up -d --build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Status "⏳ Waiting for services to initialize..."
        Start-Sleep -Seconds 30
        
        Write-Status "🔍 Checking service status..."
        docker-compose ps
        
        Write-Success ""
        Write-Success "🎉 SAFE deployment complete!"
        Write-Success "=================================================================="
        Write-Success "🛡️ Your existing data has been preserved:"
        Write-Success "  - OpenVPN certificates and keys: PRESERVED"
        Write-Success "  - VPN client configurations: PRESERVED"
        Write-Success "  - Client registry: PRESERVED"
        Write-Success "  - Shinobi configuration: PRESERVED"
        Write-Success ""
        Write-Success "💾 Backup created at: $backupDir"
        Write-Success ""
        Write-Success "🌐 Access points:"
        Write-Success "  - Django Web:     http://localhost:8000"
        Write-Success "  - Shinobi CCTV:   http://localhost:5000"
        Write-Success "  - Shinobi NVR:    http://localhost:8080"
        Write-Success "  - pgAdmin:        http://localhost:5050"
        Write-Success "=================================================================="
    } else {
        Write-Error "❌ Deployment failed! Your data is safe in backup: $backupDir"
        Write-Error "Check logs with: docker-compose logs"
    }
}

function Show-Status {
    Write-Status "📊 Current System Status:"
    
    # Check if containers are running
    $containers = docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
    Write-Host $containers
    
    Write-Status ""
    Write-Status "🔍 OpenVPN Data Status:"
    Check-ExistingData
    
    Write-Status ""
    Write-Status "🌐 Service Health Check:"
    
    $services = @{
        "Django Web" = "http://localhost:8000"
        "Shinobi CCTV" = "http://localhost:5000"
        "Shinobi NVR" = "http://localhost:8080"
        "pgAdmin" = "http://localhost:5050"
    }
    
    foreach ($service in $services.GetEnumerator()) {
        try {
            $response = Invoke-WebRequest -Uri $service.Value -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "✅ $($service.Key): OK"
            } else {
                Write-Warning "⚠️ $($service.Key): HTTP $($response.StatusCode)"
            }
        } catch {
            Write-Error "❌ $($service.Key): DOWN"
        }
    }
}

function Test-System {
    Write-Status "🧪 Testing CCTV Warehouse Monitoring System..."
    
    # Test VPN client creation capability
    Write-Status "🔐 Testing VPN client registry..."
    if (Test-Path ".\openvpn_data\client_registry.json") {
        $registry = Get-Content ".\openvpn_data\client_registry.json" | ConvertFrom-Json
        $clientCount = $registry.clients.Count
        Write-Success "✅ VPN Client Registry: $clientCount clients registered"
    } else {
        Write-Warning "⚠️ VPN Client Registry: Not found"
    }
    
    # Test service endpoints
    $services = @{
        "Django Web" = "http://localhost:8000"
        "Shinobi CCTV" = "http://localhost:5000"
        "Shinobi NVR" = "http://localhost:8080"
        "pgAdmin" = "http://localhost:5050"
    }
    
    foreach ($service in $services.GetEnumerator()) {
        try {
            $response = Invoke-WebRequest -Uri $service.Value -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "✅ $($service.Key): OK"
            } else {
                Write-Warning "⚠️ $($service.Key): HTTP $($response.StatusCode)"
            }
        } catch {
            Write-Error "❌ $($service.Key): FAIL"
        }
    }
    
    Write-Success "🎉 System test complete!"
}

function Show-Logs {
    Write-Status "📋 Showing logs from all services..."
    docker-compose logs -f
}

# Main command dispatcher
switch ($Command.ToLower()) {
    "help" { Show-Help }
    "status" { Show-Status }
    "backup" { Backup-Configurations }
    "deploy" { Deploy-Safe }
    "test" { Test-System }
    "logs" { Show-Logs }
    default { 
        Write-Error "Unknown command: $Command"
        Show-Help 
    }
}
