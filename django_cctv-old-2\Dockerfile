# Use official Python image
FROM python:3.12-slim

# Set env vars
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set working directory
WORKDIR /app

RUN apt-get update \
    && apt-get install -y easy-rsa \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install dependencies
COPY requirements.txt /app/
RUN pip install --upgrade pip && pip install -r requirements.txt

# Copy project files
COPY . /app/

# Give execute permissions to entrypoint
RUN chmod +x /app/entrypoint.sh

# Run server using entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]
