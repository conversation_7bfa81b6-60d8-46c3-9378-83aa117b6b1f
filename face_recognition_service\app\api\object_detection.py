"""
🎖️ OBJECT DETECTION API ENDPOINTS - COCO DATASET
🎯 OPERATION: YOLO OBJECT DETECTION INTEGRATION
⚔️ TACTICAL OBJECT DETECTION SERVICES

🚀 FUTURE SERVICE: This module is ready for deployment as a separate
   Django service similar to shinobi_cctv_django on a different port.
   Currently NOT integrated with the face recognition service.

📋 DEPLOYMENT PLAN:
   - Create new Django service: object_detection_django
   - Port: 6000 (or similar)
   - Integrate with camera streams
   - Detect 80+ COCO objects in real-time
   - Separate UI and management interface
"""

import logging
import time
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
import cv2
import numpy as np
from PIL import Image
import io

from app.core.cache import get_cache, CacheManager

logger = logging.getLogger(__name__)

# 🎯 Create object detection router
router = APIRouter(prefix="/object-detection", tags=["Object Detection"])

class YOLOObjectDetector:
    """🎯 YOLO Object Detection Engine - COCO Dataset"""
    
    def __init__(self):
        self.model = None
        self.device = None
        self.is_loaded = False
        self.coco_classes = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck', 'boat',
            'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench', 'bird', 'cat',
            'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe', 'backpack',
            'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee', 'skis', 'snowboard', 'sports ball',
            'kite', 'baseball bat', 'baseball glove', 'skateboard', 'surfboard', 'tennis racket',
            'bottle', 'wine glass', 'cup', 'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple',
            'sandwich', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake',
            'chair', 'couch', 'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
            'mouse', 'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier', 'toothbrush'
        ]
        
    async def load_model(self):
        """🚀 Load YOLO model for object detection"""
        try:
            logger.info("🧠 Loading YOLOv11 Object Detection model...")
            
            from ultralytics import YOLO
            import torch
            
            # Detect GPU availability
            if torch.cuda.is_available():
                self.device = 'cuda'
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"🚀 GPU detected for object detection: {gpu_name}")
            else:
                self.device = 'cpu'
                logger.warning("⚠️ GPU not available for object detection, using CPU")
            
            # Load YOLO model for object detection
            self.model = YOLO('yolo11n.pt')  # COCO-trained model
            self.model.to(self.device)
            
            self.is_loaded = True
            logger.info("✅ YOLO Object Detection model loaded successfully!")
            
        except Exception as e:
            logger.error(f"🚨 Failed to load YOLO object detection model: {e}")
            raise e
    
    async def detect_objects(self, image: np.ndarray, confidence_threshold: float = 0.25) -> List[Dict[str, Any]]:
        """
        🎯 Detect objects in image using YOLO (COCO dataset)
        
        Args:
            image: Input image as numpy array
            confidence_threshold: Detection confidence threshold
            
        Returns:
            List of object detections with bounding boxes and classes
        """
        if not self.is_loaded:
            await self.load_model()
        
        try:
            start_time = time.time()
            
            # Run inference
            results = self.model(image, verbose=False)
            
            # Process results
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract bounding box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0].cpu().numpy())
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # Filter by confidence threshold
                        if confidence >= confidence_threshold:
                            detection = {
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': confidence,
                                'class_id': class_id,
                                'class_name': self.coco_classes[class_id] if class_id < len(self.coco_classes) else 'unknown',
                                'area': (x2 - x1) * (y2 - y1),
                                'center': [(x1 + x2) / 2, (y1 + y2) / 2]
                            }
                            detections.append(detection)
            
            processing_time = (time.time() - start_time) * 1000
            logger.debug(f"🎯 YOLO object detection: {len(detections)} objects in {processing_time:.2f}ms")
            
            return detections
            
        except Exception as e:
            logger.error(f"🚨 Object detection failed: {e}")
            return []

# Global object detector instance
object_detector = YOLOObjectDetector()

@router.post("/detect")
async def detect_objects(
    file: UploadFile = File(...),
    confidence_threshold: float = Form(0.25),
    cache: CacheManager = Depends(get_cache)
):
    """
    🎯 Detect objects in uploaded image using YOLO (COCO dataset)
    
    Args:
        file: Image file to process
        confidence_threshold: Detection confidence threshold
        
    Returns:
        JSON response with object detections
    """
    try:
        start_time = time.time()
        
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read and process image
        image_bytes = await file.read()
        image = Image.open(io.BytesIO(image_bytes))
        image_np = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # Generate image hash for caching
        image_hash = str(hash(image_bytes.hex()))
        
        # Check cache first
        cached_result = await cache.get_recognition_result(f"obj_{image_hash}")
        if cached_result:
            logger.info(f"🚀 Cache hit for object detection: {image_hash[:8]}...")
            return JSONResponse(content=cached_result)
        
        # Detect objects
        detections = await object_detector.detect_objects(image_np, confidence_threshold)
        
        # Group detections by class
        class_counts = {}
        for detection in detections:
            class_name = detection['class_name']
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        # Prepare response
        response_data = {
            "success": True,
            "image_info": {
                "filename": file.filename,
                "size": len(image_bytes),
                "dimensions": [image_np.shape[1], image_np.shape[0]],
                "format": file.content_type
            },
            "detection_info": {
                "model": "YOLOv11",
                "dataset": "COCO",
                "confidence_threshold": confidence_threshold,
                "objects_detected": len(detections),
                "unique_classes": len(class_counts)
            },
            "objects": detections,
            "class_summary": class_counts,
            "processing_time_ms": (time.time() - start_time) * 1000,
            "timestamp": time.time()
        }
        
        # Cache result
        await cache.set_recognition_result(f"obj_{image_hash}", response_data, ttl=300)
        
        logger.info(f"🎯 Detected {len(detections)} objects in {file.filename}")
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"🚨 Object detection failed: {e}")
        raise HTTPException(status_code=500, detail=f"Object detection failed: {str(e)}")

@router.get("/classes")
async def get_coco_classes():
    """
    📋 Get list of COCO dataset classes
    
    Returns:
        JSON response with COCO class names
    """
    return {
        "success": True,
        "dataset": "COCO",
        "total_classes": len(object_detector.coco_classes),
        "classes": object_detector.coco_classes
    }

logger.info("🎯 Object Detection API endpoints loaded")
logger.info("📦 COCO dataset classes ready")
logger.info("🚀 YOLOv11 object detection endpoints active")
