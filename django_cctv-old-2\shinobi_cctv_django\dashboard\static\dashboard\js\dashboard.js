// dashboard/static/dashboard/js/dashboard.js

document.addEventListener('DOMContentLoaded', function() {
    initDashboardStats();
    // initCameraStatusPolling(); // This was in the original, but seems more for a global camera status view
    setupDashboardEventListeners();
});

function initDashboardStats() {
    fetchAndUpdateDashboardStats(); // Initial fetch
    setInterval(fetchAndUpdateDashboardStats, 30000); // Poll every 30 seconds
}

function fetchAndUpdateDashboardStats() {
    const apiUrl = document.body.dataset.dashboardStatsApiUrl; // Get URL from data attribute
    if (!apiUrl) {
        console.error('Dashboard Stats API URL not found on body data attribute (data-dashboard-stats-api-url).');
        // Fallback to hardcoded if necessary, or show an error
        // updateDashboardStatsWithData(mockStats); // For offline testing (remove or comment out in production)
        return;
    }

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Network response was not ok: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            updateDashboardStatsWithData(data);
        })
        .catch(error => {
            console.error('Error fetching dashboard stats:', error);
            showNotification('Failed to update dashboard statistics.', 'danger');
            // Optionally, update with stale or zeroed data, or show error indicators
            // updateDashboardStatsWithData(getZeroedStats()); // Example
        });
}

function updateDashboardStatsWithData(stats) {
    // System stats (IDs from original Flask template)
    const cpuUsageEl = document.getElementById('cpu-usage'); // Text inside progress bar
    const cpuProgressEl = document.getElementById('cpu-progress'); // Progress bar itself
    if (cpuUsageEl && cpuProgressEl && stats.system) {
        cpuUsageEl.textContent = `${stats.system.cpu.toFixed(1)}%`;
        cpuProgressEl.style.width = `${stats.system.cpu}%`;
        cpuProgressEl.setAttribute('aria-valuenow', stats.system.cpu);
    }

    const memoryUsageEl = document.getElementById('memory-usage');
    const memoryProgressEl = document.getElementById('memory-progress');
    if (memoryUsageEl && memoryProgressEl && stats.system) {
        memoryUsageEl.textContent = `${stats.system.memory.toFixed(1)}%`;
        memoryProgressEl.style.width = `${stats.system.memory}%`;
        memoryProgressEl.setAttribute('aria-valuenow', stats.system.memory);
    }

    const storageUsageEl = document.getElementById('storage-usage');
    const storageProgressEl = document.getElementById('storage-progress');
    if (storageUsageEl && storageProgressEl && stats.system) {
        storageUsageEl.textContent = `${stats.system.storage.toFixed(1)}%`;
        storageProgressEl.style.width = `${stats.system.storage}%`;
        storageProgressEl.setAttribute('aria-valuenow', stats.system.storage);
    }
    
    const vpnConnectionsEl = document.getElementById('vpn-connections'); // Main stat card
    if (vpnConnectionsEl && stats.system) {
        vpnConnectionsEl.textContent = stats.system.vpn_connections;
    }
     // For dashboard.html template provided earlier, IDs were:
    // total-cameras-val, online-cameras-val, offline-cameras-val
    // total-locations-val, online-locations-val, offline-locations-val
    // open-incidents-val, vpn-connections-val (already handled above)
    // cpu-usage-val, memory-usage-val, storage-usage-val (already handled above)
    // cpu-progress-bar, memory-progress-bar, storage-progress-bar (already handled above)

    const totalCamerasEl = document.getElementById('total-cameras-val') || document.getElementById('total-cameras');
    if (totalCamerasEl && stats.cameras) totalCamerasEl.textContent = stats.cameras.total;
    
    const onlineCamerasEl = document.getElementById('online-cameras-val') || document.getElementById('online-cameras');
    if (onlineCamerasEl && stats.cameras) onlineCamerasEl.textContent = stats.cameras.online;

    const offlineCamerasEl = document.getElementById('offline-cameras-val') || document.getElementById('offline-cameras');
    if (offlineCamerasEl && stats.cameras) offlineCamerasEl.textContent = stats.cameras.offline;

    const totalLocationsEl = document.getElementById('total-locations-val') || document.getElementById('total-locations');
    if (totalLocationsEl && stats.locations) totalLocationsEl.textContent = stats.locations.total;
    
    const onlineLocationsEl = document.getElementById('online-locations-val') || document.getElementById('online-locations');
    if (onlineLocationsEl && stats.locations) onlineLocationsEl.textContent = stats.locations.online;
    
    const offlineLocationsEl = document.getElementById('offline-locations-val') || document.getElementById('offline-locations');
    if (offlineLocationsEl && stats.locations) offlineLocationsEl.textContent = stats.locations.offline;

    const openIncidentsEl = document.getElementById('open-incidents-val') || document.getElementById('open-incidents');
    if (openIncidentsEl && stats.incidents) openIncidentsEl.textContent = stats.incidents.open;
}


function setupDashboardEventListeners() {
    const refreshButton = document.getElementById('refresh-dashboard');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            fetchAndUpdateDashboardStats();
            setTimeout(() => { // Give some time for the fetch to potentially complete
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            }, 1500); // Adjust delay as needed
        });
    }

    // Example: Event listener for grid toggle if it were on this page
    // const gridToggle = document.getElementById('toggle-grid-size');
    // if (gridToggle) { ... }
}