# Generated by Django 5.2 on 2025-06-01 04:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0003_vpnclient'),
    ]

    operations = [
        migrations.AlterField(
            model_name='vpnclient',
            name='client_name',
            field=models.CharField(max_length=128),
        ),
        migrations.AlterField(
            model_name='vpnclient',
            name='location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dashboard.location'),
        ),
        migrations.AlterField(
            model_name='vpnclient',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
    ]
