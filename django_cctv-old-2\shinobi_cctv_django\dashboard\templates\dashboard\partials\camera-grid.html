{% load static %}
<div class="camera-grid {{ grid_size_class|default:'col-md-4' }}"> {# Default grid size if not provided #}
    {% for camera in cameras %}
    <div class="camera-card mb-4" data-camera-id="{{ camera.id }}" data-location-id="{{ camera.location.id }}" data-status="{{ camera.status|lower }}">
        <div class="camera-feed position-relative">
            {# Removed camera-title-overlay as requested #}
            <div class="ratio ratio-16x9 bg-dark">
                 {% if config.SHINOBI_URL and camera.shinobi_group_key and camera.shinobi_id and camera.shinobi_api_key %}
                    <iframe 
                        src="{{ config.SHINOBI_URL }}/embed/{{ camera.shinobi_group_key }}/{{ camera.shinobi_id }}/{{ camera.shinobi_api_key }}? получаем={{ camera.id }}"
                        frameborder="0"
                        allowfullscreen
                        title="Live feed for {{ camera.name }}"
                        loading="lazy"
                    ></iframe>
                {% else %}
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <img src="{% static 'dashboard/img/placeholder_no_feed.png' %}" alt="Video feed unavailable" class="img-fluid" style="max-height: 100%;">
                    </div>
                {% endif %}
            </div>
            
            <span class="camera-status-badge position-absolute top-0 start-0 m-2 badge bg-{{ camera.status|lower|slice:':3'|lower }}{% if camera.status == 'online' %}success{% elif camera.status == 'offline' %}danger{% else %}warning{% endif %}" data-status-indicator-grid="{{ camera.id }}">
                {{ camera.status|capfirst }}
            </span>
        </div>
        
        <div class="card-body p-2">
            <h5 class="card-title fs-6 mb-1">{{ camera.name }}</h5>
            <p class="card-text small text-muted mb-2">{{ camera.location.name }}</p>
            
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge rounded-pill bg-{% if camera.status == 'online' %}success{% elif camera.status == 'offline' %}danger{% else %}secondary{% endif %}">
                    {{ camera.status|capfirst }}
                </span>
                <span class="badge rounded-pill {% if camera.is_recording %}bg-danger{% else %}bg-secondary{% endif %}" 
                      data-recording-indicator-grid="{{ camera.id }}">
                    <i class="bi {% if camera.is_recording %}bi-record-fill{% else %}bi-record{% endif %}"></i>
                    {% if camera.is_recording %}REC{% else %}Ready{% endif %}
                </span>
            </div>
            
            <div class="d-grid gap-1">
                <a href="{% url 'dashboard:camera_detail' camera_id=camera.id %}" class="btn btn-sm btn-primary">
                    <i class="bi bi-eye"></i> View Detail
                </a>
                
                {% if request.user.is_authenticated and (request.user.is_superuser or request.user.role.name in "Administrator,Warehouse Manager,Security Operator") %}
                <div class="btn-group w-100">
                    <button type="button" class="btn btn-sm btn-success recording-control-grid w-50 {% if camera.is_recording %}d-none{% endif %}" 
                            data-camera-id="{{ camera.id }}" data-action="start">
                        <i class="bi bi-record-fill"></i> Rec
                    </button>
                    <button type="button" class="btn btn-sm btn-danger recording-control-grid w-50 {% if not camera.is_recording %}d-none{% endif %}" 
                            data-camera-id="{{ camera.id }}" data-action="stop">
                        <i class="bi bi-stop-fill"></i> Stop
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12 text-center py-5">
        <h4 class="text-muted">No cameras found</h4>
        {% if request.user.is_authenticated and (request.user.is_superuser or request.user.role.name in "Administrator,Warehouse Manager") %}
         <p>There are no cameras configured that match your current filters or permissions.</p>
        <a href="{% url 'dashboard:camera_add' %}" class="btn btn-primary mt-2">
            <i class="bi bi-plus-lg"></i> Add Camera
        </a>
        {% else %}
        <p>There are no cameras accessible to you at this time.</p>
        {% endif %}
    </div>
    {% endfor %}
</div>