# Manual Docker Cleanup and Rebuild Guide

This guide provides step-by-step instructions to completely clean your Docker environment and rebuild the services from scratch.

## 🧹 Phase 1: Complete Cleanup

### Step 1: Stop All Project Services
```bash
docker-compose down --remove-orphans
```

### Step 2: Remove All Project Containers
```bash
# List all containers related to the project
docker ps -a --filter "name=django_web" --filter "name=shinobi_cctv_django" --filter "name=postgres_db_django" --filter "name=redis_cache" --filter "name=shinobi-nvr" --filter "name=shinobi_mariadb" --filter "name=openvpn" --filter "name=pgadmin"

# Remove them (replace with actual container IDs/names if needed)
docker rm -f django_web shinobi_cctv_django postgres_db_django redis_cache shinobi-nvr shinobi_mariadb_for_shinobi openvpn_server openvpn-ui bolt2_openvpn_admin pgadmin_service_django duckdns
```

### Step 3: Remove Project Images
```bash
# List project images
docker images --filter "reference=django_cctv*" --filter "reference=*django_web*" --filter "reference=*shinobi_cctv_django*"

# Remove project images
docker rmi -f $(docker images --filter "reference=django_cctv*" --filter "reference=*django_web*" --filter "reference=*shinobi_cctv_django*" -q)
```

### Step 4: Remove Project Volumes
```bash
# List project volumes
docker volume ls --filter "name=django_cctv"

# Remove project volumes
docker volume rm $(docker volume ls --filter "name=django_cctv" -q)
```

### Step 5: Remove Project Networks
```bash
# List project networks
docker network ls --filter "name=django_cctv" --filter "name=cctv_net"

# Remove project networks
docker network rm $(docker network ls --filter "name=django_cctv" --filter "name=cctv_net" -q)
```

### Step 6: Clean Up Dangling Resources
```bash
# Remove dangling images
docker image prune -f

# Remove build cache
docker builder prune -f

# Optional: Remove all unused containers, networks, images (BE CAREFUL!)
# docker system prune -a -f
```

### Step 7: Verify Cleanup
```bash
# Check remaining containers
docker ps -a

# Check remaining images
docker images

# Check remaining volumes
docker volume ls

# Check remaining networks
docker network ls
```

## 🚀 Phase 2: Fresh Rebuild

### Step 1: Build and Start Services
```bash
# Build and start all services from scratch
docker-compose up -d --build --force-recreate
```

### Step 2: Wait for Services to Initialize
```bash
# Wait for services to start (about 60 seconds)
# You can monitor the logs:
docker-compose logs -f
```

### Step 3: Check Database Connectivity
```bash
# Test if PostgreSQL is ready
docker-compose exec db pg_isready -U user -d warehouse_shinobi

# If not ready, wait and try again
# You can also check database logs:
docker-compose logs db
```

### Step 4: Run Database Migrations
```bash
# Run migrations for django_web
docker-compose exec web python manage.py migrate

# Run migrations for shinobi_cctv_django
docker-compose exec shinobi_cctv_django python manage.py migrate
```

### Step 5: Collect Static Files
```bash
# Collect static files for django_web
docker-compose exec web python manage.py collectstatic --noinput

# Collect static files for shinobi_cctv_django
docker-compose exec shinobi_cctv_django python manage.py collectstatic --noinput --clear
```

### Step 6: Create Superusers (Optional)
```bash
# Create superuser for django_web
docker-compose exec web python manage.py createsuperuser

# Create superuser for shinobi_cctv_django
docker-compose exec shinobi_cctv_django python manage.py createsuperuser
```

## 🔍 Phase 3: Verification

### Step 1: Check Service Status
```bash
# Check all services are running
docker-compose ps

# Check service logs if needed
docker-compose logs web
docker-compose logs shinobi_cctv_django
docker-compose logs db
```

### Step 2: Test Service Accessibility
```bash
# Test django_web (should return HTML)
curl -I http://localhost:8000

# Test shinobi_cctv_django (should return HTML)
curl -I http://localhost:5000

# Test pgAdmin (should return HTML)
curl -I http://localhost:5050
```

### Step 3: Run Verification Script
```bash
# Run the verification script
python verify_setup.py
```

### Step 4: Test Database Connection
```bash
# Test database connection from django_web
docker-compose exec web python -c "
import django
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cctv_project.settings')
django.setup()
from django.db import connection
cursor = connection.cursor()
cursor.execute('SELECT 1')
print('django_web database connection: SUCCESS')
"

# Test database connection from shinobi_cctv_django
docker-compose exec shinobi_cctv_django python -c "
import django
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shinobi_cctv_django.settings')
django.setup()
from django.db import connection
cursor = connection.cursor()
cursor.execute('SELECT 1')
print('shinobi_cctv_django database connection: SUCCESS')
"
```

## 🎯 Expected Results

After successful completion, you should have:

- ✅ **django_web** accessible at http://localhost:8000
- ✅ **shinobi_cctv_django** accessible at http://localhost:5000
- ✅ **pgAdmin** accessible at http://localhost:5050
- ✅ Both services using the same PostgreSQL database
- ✅ Separate session management (different cookie names)
- ✅ All migrations applied successfully
- ✅ Static files collected for both services

## 🚨 Troubleshooting

### If Services Don't Start:
```bash
# Check logs for errors
docker-compose logs

# Check specific service logs
docker-compose logs web
docker-compose logs shinobi_cctv_django
docker-compose logs db
```

### If Database Connection Fails:
```bash
# Check if database container is running
docker-compose ps db

# Check database logs
docker-compose logs db

# Try restarting database
docker-compose restart db
```

### If Migrations Fail:
```bash
# Check migration status
docker-compose exec web python manage.py showmigrations
docker-compose exec shinobi_cctv_django python manage.py showmigrations

# Try fake initial migrations if tables exist
docker-compose exec web python manage.py migrate --fake-initial
docker-compose exec shinobi_cctv_django python manage.py migrate --fake-initial
```

### If Ports Are Already in Use:
```bash
# Check what's using the ports
netstat -ano | findstr :8000
netstat -ano | findstr :5000

# Stop conflicting services or change ports in docker-compose.yml
```

## 📝 Notes

- The cleanup process will remove ALL data in the project volumes
- Make sure to backup any important data before running the cleanup
- The rebuild process may take 10-15 minutes depending on your internet connection
- Both services will share the same database but have isolated sessions
