"""
URL configuration for shinobi_cctv_django project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings # For static files in development
from django.conf.urls.static import static # For static files in development

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('dashboard.urls')), # Include URLs from our dashboard app
]

# Serve static files during development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    # Note: STATIC_ROOT is typically used for `collectstatic`. For local dev,
    # Django's staticfiles app usually finds static files in app's 'static' dirs
    # and in STATICFILES_DIRS. If you are not using `collectstatic` for dev,
    # this specific line might not be necessary if your static files are served correctly.
    # However, it's a common pattern.
