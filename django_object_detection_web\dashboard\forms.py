# =============================================================================
# 🎖️ SHINOBI CCTV DJANGO FORMS - USING SHARED MODELS IMPLEMENTATION
# =============================================================================

from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from .models import (
    CustomUser, Role, Camera, CameraGroup, Location, Incident,
    ObjectDetectionConfig, ObjectDetectionEvent, ObjectDetectionStatistics
)

class LoginForm(forms.Form):
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter username', 'id': 'username'}),
        required=True
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Enter password', 'id': 'password'}),
        required=True
    )
    remember = forms.BooleanField( # Renamed from remember_me to match login.html
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input', 'id': 'remember'}),
        label="Remember me"
    )


class CustomUserCreationForm(UserCreationForm):
    role = forms.ModelChoiceField(
        queryset=Role.objects.all(),
        required=False, # Make it optional or set a default
        widget=forms.Select(attrs={'class': 'form-select', 'id': 'role_id'})
    )
    accessible_locations = forms.ModelMultipleChoiceField(
        queryset=Location.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '5', 'id': 'locations'}),
        required=False
    )

    # CRITICAL: Add camera groups field for access control
    camera_groups = forms.ModelMultipleChoiceField(
        queryset=CameraGroup.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '4', 'id': 'camera_groups'}),
        required=False,
        label="Camera Groups",
        help_text="Select which camera groups this user can access"
    )

    class Meta(UserCreationForm.Meta):
        model = CustomUser
        fields = UserCreationForm.Meta.fields + ('email', 'first_name', 'last_name', 'role', 'is_active', 'accessible_locations', 'camera_groups')
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'first_name'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'last_name'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'id': 'email'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input', 'id': 'is_active'}),
        }

    def save(self, commit=True):
        user = super().save(commit=False)
        # Password is set by UserCreationForm
        if commit:
            user.save()
            # ManyToMany fields need to be saved after the instance is saved
            if self.cleaned_data.get('accessible_locations'):
                user.accessible_locations.set(self.cleaned_data['accessible_locations'])
            if self.cleaned_data.get('camera_groups'):
                user.camera_groups.set(self.cleaned_data['camera_groups'])
        return user


class CustomUserChangeForm(UserChangeForm):
    role = forms.ModelChoiceField(
        queryset=Role.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select', 'id': 'role_id'})
    )
    accessible_locations = forms.ModelMultipleChoiceField(
        queryset=Location.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '5', 'id': 'locations'}),
        required=False
    )

    # CRITICAL: Add camera groups field for access control
    camera_groups = forms.ModelMultipleChoiceField(
        queryset=CameraGroup.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '4', 'id': 'camera_groups'}),
        required=False,
        label="Camera Groups",
        help_text="Select which camera groups this user can access"
    )

    # Remove password from here, handle separately if needed
    password = None

    class Meta(UserChangeForm.Meta):
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name', 'role', 'is_active', 'accessible_locations', 'camera_groups')
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'first_name'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'last_name'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'id': 'email'}),
            'username': forms.TextInput(attrs={'class': 'form-control', 'id': 'username'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input', 'id': 'is_active'}),
        }

    def save(self, commit=True):
        user = super().save(commit=False)
        if commit:
            user.save()
            if self.cleaned_data.get('accessible_locations'):
                 user.accessible_locations.set(self.cleaned_data['accessible_locations'])
            else: # If nothing selected, clear existing relations
                user.accessible_locations.clear()

            if self.cleaned_data.get('camera_groups'):
                user.camera_groups.set(self.cleaned_data['camera_groups'])
            else: # If nothing selected, clear existing relations
                user.camera_groups.clear()

        return user


class LocationForm(forms.ModelForm):
    class Meta:
        model = Location
        fields = ['name', 'address', 'city', 'state', 'country', 'zipcode']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter location name', 'id': 'name'}),
            'address': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter street address', 'id': 'address'}),
            'city': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter city', 'id': 'city'}),
            'state': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter state or province', 'id': 'state'}),
            'country': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter country', 'id': 'country'}),
            'zipcode': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter ZIP/postal code', 'id': 'zipcode'}),
        }

class CameraForm(forms.ModelForm):
    location = forms.ModelChoiceField(
        queryset=Location.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Location",
        required=False
    )

    # CRITICAL: Add camera groups field for access control
    groups = forms.ModelMultipleChoiceField(
        queryset=CameraGroup.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '4'}),
        label="Camera Groups",
        required=False,
        help_text="Select which camera groups can access this camera"
    )

    class Meta:
        model = Camera
        fields = ['name', 'rtsp_url', 'shinobi_id', 'shinobi_api_key', 'shinobi_group_key', 'location_name', 'is_ptz', 'status', 'groups']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'rtsp_url': forms.URLInput(attrs={'class': 'form-control'}),
            'shinobi_id': forms.TextInput(attrs={'class': 'form-control'}),
            'shinobi_api_key': forms.TextInput(attrs={'class': 'form-control'}),
            'shinobi_group_key': forms.TextInput(attrs={'class': 'form-control'}),
            'location_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter location name'}),
            'is_ptz': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

    def save(self, commit=True):
        camera = super().save(commit=False)
        # If a location is selected, use its name for location_name
        if self.cleaned_data.get('location'):
            camera.location_name = self.cleaned_data['location'].name
        if commit:
            camera.save()
            # Save many-to-many relationships after the instance is saved
            if self.cleaned_data.get('groups'):
                camera.groups.set(self.cleaned_data['groups'])
            else:
                camera.groups.clear()
        return camera

class IncidentForm(forms.ModelForm):
    location = forms.ModelChoiceField(
        queryset=Location.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Location",
        required=True
    )
    camera = forms.ModelChoiceField(
        queryset=Camera.objects.all(), # Consider filtering by selected location using JS or separate step
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Camera",
        required=False
    )
    class Meta:
        model = Incident
        fields = ['title', 'description', 'camera', 'location', 'severity', 'status']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'severity': forms.Select(attrs={'class': 'form-select'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }


# =============================================================================
# 🎯 OBJECT DETECTION FORMS
# =============================================================================

class ObjectDetectionConfigForm(forms.ModelForm):
    """Form for configuring object detection settings per camera"""

    # Object type choices for enabled_objects field
    OBJECT_CHOICES = [
        ('person', 'Person'),
        ('car', 'Car'),
        ('bus', 'Bus'),
        ('truck', 'Truck'),
        ('bicycle', 'Bicycle'),
        ('motorcycle', 'Motorcycle'),
        ('train', 'Train'),
        ('boat', 'Boat'),
    ]

    enabled_objects = forms.MultipleChoiceField(
        choices=OBJECT_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
        required=False,
        label="Enabled Object Types",
        help_text="Select which object types to detect"
    )

    # Detection configuration fields
    confidence_threshold = forms.FloatField(
        min_value=0.1,
        max_value=1.0,
        initial=0.5,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.1'}),
        label="Confidence Threshold",
        help_text="Minimum confidence score for detections (0.1 - 1.0)"
    )

    # Speed detection fields
    speed_limit_kmh = forms.IntegerField(
        min_value=10,
        max_value=200,
        initial=50,
        widget=forms.NumberInput(attrs={'class': 'form-control'}),
        label="Speed Limit (km/h)",
        help_text="Speed limit for violation detection"
    )

    distance_meters = forms.FloatField(
        min_value=5.0,
        max_value=100.0,
        initial=30.0,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.5'}),
        label="Distance (meters)",
        help_text="Distance between detection lines for speed calculation"
    )

    class Meta:
        model = ObjectDetectionConfig
        fields = ['camera', 'detection_mode', 'is_active']
        widgets = {
            'camera': forms.Select(attrs={'class': 'form-select'}),
            'detection_mode': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # If editing existing config, populate custom fields
        if self.instance and self.instance.pk:
            detection_config = self.instance.detection_config or {}
            speed_settings = self.instance.speed_settings or {}

            self.fields['confidence_threshold'].initial = detection_config.get('confidence_threshold', 0.5)
            self.fields['speed_limit_kmh'].initial = speed_settings.get('speed_limit_kmh', 50)
            self.fields['distance_meters'].initial = speed_settings.get('distance_meters', 30.0)
            self.fields['enabled_objects'].initial = self.instance.enabled_objects or []

    def save(self, commit=True):
        config = super().save(commit=False)

        # Update detection_config JSON field
        config.detection_config = {
            'confidence_threshold': self.cleaned_data['confidence_threshold'],
            'iou_threshold': 0.45,
            'max_detections': 100,
            'track_objects': True
        }

        # Update speed_settings JSON field
        config.speed_settings = {
            'speed_limit_kmh': self.cleaned_data['speed_limit_kmh'],
            'distance_meters': self.cleaned_data['distance_meters'],
            'enable_speed_alerts': True
        }

        # Update enabled_objects
        config.enabled_objects = self.cleaned_data['enabled_objects']

        if commit:
            config.save()

        return config


class PolygonZoneForm(forms.Form):
    """Form for creating polygon zones for object detection"""

    zone_name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter zone name'}),
        label="Zone Name"
    )

    zone_type = forms.ChoiceField(
        choices=[
            ('horizontal', 'Horizontal Line'),
            ('vertical', 'Vertical Line'),
            ('polygon', 'Polygon Area'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Zone Type"
    )

    coordinates = forms.CharField(
        widget=forms.HiddenInput(),
        label="Coordinates",
        help_text="Polygon coordinates (set by drawing on canvas)"
    )

    direction = forms.ChoiceField(
        choices=[
            ('in', 'In'),
            ('out', 'Out'),
            ('both', 'Both Directions'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Count Direction",
        initial='both'
    )
