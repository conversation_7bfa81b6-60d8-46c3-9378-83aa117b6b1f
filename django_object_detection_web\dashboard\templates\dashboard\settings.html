{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}System Settings - ABC CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">System Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-settings-btn">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
        <button type="button" class="btn btn-sm btn-primary ms-2" id="save-settings-btn">
            <i class="bi bi-save"></i> Save Changes
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-3 mb-4">
        <div class="list-group" id="settings-nav">
            <a href="#general-settings-tab" class="list-group-item list-group-item-action active" data-bs-toggle="list" role="tab" aria-controls="general-settings-tab">
                <i class="bi bi-gear"></i> General Settings
            </a>
            <a href="#shinobi-settings-tab" class="list-group-item list-group-item-action" data-bs-toggle="list" role="tab" aria-controls="shinobi-settings-tab">
                <i class="bi bi-camera"></i> Shinobi Integration
            </a>
            <a href="#vpn-settings-tab" class="list-group-item list-group-item-action" data-bs-toggle="list" role="tab" aria-controls="vpn-settings-tab">
                <i class="bi bi-shield-lock"></i> VPN Configuration
            </a>
            <a href="#notification-settings-tab" class="list-group-item list-group-item-action" data-bs-toggle="list" role="tab" aria-controls="notification-settings-tab">
                <i class="bi bi-bell"></i> Notification Settings
            </a>
            <a href="#storage-settings-tab" class="list-group-item list-group-item-action" data-bs-toggle="list" role="tab" aria-controls="storage-settings-tab">
                <i class="bi bi-hdd"></i> Storage Management
            </a>
            <a href="#system-logs-tab" class="list-group-item list-group-item-action" data-bs-toggle="list" role="tab" aria-controls="system-logs-tab">
                <i class="bi bi-journal-text"></i> System Logs
            </a>
            <a href="#backup-restore-tab" class="list-group-item list-group-item-action" data-bs-toggle="list" role="tab" aria-controls="backup-restore-tab">
                <i class="bi bi-cloud-arrow-up"></i> Backup & Restore
            </a>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="tab-content" id="settings-tab-content">
            <div class="tab-pane fade show active" id="general-settings-tab" role="tabpanel" aria-labelledby="general-settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">General Settings</h5>
                    </div>
                    <div class="card-body">
                        <form> {% csrf_token %} <div class="mb-3">
                                <label for="system-name" class="form-label">System Name</label>
                                <input type="text" class="form-control" id="system-name" value="ABC CCTV System via Django">
                                <div class="form-text">This name appears in the browser title and email notifications</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="default-view" class="form-label">Default Dashboard View</label>
                                <select class="form-select" id="default-view">
                                    <option value="grid">Camera Grid</option>
                                    <option value="list">Camera List</option>
                                    <option value="locations">Locations</option>
                                    <option value="health" selected>System Health</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="timezone" class="form-label">System Timezone</label>
                                <select class="form-select" id="timezone">
                                    <option value="UTC" selected>UTC</option>
                                    <option value="America/New_York">Eastern Time (US & Canada)</option>
                                    <option value="America/Chicago">Central Time (US & Canada)</option>
                                    <option value="America/Denver">Mountain Time (US & Canada)</option>
                                    <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                                    <option value="Europe/London">London</option>
                                    <option value="Europe/Paris">Paris</option>
                                    <option value="Asia/Tokyo">Tokyo</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enable-2fa" checked>
                                    <label class="form-check-label" for="enable-2fa">Enable Two-Factor Authentication</label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="session-timeout-switch" checked>
                                    <label class="form-check-label" for="session-timeout-switch">Enable Session Timeout</label>
                                </div>
                                <div class="input-group mt-2">
                                    <input type="number" class="form-control" id="session-timeout-value" value="30" min="5" max="120">
                                    <span class="input-group-text">minutes</span>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="shinobi-settings-tab" role="tabpanel" aria-labelledby="shinobi-settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Shinobi Integration</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Configure Shinobi API integration settings here.
                        </div>
                        
                        <form>
                             {% csrf_token %}
                            <div class="mb-3">
                                <label for="shinobi-url" class="form-label">Shinobi API URL</label>
                                <input type="url" class="form-control" id="shinobi-url" value="{{ settings.SHINOBI_URL|default:'http://localhost:8080' }}" placeholder="https://shinobi.example.com">
                                <div class="form-text">Ensure this matches your `settings.SHINOBI_URL`.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="shinobi-api-key" class="form-label">Shinobi API Key</label>
                                <input type="password" class="form-control" id="shinobi-api-key" placeholder="Enter Super User API Key from Shinobi">
                                <div class="form-text">This key is used for global Shinobi administration. See `settings.SHINOBI_API_KEY`.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="shinobi-group-key-setting" class="form-label">Default Group Key</label>
                                <input type="password" class="form-control" id="shinobi-group-key-setting" placeholder="Enter Default Group Key from Shinobi">
                                 <div class="form-text">See `settings.SHINOBI_GROUP_KEY`.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="recording-mode" class="form-label">Default Recording Mode</label>
                                <select class="form-select" id="recording-mode">
                                    <option value="always">Always Record</option>
                                    <option value="motion" selected>Record on Motion</option>
                                    <option value="schedule">Record on Schedule</option>
                                    <option value="manual">Manual Recording Only</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="auto-stream-restart" checked>
                                    <label class="form-check-label" for="auto-stream-restart">Auto-restart Failed Streams</label>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="vpn-settings-tab" role="tabpanel" aria-labelledby="vpn-settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">VPN Configuration</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> Changes to VPN settings may temporarily disconnect remote locations.
                        </div>
                        
                        <div class="mb-4">
                            <h6>OpenVPN Server Status</h6>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge bg-success">Running</span>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary me-2" disabled>
                                        <i class="bi bi-arrow-repeat"></i> Restart Server
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" disabled>
                                        <i class="bi bi-stop-circle"></i> Stop Server
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <form>
                             {% csrf_token %}
                            <div class="mb-3">
                                <label for="vpn-port" class="form-label">OpenVPN Port</label>
                                <input type="number" class="form-control" id="vpn-port" value="1194" min="1" max="65535">
                            </div>
                            
                            <div class="mb-3">
                                <label for="vpn-protocol" class="form-label">Protocol</label>
                                <select class="form-select" id="vpn-protocol">
                                    <option value="udp" selected>UDP</option>
                                    <option value="tcp">TCP</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="vpn-cipher" class="form-label">Encryption Cipher</label>
                                <select class="form-select" id="vpn-cipher">
                                    <option value="AES-256-GCM" selected>AES-256-GCM</option>
                                    <option value="AES-128-GCM">AES-128-GCM</option>
                                    <option value="CHACHA20-POLY1305">CHACHA20-POLY1305</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="vpn-subnet" class="form-label">VPN Subnet</label>
                                <input type="text" class="form-control" id="vpn-subnet" value="********/24">
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="vpn-client-to-client" checked>
                                    <label class="form-check-label" for="vpn-client-to-client">Allow Client-to-Client Communication</label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Client Certificate Management</label>
                                <div>
                                    <a href="{% url 'dashboard:download_ovpn_global' %}" class="btn btn-sm btn-outline-success me-2" id="download-vpn-config-btn" type="button">
                                        <i class="bi bi-download"></i> Download VPN Config
                                    </a>
                                    <button class="btn btn-sm btn-outline-primary me-2" id="create-client-btn" type="button">
                                        <i class="bi bi-plus-circle"></i> Create New Client
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" id="export-client-btn" type="button">
                                        <i class="bi bi-download"></i> Export Configuration
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Existing VPN Clients</label>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered align-middle" id="global-vpn-clients-table">
                                        <thead>
                                            <tr>
                                                <th>Client Name</th>
                                                <th>Created</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        {% for client in global_vpn_clients %}
                                            <tr>
                                                <td>{{ client.client_name }}</td>
                                                <td>{{ client.created_at|date:'Y-m-d H:i' }}</td>
                                                <td>{% if client.revoked %}<span class="badge bg-danger">Revoked</span>{% else %}<span class="badge bg-success">Active</span>{% endif %}</td>
                                                <td>
                                                    <a href="{% url 'dashboard:download_vpnclient_ovpn' client_id=client.id %}" class="btn btn-sm btn-outline-success" title="Download .ovpn"><i class="bi bi-download"></i></a>
                                                    {% if not client.revoked %}
                                                    <button class="btn btn-sm btn-outline-danger revoke-client-btn" data-client-id="{{ client.id }}" title="Revoke" type="button"><i class="bi bi-x-circle"></i></button>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% empty %}
                                            <tr><td colspan="4" class="text-center text-muted">No VPN clients found.</td></tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="notification-settings-tab" role="tabpanel" aria-labelledby="notification-settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Notification Settings</h5>
                    </div>
                    <div class="card-body">
                        <form>
                             {% csrf_token %}
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enable-email" checked>
                                    <label class="form-check-label" for="enable-email">Enable Email Notifications</label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="smtp-server" class="form-label">SMTP Server</label>
                                <input type="text" class="form-control" id="smtp-server" placeholder="smtp.example.com">
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="smtp-port" class="form-label">SMTP Port</label>
                                    <input type="number" class="form-control" id="smtp-port" value="587">
                                </div>
                                <div class="col-md-6">
                                    <label for="smtp-security" class="form-label">Security</label>
                                    <select class="form-select" id="smtp-security">
                                        <option value="tls" selected>TLS</option>
                                        <option value="ssl">SSL</option>
                                        <option value="none">None</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="smtp-username" class="form-label">SMTP Username</label>
                                <input type="text" class="form-control" id="smtp-username">
                            </div>
                            
                            <div class="mb-3">
                                <label for="smtp-password" class="form-label">SMTP Password</label>
                                <input type="password" class="form-control" id="smtp-password">
                            </div>
                            
                            <div class="mb-3">
                                <label for="from-email" class="form-label">From Email</label>
                                <input type="email" class="form-control" id="from-email" placeholder="<EMAIL>">
                            </div>
                            
                            <hr>
                            
                            <h6>Notification Events</h6>
                            
                            <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="notify-motion" checked><label class="form-check-label" for="notify-motion">Motion Detection</label></div></div>
                            <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="notify-offline" checked><label class="form-check-label" for="notify-offline">Camera Offline</label></div></div>
                            <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="notify-vpn" checked><label class="form-check-label" for="notify-vpn">VPN Connection Changes</label></div></div>
                            <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="notify-storage"><label class="form-check-label" for="notify-storage">Storage Warnings</label></div></div>
                            <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="notify-login"><label class="form-check-label" for="notify-login">Login Attempts</label></div></div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="storage-settings-tab" role="tabpanel" aria-labelledby="storage-settings-tab">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Storage Management</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6>Storage Usage</h6>
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 45%" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100">1.8 TB</div>
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">1.0 TB</div>
                                <div class="progress-bar bg-info text-dark" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100">1.2 TB Free</div>
                            </div>
                            <div class="d-flex justify-content-between small">
                                <span>Total: 4.0 TB</span>
                                <span>Used: 2.8 TB (70%)</span>
                                <span>Free: 1.2 TB (30%)</span>
                            </div>
                        </div>
                        
                        <form>
                             {% csrf_token %}
                            <div class="mb-3">
                                <label class="form-label">Storage Path</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="/var/lib/shinobi/videos" readonly>
                                    <button class="btn btn-outline-secondary" type="button" disabled><i class="bi bi-folder2-open"></i></button>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="storage-retention" class="form-label">Video Retention Period</label>
                                <div class="input-group"><input type="number" class="form-control" id="storage-retention" value="30" min="1" max="365"><span class="input-group-text">days</span></div>
                                <div class="form-text">Videos older than this will be automatically deleted</div>
                            </div>
                            <div class="mb-3">
                                <label for="storage-threshold" class="form-label">Low Storage Warning Threshold</label>
                                <div class="input-group"><input type="number" class="form-control" id="storage-threshold" value="85" min="50" max="95"><span class="input-group-text">%</span></div>
                            </div>
                            <div class="mb-3"><div class="form-check form-switch"><input class="form-check-input" type="checkbox" id="storage-recycle" checked><label class="form-check-label" for="storage-recycle">Auto Recycle Storage</label></div><div class="form-text">When storage reaches 95% capacity, oldest videos will be deleted</div></div>
                            <div class="mb-3"><div class="form-check form-switch"><input class="form-check-input" type="checkbox" id="cloud-backup-storage"><label class="form-check-label" for="cloud-backup-storage">Enable Cloud Backup</label></div></div>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header"><h5 class="mb-0">Storage Volumes</h5></div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead><tr><th>Volume</th><th>Size</th><th>Used</th><th>Status</th><th>Actions</th></tr></thead>
                                <tbody>
                                    <tr><td>/dev/sda1</td><td>4.0 TB</td><td><div class="progress" style="height: 6px; width: 100px;"><div class="progress-bar bg-success" role="progressbar" style="width: 70%"></div></div><span class="small">70%</span></td><td><span class="badge bg-success">Healthy</span></td><td><button class="btn btn-sm btn-outline-secondary" disabled><i class="bi bi-tools"></i></button></td></tr>
                                    <tr><td>/dev/sdb1</td><td>2.0 TB</td><td><div class="progress" style="height: 6px; width: 100px;"><div class="progress-bar bg-warning" role="progressbar" style="width: 85%"></div></div><span class="small">85%</span></td><td><span class="badge bg-warning">Warning</span></td><td><button class="btn btn-sm btn-outline-secondary" disabled><i class="bi bi-tools"></i></button></td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer"><button class="btn btn-sm btn-outline-primary" disabled><i class="bi bi-plus-circle"></i> Add Storage Volume</button></div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="system-logs-tab" role="tabpanel" aria-labelledby="system-logs-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">System Logs</h5>
                        <div><button class="btn btn-sm btn-outline-secondary me-2" disabled><i class="bi bi-download"></i> Export</button><button class="btn btn-sm btn-outline-danger" disabled><i class="bi bi-trash"></i> Clear</button></div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3"><div class="d-flex"><select class="form-select me-2"><option value="all">All Logs</option><option value="error">Errors</option></select><input type="date" class="form-control me-2"><button class="btn btn-outline-primary" disabled><i class="bi bi-funnel"></i> Filter</button></div></div>
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead><tr><th>Timestamp</th><th>Level</th><th>Source</th><th>Message</th></tr></thead>
                                <tbody>
                                    <tr><td><small>2023-11-15 13:45:22</small></td><td><span class="badge bg-success">INFO</span></td><td>Auth</td><td>Admin logged in</td></tr>
                                    <tr><td><small>2023-11-15 13:42:10</small></td><td><span class="badge bg-warning">WARN</span></td><td>Camera</td><td>Cam "WH Front" offline</td></tr>
                                    <tr><td><small>2023-11-15 13:40:05</small></td><td><span class="badge bg-danger">ERROR</span></td><td>API</td><td>Shinobi connection fail</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <nav aria-label="Log pagination"><ul class="pagination justify-content-center"><li class="page-item disabled"><a class="page-link" href="#">Prev</a></li><li class="page-item active"><a class="page-link" href="#">1</a></li><li class="page-item"><a class="page-link" href="#">Next</a></li></ul></nav>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane fade" id="backup-restore-tab" role="tabpanel" aria-labelledby="backup-restore-tab">
                <div class="card mb-4">
                    <div class="card-header"><h5 class="mb-0">System Backup</h5></div>
                    <div class="card-body">
                        <p>Create a backup of your system configuration.</p>
                        <form> {% csrf_token %}
                        <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="backup-users" checked><label class="form-check-label" for="backup-users">Users & Permissions</label></div></div>
                        <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="backup-locations" checked><label class="form-check-label" for="backup-locations">Locations & VPN</label></div></div>
                        <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="backup-cameras" checked><label class="form-check-label" for="backup-cameras">Camera Configs</label></div></div>
                        <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="backup-system-settings" checked><label class="form-check-label" for="backup-system-settings">System Settings</label></div></div>
                        <div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="backup-encrypt" checked><label class="form-check-label" for="backup-encrypt">Encrypt Backup</label></div></div>
                        <div class="d-grid"><button class="btn btn-primary" disabled><i class="bi bi-download"></i> Create Backup</button></div>
                        </form>
                    </div>
                </div>
                <div class="card mb-4">
                    <div class="card-header"><h5 class="mb-0">Scheduled Backups</h5></div>
                    <div class="card-body">
                        <form> {% csrf_token %}
                        <div class="mb-3"><div class="form-check form-switch"><input class="form-check-input" type="checkbox" id="enable-scheduled" checked><label class="form-check-label" for="enable-scheduled">Enable Scheduled Backups</label></div></div>
                        <div class="mb-3"><label for="backup-frequency" class="form-label">Frequency</label><select class="form-select" id="backup-frequency"><option value="daily">Daily</option><option value="weekly" selected>Weekly</option></select></div>
                        <div class="mb-3"><label for="backup-time" class="form-label">Time</label><input type="time" class="form-control" id="backup-time" value="02:00"></div>
                        <div class="mb-3"><label for="backup-retention-count" class="form-label">Retention</label><div class="input-group"><input type="number" class="form-control" id="backup-retention-count" value="4" min="1"><span class="input-group-text">backups</span></div></div>
                        </form>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header"><h5 class="mb-0">Restore System</h5></div>
                    <div class="card-body">
                        <div class="alert alert-warning"><i class="bi bi-exclamation-triangle"></i> Restoring will overwrite current settings.</div>
                        <form> {% csrf_token %}
                        <div class="mb-3"><label for="backup-file" class="form-label">Backup File</label><input class="form-control" type="file" id="backup-file"></div>
                        <div class="mb-3"><label for="backup-password-restore" class="form-label">Password (if encrypted)</label><input type="password" class="form-control" id="backup-password-restore"></div>
                        <div class="d-grid"><button class="btn btn-danger" disabled><i class="bi bi-upload"></i> Restore System</button></div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="notifications-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;"></div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    function showNotification(message, type = 'success') {
        const notificationsContainer = document.getElementById('notifications-container');
        if (!notificationsContainer) {
            console.warn("Notifications container not found for message:", message);
            return;
        }
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.setAttribute('role', 'alert');
        notification.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>`;
        notificationsContainer.appendChild(notification);
        new bootstrap.Alert(notification); // Initialize Bootstrap alert for dismissal
        setTimeout(() => {
            const alertInstance = bootstrap.Alert.getInstance(notification);
            if (alertInstance) {
                alertInstance.close();
            } else {
                notification.remove(); // Fallback
            }
        }, 5000);
    }

    const saveButton = document.getElementById('save-settings-btn');
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
            // AJAX call to save settings would go here
            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-save"></i> Save Changes';
                showNotification('Settings saved successfully!', 'success');
            }, 1500);
        });
    }
    
    const refreshButton = document.getElementById('refresh-settings-btn');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
             // AJAX call to fetch current settings would go here
            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
                showNotification('Settings refreshed!', 'info');
            }, 1000);
        });
    }

    // Ensure Bootstrap tabs are initialized if not automatically handled by your layout.html
    var triggerTabList = [].slice.call(document.querySelectorAll('#settings-nav a'));
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl);
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });
    
    // --- Global VPN Client Management ---
    const downloadVpnConfigBtn = document.getElementById('download-vpn-config-btn');
    const createClientBtn = document.getElementById('create-client-btn');
    const exportClientBtn = document.getElementById('export-client-btn');

    if (downloadVpnConfigBtn) {
        downloadVpnConfigBtn.addEventListener('click', function(e) {
            // Just follow the link (download)
            // No AJAX needed, handled by anchor href
        });
    }

    if (createClientBtn) {
        createClientBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const clientName = prompt('Enter a name for the new VPN client:');
            if (!clientName) return;
            fetch('/api/vpn-create-client-global/?client_name=' + encodeURIComponent(clientName), {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.client_id) {
                    showNotification('New global VPN client created.', 'success');
                    // Download the new .ovpn file
                    window.location.href = `/vpn-client/${data.client_id}/download_ovpn/`;
                } else {
                    showNotification(data.error || 'Failed to create client.', 'danger');
                }
            })
            .catch(() => showNotification('Error creating client.', 'danger'));
        });
    }

    if (exportClientBtn) {
        exportClientBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // Download all global clients as zip
            window.location.href = '/api/vpn-export-clients-global/';
        });
    }

    // --- VPN Client Revocation ---
    document.querySelectorAll('.revoke-client-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const clientId = this.getAttribute('data-client-id');
            if (!clientId) return;
            if (!confirm('Are you sure you want to revoke this VPN client certificate? This action cannot be undone.')) return;
            fetch('/api/vpn-revoke-client/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: 'client_id=' + encodeURIComponent(clientId)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('VPN client revoked.', 'success');
                    // Optionally, reload the page or update the row
                    window.location.reload();
                } else {
                    showNotification(data.error || 'Failed to revoke client.', 'danger');
                }
            })
            .catch(() => showNotification('Error revoking client.', 'danger'));
        });
    });
    

});
</script>
{% endblock %}