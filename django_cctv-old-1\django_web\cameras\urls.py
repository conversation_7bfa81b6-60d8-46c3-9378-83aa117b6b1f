from django.urls import path
from . import views

app_name = 'cameras'

urlpatterns = [
    # Camera management
    path('', views.camera_list, name='camera_list'),
    path('create/', views.camera_create, name='camera_create'),
    path('<int:camera_id>/', views.camera_detail, name='camera_detail'),
    path('<int:camera_id>/edit/', views.camera_edit, name='camera_edit'),
    path('<int:camera_id>/delete/', views.camera_delete, name='camera_delete'),
    
    # Camera groups
    path('groups/', views.group_list, name='group_list'),
    path('groups/create/', views.group_create, name='group_create'),
    path('groups/<int:group_id>/', views.group_detail, name='group_detail'),
    path('groups/<int:group_id>/edit/', views.group_edit, name='group_edit'),
    path('groups/<int:group_id>/delete/', views.group_delete, name='group_delete'),
    
    # Camera actions and views
    path('grid/', views.camera_grid, name='camera_grid'),
    path('grid/configure/', views.configure_grid, name='configure_grid'),
    path('events/', views.event_list, name='event_list'),
    
    # API endpoints for integration with Shinobi
    path('api/events/webhook/', views.event_webhook, name='event_webhook'),
    path('api/status/update/', views.update_camera_status, name='update_status'),
    path('api/monitors/', views.api_monitors, name='api_monitors'),
    path('api/camera/<int:camera_id>/stream/', views.api_camera_stream, name='api_camera_stream'),
]