FROM python:3.12-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

WORKDIR /app

RUN apt-get update \
    && apt-get install -y easy-rsa \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

    # Copy only requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies with retry mechanism
RUN pip install --upgrade pip && \
    pip install --retries 3 --timeout 60 --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Create necessary directories
RUN mkdir -p /app/static /app/media /app/logs

# Set proper permissions
RUN chmod +x /app/entrypoint.sh /app/healthcheck.sh

# Install curl for health checks
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Expose port 8000 for Django app
EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "cctv_project.wsgi:application"]