#!/bin/bash
# OpenVPN Auto-Initialization Script
# This script automatically sets up OpenVPN PKI and server configuration

set -e

echo "🔐 Starting OpenVPN Auto-Initialization..."

# Configuration variables
OPENVPN_DATA="/etc/openvpn"
PKI_DIR="$OPENVPN_DATA/pki"
SERVER_NAME="abinetalemuvpn.duckdns.org"
EASYRSA_DIR="/usr/share/easy-rsa"

# Check if already initialized (multiple ways to detect)
if [ -f "$OPENVPN_DATA/.initialized" ] || [ -f "$PKI_DIR/ca.crt" ] || [ -f "$OPENVPN_DATA/android_tablet.ovpn" ]; then
    echo "✅ OpenVPN already initialized (found existing certificates/config). Skipping..."
    echo "📁 Existing files detected:"
    [ -f "$PKI_DIR/ca.crt" ] && echo "  - CA Certificate: $PKI_DIR/ca.crt"
    [ -f "$PKI_DIR/ta.key" ] && echo "  - TLS Auth Key: $PKI_DIR/ta.key"
    [ -f "$OPENVPN_DATA/android_tablet.ovpn" ] && echo "  - Client Template: $OPENVPN_DATA/android_tablet.ovpn"
    [ -f "$OPENVPN_DATA/client_registry.json" ] && echo "  - Client Registry: $OPENVPN_DATA/client_registry.json"
    exit 0
fi

echo "🏗️ Initializing OpenVPN PKI..."

# Initialize PKI
cd $EASYRSA_DIR
./easyrsa init-pki

# Build CA (non-interactive)
echo "🔑 Building Certificate Authority..."
EASYRSA_BATCH=1 ./easyrsa build-ca nopass

# Generate server certificate
echo "🖥️ Generating server certificate..."
EASYRSA_BATCH=1 ./easyrsa build-server-full server nopass

# Generate Diffie-Hellman parameters
echo "🔐 Generating Diffie-Hellman parameters..."
./easyrsa gen-dh

# Generate TLS auth key
echo "🔒 Generating TLS auth key..."
openvpn --genkey --secret $PKI_DIR/ta.key

# Generate CRL
echo "📋 Generating Certificate Revocation List..."
EASYRSA_BATCH=1 ./easyrsa gen-crl

# Create server configuration
echo "⚙️ Creating server configuration..."
cat > $OPENVPN_DATA/openvpn.conf << EOF
# OpenVPN Server Configuration
# Auto-generated by init script

port 1194
proto udp
dev tun

# SSL/TLS root certificate (ca), certificate
# (cert), and private key (key)
ca $PKI_DIR/ca.crt
cert $PKI_DIR/issued/server.crt
key $PKI_DIR/private/server.key

# Diffie hellman parameters
dh $PKI_DIR/dh.pem

# Network topology
topology subnet

# Configure server mode and supply a VPN subnet
server ******** *************

# Maintain a record of client <-> virtual IP address
ifconfig-pool-persist /tmp/ipp.txt

# Configure server mode for ethernet bridging
;server-bridge ******** ************* ********* **********

# Push routes to the client to allow it
push "redirect-gateway def1 bypass-dhcp"

# Provide DNS servers to the client
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Allow different clients to be able to "see" each other
client-to-client

# The keepalive directive causes ping-like
keepalive 10 120

# TLS auth for additional security
tls-auth $PKI_DIR/ta.key 0

# Select a cryptographic cipher
cipher AES-256-GCM
auth SHA512

# Enable compression on the VPN link
;comp-lzo

# The maximum number of concurrently connected clients
max-clients 100

# Run with reduced privileges
user nobody
group nogroup

# The persist options will try to avoid
persist-key
persist-tun

# Output a short status file
status /tmp/openvpn-status.log

# Log verbosity
verb 3

# Silence repeating messages
;mute 20

# CRL verification
crl-verify $PKI_DIR/crl.pem
EOF

# Create client template
echo "📄 Creating client template..."
cat > $OPENVPN_DATA/android_tablet.ovpn << EOF
client
nobind
dev tun
remote-cert-tls server

remote $SERVER_NAME 1194 udp

<key>
$(cat $PKI_DIR/private/server.key)
</key>
<cert>
$(cat $PKI_DIR/issued/server.crt)
</cert>
<ca>
$(cat $PKI_DIR/ca.crt)
</ca>
key-direction 1
<tls-auth>
$(cat $PKI_DIR/ta.key)
</tls-auth>

redirect-gateway def1
tls-cipher TLS-ECDHE-ECDSA-WITH-AES-128-GCM-SHA256
cipher AES-256-GCM
auth SHA512
EOF

# Set permissions
chmod 600 $PKI_DIR/private/*
chmod 644 $PKI_DIR/ca.crt
chmod 644 $PKI_DIR/issued/*
chmod 644 $PKI_DIR/ta.key

# Mark as initialized
touch $OPENVPN_DATA/.initialized

echo "✅ OpenVPN initialization complete!"
echo "🔐 Server certificate: $PKI_DIR/issued/server.crt"
echo "🔑 CA certificate: $PKI_DIR/ca.crt"
echo "🔒 TLS auth key: $PKI_DIR/ta.key"
echo "⚙️ Server config: $OPENVPN_DATA/openvpn.conf"
