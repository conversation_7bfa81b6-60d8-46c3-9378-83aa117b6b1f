-- Create API key for the current environment variables
USE shinobi_db;

-- First, let's check if the group key exists
SELECT ke, uid, mail FROM Users WHERE ke = 'we0NYsYeTk';

-- If the group key doesn't exist, we need to create a user for it
-- Let's create a user with the group key that's currently being used
INSERT INTO Users (ke, uid, auth, mail, pass, accountType, details) VALUES 
('we0NYsYeTk', 'django_api_user', 'we0NYsYeTk', '<EMAIL>', 'django_password', 0, '{"permissions":{"watch_stream":true,"watch_snapshot":true,"watch_videos":true,"control_monitors":true}}')
ON DUPLICATE KEY UPDATE 
mail = '<EMAIL>',
details = '{"permissions":{"watch_stream":true,"watch_snapshot":true,"watch_videos":true,"control_monitors":true}}';

-- Create the API key for this user
INSERT INTO API (ke, uid, details) VALUES 
('r7VGCq3QXVr1gj6SEsG5XSVkNQkk5w', 'django_api_user', '{"auth_socket":"1","get_monitors":"1","control_monitors":"1","get_logs":"1","watch_stream":"1","watch_snapshot":"1","watch_videos":"1","delete_videos":"1"}')
ON DUPLICATE KEY UPDATE 
details = '{"auth_socket":"1","get_monitors":"1","control_monitors":"1","get_logs":"1","watch_stream":"1","watch_snapshot":"1","watch_videos":"1","delete_videos":"1"}';

-- Verify the setup
SELECT 'Users:' as table_name, ke, uid, mail FROM Users WHERE ke = 'we0NYsYeTk'
UNION ALL
SELECT 'API:' as table_name, ke, uid, details FROM API WHERE ke = 'r7VGCq3QXVr1gj6SEsG5XSVkNQkk5w';
