{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}Incidents - ABC CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Incidents</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'dashboard:incident_add' %}" class="btn btn-sm btn-primary">
            <i class="bi bi-plus-circle"></i> Report Incident
        </a>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover align-middle">
        <thead>
            <tr>
                <th>Title</th>
                <th>Location</th>
                <th>Camera</th>
                <th>Severity</th>
                <th>Status</th>
                <th>Reporter</th>
                <th>Created</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for incident in incidents %}
            <tr>
                <td>{{ incident.title }}</td>
                <td>{{ incident.location.name }}</td>
                <td>{% if incident.camera %}{{ incident.camera.name }}{% else %}-{% endif %}</td>
                <td><span class="badge bg-{{ incident.severity|default:'secondary' }}">{{ incident.get_severity_display }}</span></td>
                <td>
                    {% if incident.status == 'open' %}
                    <span class="badge bg-success">{{ incident.get_status_display }}</span>
                    {% else %}
                    <span class="badge bg-secondary">{{ incident.get_status_display }}</span>
                    {% endif %}
                </td>
                <td>{% if incident.reporter %}{{ incident.reporter.username }}{% else %}-{% endif %}</td>
                <td>{{ incident.created_at|date:'Y-m-d H:i' }}</td>
                <td>
                    <a href="{% url 'dashboard:incident_detail' incident_id=incident.id %}" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></a>
                    {% if request.user == incident.reporter or request.user.is_superuser or request.user.role and request.user.role.name == 'Administrator' %}
                    <a href="{% url 'dashboard:incident_edit' incident_id=incident.id %}" class="btn btn-sm btn-outline-secondary"><i class="bi bi-pencil"></i></a>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr><td colspan="8" class="text-center text-muted">No incidents found.</td></tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
