from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from .models import User
from .forms import UserCreationForm, UserChangeForm, ProfileUpdateForm

def is_admin(user):
    """Check if user is an admin."""
    return user.is_admin

@login_required
def profile(request):
    """View for users to see their own profile."""
    return render(request, 'users/profile.html')

@login_required
def profile_edit(request):
    """View for users to edit their own profile."""
    if request.method == 'POST':
        form = ProfileUpdateForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Your profile has been updated!')
            return redirect('users:profile')
    else:
        form = ProfileUpdateForm(instance=request.user)
    
    return render(request, 'users/profile_edit.html', {'form': form})

@login_required
@user_passes_test(is_admin)
def user_list(request):
    """View for admins to list all users."""
    users = User.objects.all()
    return render(request, 'users/user_list.html', {'users': users})

@login_required
@user_passes_test(is_admin)
def user_create(request):
    """View for admins to create a new user."""
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'User created successfully!')
            return redirect('users:user_list')
    else:
        form = UserCreationForm()
    
    return render(request, 'users/user_form.html', {
        'form': form,
        'title': 'Create User'
    })

@login_required
@user_passes_test(is_admin)
def user_edit(request, user_id):
    """View for admins to edit a user."""
    user = get_object_or_404(User, id=user_id)
    
    if request.method == 'POST':
        form = UserChangeForm(request.POST, request.FILES, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, 'User updated successfully!')
            return redirect('users:user_list')
    else:
        form = UserChangeForm(instance=user)
    
    return render(request, 'users/user_form.html', {
        'form': form,
        'title': f'Edit User: {user.username}'
    })

@login_required
@user_passes_test(is_admin)
def user_delete(request, user_id):
    """View for admins to delete a user."""
    user = get_object_or_404(User, id=user_id)
    
    if request.method == 'POST':
        if user.id == request.user.id:
            messages.error(request, 'You cannot delete your own account!')
            return redirect('users:user_list')
            
        user.delete()
        messages.success(request, 'User deleted successfully!')
        return redirect('users:user_list')
    
    return render(request, 'users/user_delete.html', {'user': user})