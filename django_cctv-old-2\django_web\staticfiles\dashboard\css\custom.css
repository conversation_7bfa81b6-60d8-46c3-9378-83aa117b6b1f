/* Custom CSS for Shinobi CCTV System */

/* Dark theme overrides */
:root {
  --primary-dark: #212529;
  --secondary-dark: #343a40;
  --accent-color: #0d6efd;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --text-light: #f8f9fa;
  --text-muted-dark-theme: #adb5bd; /* Adjusted for better visibility on dark bg */
  --border-color: #495057;
}

body {
    background-color: var(--primary-dark);
    color: var(--text-light);
}

.card {
    background-color: var(--secondary-dark);
    border-color: var(--border-color);
}

.table {
    color: var(--text-light); /* Ensures table text is light */
}
.table-hover tbody tr:hover {
    color: var(--text-light);
    background-color: rgba(255, 255, 255, 0.075); /* Subtle hover for dark theme */
}
.table th, .table td {
    border-color: var(--border-color);
}
.table thead th {
    border-bottom-width: 2px;
}

.list-group-item {
    background-color: var(--secondary-dark);
    border-color: var(--border-color);
    color: var(--text-light);
}
.list-group-item-action:hover, .list-group-item-action:focus {
    background-color: #3e444a; /* Slightly lighter hover for list items */
    color: var(--text-light);
}
.list-group-item.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.form-control, .form-select {
    background-color: #2c3034; /* Darker input background */
    color: var(--text-light);
    border-color: var(--border-color);
}
.form-control:focus, .form-select:focus {
    background-color: #2c3034;
    color: var(--text-light);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-control::placeholder {
    color: #333 !important;
    opacity: 1;
}
.form-check-input {
    background-color: #495057;
    border-color: #6c757d;
}
.form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}


.progress {
    background-color: #495057; /* Darker progress bar background */
}

.alert-info {
    background-color: rgba(13,202,240,0.2);
    color: #0dcaf0;
    border-color: #0dcaf0;
}

.text-muted {
    color: var(--text-muted-dark-theme) !important;
}


/* Camera grid layout */
.camera-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
}

.camera-card {
  border: 1px solid var(--border-color);
  border-radius: 5px;
  overflow: hidden;
  background-color: var(--secondary-dark);
  transition: all 0.3s ease;
}

.camera-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.camera-card .camera-feed .ratio-16x9 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #222;
  position: relative;
  overflow: hidden;
}
.camera-card .camera-feed video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 0.5rem 0.5rem 0 0;
  background: #222;
  display: block;
}

.video-error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    z-index: 20;
}
.error-message {
    padding: 20px;
}


.camera-status-badge { /* Changed from .camera-status to be more specific */
  position: absolute;
  top: 10px;
  /* left: 10px; */ /* Original had right, grid has start (left in LTR) */
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
}
/* Ensure distinct class names if both camera-status and camera-status-badge are used */
.camera-status { /* For camera_detail page */
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}


.status-online {
  background-color: var(--success-color);
  color: white;
}

.status-offline {
  background-color: var(--danger-color);
  color: white;
}

.status-unknown {
  background-color: var(--warning-color);
  color: black;
}

/* Dashboard stats */
.stats-card {
  border-radius: 8px;
  padding: 15px; /* Adjusted padding */
  transition: all 0.3s ease;
  height: 100%;
  background-color: var(--secondary-dark); /* Ensured for consistency */
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.stats-icon {
  font-size: 2rem; /* Adjusted size */
  margin-bottom: 10px; /* Adjusted margin */
}

/* VPN status indicators */
.vpn-status {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 20px;
  font-weight: 500;
  /* margin-right: 10px; */ /* Removed as it might not always be needed */
}

.vpn-connected {
  background-color: rgba(25, 135, 84, 0.2);
  color: #198754; /* Success color */
  border: 1px solid #198754;
}

.vpn-disconnected {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545; /* Danger color */
  border: 1px solid #dc3545;
}

/* Location cards */
.location-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  height: 100%;
  background-color: var(--secondary-dark);
}

.location-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
.location-card .card-header, .location-card .card-body {
  background: var(--secondary-dark);
  color: var(--text-light);
}
.location-card .location-name {
  color: var(--accent-color);
  font-weight: 700;
  font-size: 1.2rem;
}
.location-card .location-status-badge {
  font-size: 1rem;
  padding: 0.4em 0.8em;
}
.location-card .location-details strong,
.location-card .location-details {
  color: var(--text-light);
}
.location-card .location-details .text-muted {
  color: var(--text-muted-dark-theme) !important;
}
.location-card .badge.bg-secondary {
  background: var(--accent-color);
  color: #fff;
}
.location-card .btn-primary, .location-card .btn-outline-secondary {
  font-weight: 600;
}

/* System health gauges */
.gauge-container {
  width: 100%;
  height: 120px; /* Ensure canvas inside fits */
  position: relative;
  margin: 0 auto; /* Center the gauge if its canvas is smaller */
}
.gauge-container canvas {
    max-width: 100%;
    max-height: 120px;
}


/* Sidebar navigation */
.sidebar {
  min-height: calc(100vh - 56px); /* Assuming navbar height is 56px */
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
  background-color: var(--primary-dark); /* Ensure sidebar matches theme */
}

.sidebar .nav-link {
  font-weight: 500;
  color: var(--text-light);
  padding: 0.75rem 1.5rem;
}

.sidebar .nav-link:hover {
  color: var(--accent-color);
  background-color: rgba(13, 110, 253, 0.05); /* Subtle hover */
}

.sidebar .nav-link.active {
  color: var(--accent-color);
  background-color: rgba(13, 110, 253, 0.1);
}

.sidebar .nav-link .bi { /* Bootstrap Icons */
  margin-right: 10px;
  width: 16px;
  height: 16px;
}
.sidebar-heading {
    color: var(--text-muted-dark-theme);
}


/* Camera controls */
.ptz-controls {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 5px;
  max-width: 150px; /* Or adjust as needed */
  margin: 1rem auto; /* Centering if needed */
}

.ptz-btn {
  width: 40px; /* Ensure square buttons */
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; /* Remove default padding if icons are used */
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1090; /* Above most other elements */
    min-width: 250px;
}
#notifications-container .alert { /* Ensure notifications are styled */
    color: #000; /* Default alert text color for better readability */
}
#notifications-container .alert-success { background-color: #d1e7dd; border-color: #badbcc; color: #0f5132; }
#notifications-container .alert-danger { background-color: #f8d7da; border-color: #f5c2c7; color: #842029; }
#notifications-container .alert-warning { background-color: #fff3cd; border-color: #ffecb5; color: #664d03; }
#notifications-container .alert-info { background-color: #cff4fc; border-color: #b6effb; color: #055160; }


/* Make card subtitle and muted text brighter for dark backgrounds */
.card .text-muted,
.text-muted,
.card-title,
.card-header,
.stats-card .text-muted,
.stats-card .card-title,
.stats-card .display-5,
.stats-card .fw-bold,
.stats-card .card-body .mb-0,
.stats-card .card-body .text-muted {
    color: #e0e0e0 !important;
}
.card .card-title,
.card .card-header {
    color: #f8f9fa !important;
}
.stats-icon {
    color: #0dcaf0 !important;
}
.display-5, .fw-bold {
    color: #f8f9fa !important;
}


/* Make form label and help text brighter for dark backgrounds */
.form-label, label, .form-text, .help-block, .text-muted, .form-control::placeholder {
    color: #e0e0e0 !important;
}

input.form-control, textarea.form-control, select.form-select {
    background-color: #23272b;
    color: #656668;
    border-color: #444;
}
input.form-control:focus, textarea.form-control:focus, select.form-select:focus {
    background-color: #23272b;
    color: #656668;
    border-color: #0dcaf0;
    box-shadow: 0 0 0 0.2rem rgba(13,202,240,.25);
}


/* Make all placeholder text (including system/app placeholders like 'ABC CCTV System via Django') a dark gray for better contrast */
input::placeholder, textarea::placeholder, select::placeholder, ::placeholder {
  color: #222 !important;
  opacity: 1;
}
input::-webkit-input-placeholder, textarea::-webkit-input-placeholder, select::-webkit-input-placeholder, ::-webkit-input-placeholder {
  color: #222 !important;
  opacity: 1;
}
input::-moz-placeholder, textarea::-moz-placeholder, select::-moz-placeholder, ::-moz-placeholder {
  color: #222 !important;
  opacity: 1;
}
input:-ms-input-placeholder, textarea:-ms-input-placeholder, select:-ms-input-placeholder, :-ms-input-placeholder {
  color: #222 !important;
  opacity: 1;
}
input::-ms-input-placeholder, textarea::-ms-input-placeholder, select::-ms-input-placeholder, ::-ms-input-placeholder {
  color: #222 !important;
  opacity: 1;
}
input:-moz-placeholder, textarea:-moz-placeholder, select:-moz-placeholder, :-moz-placeholder {
  color: #222 !important;
  opacity: 1;
}


/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .camera-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); /* Adjusted for smaller screens */
  }
  .stats-card .display-5 {
    font-size: 2rem; /* Smaller font for stats on mobile */
  }
}

/* Camera title overlay for video cards */
.camera-title-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 0.25rem 0.5rem;
    text-align: center;
    background: rgba(0,0,0,0.6);
    z-index: 2;
    color: #fff;
    font-weight: 500;
    font-size: 1rem;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.camera-feed .img-fluid {
    max-height: 100%;
}

.camera-card .card-title.text-primary {
  color: var(--bs-primary, #0d6efd) !important;
  font-size: 1.3rem;
  letter-spacing: 0.5px;
  font-weight: 700;
}
.card-body h6,
.card-body .vpn-config-label {
  color: var(--bs-primary, #0d6efd) !important;
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* VPN Configuration Modal Styles */
#vpnConfigModal .modal-content {
  background: #23272b;
  color: #f8f9fa;
}
#vpnConfigModal .modal-header, #vpnConfigModal .modal-footer {
  background: #23272b;
  color: #f8f9fa;
  border: none;
}
#vpnConfigModal .form-label,
#vpnConfigModal label,
#vpnConfigModal .modal-title {
  color: #f8f9fa !important;
}
#vpnConfigModal .form-control,
#vpnConfigModal .form-select,
#vpnConfigModal textarea {
  background: #181a1b;
  color: #f8f9fa;
  border: 1px solid #444;
}
#vpnConfigModal .form-control::placeholder,
#vpnConfigModal textarea::placeholder {
  color: #b0b3b8;
  opacity: 1;
}
#vpnConfigModal .btn-primary {
  background: #0d6efd;
  border: none;
}
#vpnConfigModal .btn-secondary {
  background: #6c757d;
  border: none;
}