/* Custom styles for CCTV Monitoring Web Application */

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #3b3b3b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Custom animations */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Status indicators - DISABLED for Quick Access clean view */
.status-indicator {
  display: none !important; /* Force hide all status indicators */
}

.status-online {
  display: none !important; /* Force hide online status */
}

.status-offline {
  display: none !important; /* Force hide offline status */
}

.status-disabled {
  display: none !important; /* Force hide disabled status */
}

.status-maintenance {
  display: none !important; /* Force hide maintenance status */
}

/* Camera grid styles moved to template-specific CSS to avoid conflicts */

/* Camera feed styles moved to template-specific CSS to avoid conflicts */

/* Responsive adjustments */
@media (max-width: 768px) {
  .camera-grid.layout-3x3,
  .camera-grid.layout-4x4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .camera-grid.layout-2x2,
  .camera-grid.layout-3x3,
  .camera-grid.layout-4x4 {
    grid-template-columns: 1fr;
  }
}

/* Dark mode optimizations */
.dark-mode {
  background-color: #111827;
  color: #f3f4f6;
}

.dark-mode .card {
  background-color: #1F2937;
  border-color: #374151;
}

.dark-mode .sidebar {
  background-color: #111827;
}

/* Dashboard statistics */
.stat-card {
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}