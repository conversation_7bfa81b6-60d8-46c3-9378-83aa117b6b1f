{"port": 8080, "passwordType": "sha256", "detectorMergePamRegionTriggers": true, "wallClockTimestampAsDefault": true, "addStorage": [{"name": "second", "path": "__DIR__/videos2"}], "db": {"host": "shinobi_db", "user": "shinobi", "password": "shinobi", "database": "ccio", "port": 3306}, "mail": {"service": "gmail", "auth": {"user": "<EMAIL>", "pass": "your_password_or_app_specific_password"}}, "cron": {"key": "c8490a7529be68cfec11a50a083c6"}, "pluginKeys": {}, "cpuUsageMarker": "CPU", "subscriptionId": "sub_XXXXXXXXXXXX", "thisIsDocker": true, "ssl": {}}