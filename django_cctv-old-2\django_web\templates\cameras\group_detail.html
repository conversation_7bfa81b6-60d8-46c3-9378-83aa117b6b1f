{% extends 'base.html' %}

{% block content %}
  <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">Group: {{ group.name }}</h1>
    <div class="mb-4">
      <span class="font-semibold text-gray-700 dark:text-gray-200">Description:</span>
      <span class="ml-2 text-gray-900 dark:text-gray-100">{{ group.description }}</span>
    </div>
    <h2 class="text-xl font-semibold mt-6 mb-2 text-gray-800 dark:text-gray-100">Cameras in this group:</h2>
    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
      {% for camera in cameras %}
        <li class="py-2 flex items-center justify-between">
          <span class="text-gray-900 dark:text-gray-100">{{ camera.name }}</span>
          <a href="{% url 'cameras:camera_detail' camera.id %}" class="text-blue-600 dark:text-blue-400 hover:underline">View</a>
        </li>
      {% empty %}
        <li class="py-2 text-gray-500 dark:text-gray-400">No cameras in this group.</li>
      {% endfor %}
    </ul>
    <div class="mt-8">
      <a href="{% url 'cameras:group_list' %}" class="inline-block px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Back to Group List</a>
    </div>
  </div>
{% endblock %}
