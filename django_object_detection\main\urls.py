from django.urls import path
from main.views import ChangeLanguageView, branch_access, camera_dashboard, VideoFeedView, toggle_detection, get_line_positions, update_line_positions, update_mode

urlpatterns = [
    path("", camera_dashboard, name="index"),
    path("language/", ChangeLanguageView.as_view(), name="change_language"),
    path('branch-access/<int:branch_id>/', branch_access, name='branch_access'),
    path('video_feed/<int:branch_id>/<int:camera_id>/', VideoFeedView.as_view(), name='video_feed'),
    path('toggle-detection/<int:camera_id>/', toggle_detection, name='toggle_detection'),
    path('api/cameras/<int:camera_id>/lines/', get_line_positions, name='get_line_positions'),
    path('api/cameras/<int:camera_id>/lines/update/', update_line_positions, name='update_line_positions'),
    path('update-mode/<int:camera_id>/', update_mode, name='update_mode'),
]