#!/usr/bin/env python3
"""
🔍 CAMERA COUNT DEBUG SCRIPT
Debug script to investigate camera count issues in Shinobi CCTV Django service
"""

import os
import sys
import django
import requests
from urllib.parse import urljoin

# Add the Django project to the path
sys.path.append('/app')  # Assuming we're running inside the container

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shinobi_cctv_django.settings')
django.setup()

# Now we can import Django models
from dashboard.models import Camera, CustomUser, CameraGroup

def debug_database_cameras():
    """Debug camera data in the database"""
    print("🔍 CAMERA COUNT DEBUG - DATABASE INVESTIGATION")
    print("=" * 60)
    
    # Check total cameras
    total_cameras = Camera.objects.all()
    print(f"📊 Total cameras in database: {total_cameras.count()}")
    
    # List all cameras
    for camera in total_cameras:
        print(f"📷 Camera: {camera.name}")
        print(f"   - ID: {camera.id}")
        print(f"   - Shinobi Monitor ID: {camera.shinobi_monitor_id}")
        print(f"   - Status: {camera.status}")
        print(f"   - Groups: {[g.name for g in camera.groups.all()]}")
        print(f"   - Location: {camera.location_name}")
        print()
    
    # Check cameras with Shinobi monitor IDs
    cameras_with_shinobi = Camera.objects.filter(shinobi_monitor_id__isnull=False)
    print(f"🎥 Cameras with Shinobi monitor IDs: {cameras_with_shinobi.count()}")
    
    # Check users and their camera groups
    users = CustomUser.objects.all()
    print(f"👥 Total users: {users.count()}")
    
    for user in users:
        print(f"👤 User: {user.username}")
        print(f"   - Is Admin: {user.is_superuser}")
        print(f"   - Role: {user.role.name if user.role else 'No Role'}")
        user_groups = user.camera_groups.all()
        print(f"   - Camera Groups: {[g.name for g in user_groups]} (count: {user_groups.count()})")
        
        # Check cameras accessible to this user
        if user.is_superuser or (user.role and user.role.name == 'Administrator'):
            accessible_cameras = Camera.objects.filter(shinobi_monitor_id__isnull=False)
            print(f"   - Accessible Cameras (Admin): {accessible_cameras.count()}")
        else:
            accessible_cameras = Camera.objects.filter(
                groups__in=user_groups,
                shinobi_monitor_id__isnull=False
            ).distinct()
            print(f"   - Accessible Cameras (User): {accessible_cameras.count()}")
        print()
    
    # Check camera groups
    camera_groups = CameraGroup.objects.all()
    print(f"🏷️ Total camera groups: {camera_groups.count()}")
    
    for group in camera_groups:
        print(f"🏷️ Group: {group.name}")
        group_cameras = Camera.objects.filter(groups=group)
        print(f"   - Cameras in group: {group_cameras.count()}")
        print(f"   - Camera names: {[c.name for c in group_cameras]}")
        print()

def test_shinobi_cctv_dashboard():
    """Test the Shinobi CCTV dashboard endpoint"""
    print("🌐 TESTING SHINOBI CCTV DASHBOARD ENDPOINT")
    print("=" * 50)
    
    try:
        dashboard_url = "http://localhost:5000/"
        print(f"Testing: {dashboard_url}")
        
        response = requests.get(dashboard_url, timeout=15)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Look for camera count indicators
            if "Cameras" in content:
                print("✅ Found 'Cameras' text in response")
                
                # Extract camera count information
                import re
                camera_patterns = [
                    r'(\d+)\s*Cameras?',
                    r'Cameras.*?(\d+)',
                    r'camera_count.*?(\d+)',
                    r'online_cameras.*?(\d+)',
                ]
                
                for pattern in camera_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        print(f"📊 Found camera count pattern '{pattern}': {matches}")
            else:
                print("⚠️ No 'Cameras' text found in response")
                
        else:
            print(f"❌ Dashboard request failed with status: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Dashboard request failed: {str(e)}")

def main():
    """Main debug function"""
    try:
        debug_database_cameras()
        test_shinobi_cctv_dashboard()
        
        print("\n🎯 SUMMARY")
        print("=" * 30)
        print("Check the output above to identify:")
        print("1. Are there cameras in the database?")
        print("2. Do cameras have shinobi_monitor_id values?")
        print("3. Are users assigned to camera groups?")
        print("4. Are cameras assigned to camera groups?")
        print("5. What does the dashboard endpoint return?")
        
    except Exception as e:
        print(f"❌ Debug script failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
