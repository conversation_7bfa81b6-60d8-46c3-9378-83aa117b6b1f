# CCTV Warehouse Monitoring System - PowerShell Deployment Script
# One-command deployment for Windows environments

param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Show-Help {
    Write-Host "🎖️  CCTV Warehouse Monitoring System - Deployment Commands" -ForegroundColor $Green
    Write-Host "=================================================================="
    Write-Host "📋 Available commands:"
    Write-Host ""
    Write-Host "  .\deploy.ps1 init     - Initialize system (create directories, configs)"
    Write-Host "  .\deploy.ps1 deploy   - Deploy complete system (one-command deployment)"
    Write-Host "  .\deploy.ps1 clean    - Clean up system (stop and remove containers)"
    Write-Host "  .\deploy.ps1 logs     - Show logs from all services"
    Write-Host "  .\deploy.ps1 status   - Show status of all services"
    Write-Host "  .\deploy.ps1 test     - Test system functionality"
    Write-Host "  .\deploy.ps1 restart  - Restart all services"
    Write-Host ""
    Write-Host "🚀 Quick start: .\deploy.ps1 deploy" -ForegroundColor $Green
    Write-Host "=================================================================="
}

function Initialize-System {
    Write-Status "🔧 Initializing CCTV Warehouse Monitoring System..."
    
    # Create necessary directories
    Write-Status "Creating necessary directories..."
    $directories = @(
        ".\openvpn_data\pki\private",
        ".\openvpn_data\pki\issued",
        ".\openvpn_data\pki\reqs",
        ".\openvpn_data\pki\certs_by_serial",
        ".\openvpn_data\pki\renewed",
        ".\openvpn_data\pki\revoked",
        ".\shinobi_config",
        ".\shinobi_videos",
        ".\logs"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Status "Created directory: $dir"
        }
    }
    
    # Create Django Web .env
    if (!(Test-Path ".\django_web\.env")) {
        $envContent = @"
# Django Web Service Environment
DEBUG=1
SECRET_KEY=django-insecure-l0p@+4)1_k73%9k!j4nw(i5yb-g5n=s4t+rx)zr8q5y_@!m2vf
DATABASE_URL=*******************************/warehouse_shinobi
SHINOBI_API_URL=http://shinobi-nvr:8080
SHINOBI_API_KEY=vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx
SHINOBI_GROUP_KEY=VqJe1awj1m
OPENVPN_SERVER_HOST=abinetalemuvpn.duckdns.org
"@
        Set-Content -Path ".\django_web\.env" -Value $envContent
        Write-Success "Created django_web\.env"
    }
    
    # Create Shinobi CCTV Django .env
    if (!(Test-Path ".\shinobi_cctv_django\.env")) {
        $envContent = @"
# Shinobi CCTV Django Service Environment
DEBUG=1
SECRET_KEY=shinobi-django-secret-key-change-in-production
DATABASE_URL=*******************************/warehouse_shinobi
SHINOBI_API_KEY=vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx
SHINOBI_GROUP_KEY=VqJe1awj1m
SHINOBI_HOST=http://shinobi-nvr:8080
SHINOBI_CLIENT_URL=http://localhost:8080
REDIS_URL=redis://redis:6379/0
"@
        Set-Content -Path ".\shinobi_cctv_django\.env" -Value $envContent
        Write-Success "Created shinobi_cctv_django\.env"
    }
    
    Write-Success "✅ System initialization complete!"
}

function Deploy-System {
    Write-Status "🚀 Deploying CCTV Warehouse Monitoring System..."
    
    # Initialize first
    Initialize-System
    
    Write-Status "📦 Building and starting all services..."
    docker-compose up -d --build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Status "⏳ Waiting for services to initialize..."
        Start-Sleep -Seconds 30
        
        Write-Status "🔍 Checking service status..."
        docker-compose ps
        
        Write-Success ""
        Write-Success "🎉 Deployment complete!"
        Write-Success "=================================================================="
        Write-Success "🌐 Access points:"
        Write-Success "  - Django Web:     http://localhost:8000"
        Write-Success "  - Shinobi CCTV:   http://localhost:5000"
        Write-Success "  - Shinobi NVR:    http://localhost:8080"
        Write-Success "  - pgAdmin:        http://localhost:5050"
        Write-Success ""
        Write-Success "🔐 Default credentials:"
        Write-Success "  - Shinobi Admin:  <EMAIL> / sU5EjCH63wRMSo048y1tOdvm3B6xGk"
        Write-Success "  - pgAdmin:        <EMAIL> / admin123"
        Write-Success "=================================================================="
    } else {
        Write-Error "❌ Deployment failed! Check logs with: .\deploy.ps1 logs"
    }
}

function Clean-System {
    Write-Status "🧹 Cleaning up CCTV Warehouse Monitoring System..."
    docker-compose down -v
    docker system prune -f
    Write-Success "✅ Cleanup complete!"
}

function Show-Logs {
    Write-Status "📋 Showing logs from all services..."
    docker-compose logs -f
}

function Show-Status {
    Write-Status "📊 Service Status:"
    docker-compose ps
    
    Write-Status ""
    Write-Status "🔍 Health Check:"
    
    $services = @{
        "Django Web" = "http://localhost:8000"
        "Shinobi CCTV" = "http://localhost:5000"
        "Shinobi NVR" = "http://localhost:8080"
        "pgAdmin" = "http://localhost:5050"
    }
    
    foreach ($service in $services.GetEnumerator()) {
        try {
            $response = Invoke-WebRequest -Uri $service.Value -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "$($service.Key): OK"
            } else {
                Write-Warning "$($service.Key): HTTP $($response.StatusCode)"
            }
        } catch {
            Write-Error "$($service.Key): DOWN"
        }
    }
}

function Test-System {
    Write-Status "🧪 Testing CCTV Warehouse Monitoring System..."
    
    $services = @{
        "Django Web" = "http://localhost:8000"
        "Shinobi CCTV" = "http://localhost:5000"
        "Shinobi NVR" = "http://localhost:8080"
        "pgAdmin" = "http://localhost:5050"
    }
    
    foreach ($service in $services.GetEnumerator()) {
        try {
            $response = Invoke-WebRequest -Uri $service.Value -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Success "✅ $($service.Key): OK"
            } else {
                Write-Warning "⚠️ $($service.Key): HTTP $($response.StatusCode)"
            }
        } catch {
            Write-Error "❌ $($service.Key): FAIL"
        }
    }
    
    Write-Success "🎉 System test complete!"
}

function Restart-Services {
    Write-Status "🔄 Restarting all services..."
    docker-compose restart
    Write-Success "✅ All services restarted!"
}

# Main command dispatcher
switch ($Command.ToLower()) {
    "help" { Show-Help }
    "init" { Initialize-System }
    "deploy" { Deploy-System }
    "clean" { Clean-System }
    "logs" { Show-Logs }
    "status" { Show-Status }
    "test" { Test-System }
    "restart" { Restart-Services }
    default { 
        Write-Error "Unknown command: $Command"
        Show-Help 
    }
}
