"""
🎖️ FACE RECOGNITION MICROSERVICE CONFIGURATION
🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 1
⚔️ TACTICAL CONFIGURATION MANAGEMENT
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """🏗️ Microservice Configuration Settings"""
    
    # 🎯 Service Configuration
    SERVICE_NAME: str = "face-recognition-service"
    SERVICE_VERSION: str = "1.0.0"
    SERVICE_HOST: str = "0.0.0.0"
    SERVICE_PORT: int = 8090
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # 🗄️ Database Configuration
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/face_recognition",
        env="DATABASE_URL"
    )
    
    # 🚀 Redis Configuration
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0",
        env="REDIS_URL"
    )
    
    # 🧠 AI Model Configuration
    YOLO_MODEL_PATH: str = Field(
        default="./weights/yolo11n-face.pt",
        env="YOLO_MODEL_PATH"
    )
    ARCFACE_MODEL_PATH: str = Field(
        default="./weights/arcface_r100.pth",
        env="ARCFACE_MODEL_PATH"
    )
    
    # 🎯 Detection Configuration
    CONFIDENCE_THRESHOLD: float = Field(default=0.25, env="CONFIDENCE_THRESHOLD")
    IOU_THRESHOLD: float = Field(default=0.45, env="IOU_THRESHOLD")
    MAX_DETECTIONS: int = Field(default=1000, env="MAX_DETECTIONS")
    
    # 🎭 Recognition Configuration
    RECOGNITION_THRESHOLD: float = Field(default=0.5, env="RECOGNITION_THRESHOLD")
    FEATURE_VECTOR_SIZE: int = Field(default=512, env="FEATURE_VECTOR_SIZE")
    
    # 📁 Storage Configuration
    UPLOAD_DIR: str = Field(default="./uploads", env="UPLOAD_DIR")
    WEIGHTS_DIR: str = Field(default="./weights", env="WEIGHTS_DIR")
    
    # 🔐 Security Configuration
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # 🚀 Performance Configuration
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    BATCH_SIZE: int = Field(default=16, env="BATCH_SIZE")
    USE_GPU: bool = Field(default=True, env="USE_GPU")
    HALF_PRECISION: bool = Field(default=True, env="HALF_PRECISION")
    
    # 📊 Logging Configuration
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 🎖️ Global settings instance
settings = Settings()

# 🏗️ Create necessary directories
def create_directories():
    """Create necessary directories for the service"""
    import os
    
    directories = [
        settings.UPLOAD_DIR,
        settings.WEIGHTS_DIR,
        f"{settings.UPLOAD_DIR}/temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Directory ensured: {directory}")


# 🎯 Initialize directories on import
create_directories()

print(f"🎖️ Configuration loaded: {settings.SERVICE_NAME} v{settings.SERVICE_VERSION}")
print(f"🎯 Debug mode: {settings.DEBUG}")
print(f"🧠 GPU enabled: {settings.USE_GPU}")
print(f"🚀 Max workers: {settings.MAX_WORKERS}")
