{% extends 'base.html' %}

{% block content %}
  <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">Configure Camera Grid</h1>
    <form method="post">
      {% csrf_token %}
      <!-- Example form fields, replace with your actual grid configuration fields -->
      <div class="mb-4">
        <label class="block text-gray-700 dark:text-gray-300 mb-2" for="grid_size">Grid Size</label>
        <input type="number" name="grid_size" id="grid_size" class="w-full px-4 py-2 border rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100" min="1" max="16" required>
      </div>
      <div class="mb-4">
        <label class="block text-gray-700 dark:text-gray-300 mb-2" for="layout">Layout</label>
        <select name="layout" id="layout" class="w-full px-4 py-2 border rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100">
          <option value="2x2">2x2</option>
          <option value="3x3">3x3</option>
          <option value="4x4">4x4</option>
        </select>
      </div>
      <button type="submit" class="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition">Save Configuration</button>
    </form>
  </div>
{% endblock %}
