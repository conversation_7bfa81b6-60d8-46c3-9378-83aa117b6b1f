#!/bin/bash
# Network Mode Switcher for CCTV System
# Supports dual-mode operation: Standalone + Organization VPN

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect current network interfaces
detect_network_interfaces() {
    print_status "🔍 Detecting network interfaces..."
    
    echo "📡 Available Network Interfaces:"
    ip addr show | grep -E "^[0-9]+:" | awk '{print "  - " $2}' | sed 's/:$//'
    
    echo ""
    echo "🌐 IP Addresses:"
    ip addr show | grep "inet " | grep -v "127.0.0.1" | awk '{print "  - " $2 " (" $NF ")"}'
}

# Function to check VPN connection
check_vpn_connection() {
    print_status "🔍 Checking VPN connection..."
    
    # Check for VPN interfaces
    VPN_INTERFACES=$(ip addr show | grep -E "tun|tap|vpn" | wc -l)
    
    # Check for VPN IP ranges (**********/12)
    VPN_IPS=$(ip addr show | grep "inet 172\." | wc -l)
    
    if [ "$VPN_INTERFACES" -gt 0 ] || [ "$VPN_IPS" -gt 0 ]; then
        print_success "✅ VPN connection detected"
        return 0
    else
        print_warning "⚠️ No VPN connection detected"
        return 1
    fi
}

# Function to update environment files
update_env_files() {
    local mode=$1
    print_status "📝 Updating environment files for mode: $mode"
    
    # Update django_web .env
    if [ -f "django_web/.env" ]; then
        sed -i "s/NETWORK_MODE=.*/NETWORK_MODE=$mode/" django_web/.env
        case $mode in
            "organization_vpn")
                sed -i "s/ORGANIZATION_VPN_ENABLED=.*/ORGANIZATION_VPN_ENABLED=true/" django_web/.env
                ;;
            "hybrid")
                sed -i "s/ORGANIZATION_VPN_ENABLED=.*/ORGANIZATION_VPN_ENABLED=true/" django_web/.env
                ;;
            *)
                sed -i "s/ORGANIZATION_VPN_ENABLED=.*/ORGANIZATION_VPN_ENABLED=false/" django_web/.env
                ;;
        esac
        print_success "✅ Updated django_web/.env"
    fi
    
    # Update shinobi_cctv_django .env
    if [ -f "shinobi_cctv_django/.env" ]; then
        sed -i "s/NETWORK_MODE=.*/NETWORK_MODE=$mode/" shinobi_cctv_django/.env
        case $mode in
            "organization_vpn")
                sed -i "s/ORGANIZATION_VPN_ENABLED=.*/ORGANIZATION_VPN_ENABLED=true/" shinobi_cctv_django/.env
                ;;
            "hybrid")
                sed -i "s/ORGANIZATION_VPN_ENABLED=.*/ORGANIZATION_VPN_ENABLED=true/" shinobi_cctv_django/.env
                ;;
            *)
                sed -i "s/ORGANIZATION_VPN_ENABLED=.*/ORGANIZATION_VPN_ENABLED=false/" shinobi_cctv_django/.env
                ;;
        esac
        print_success "✅ Updated shinobi_cctv_django/.env"
    fi
}

# Function to restart services
restart_services() {
    print_status "🔄 Restarting services..."
    
    # Restart specific services that need network reconfiguration
    docker-compose restart shinobi-nvr django_web shinobi_cctv_django
    
    print_success "✅ Services restarted"
}

# Function to test network connectivity
test_network_connectivity() {
    print_status "🧪 Testing network connectivity..."
    
    # Test local network
    if ping -c 1 -W 2 192.168.1.1 >/dev/null 2>&1; then
        print_success "✅ Local network connectivity: OK"
    else
        print_warning "⚠️ Local network connectivity: FAILED"
    fi
    
    # Test VPN network (if in VPN mode)
    if check_vpn_connection; then
        # Try to ping a common VPN gateway
        if ping -c 1 -W 2 ********** >/dev/null 2>&1; then
            print_success "✅ VPN network connectivity: OK"
        else
            print_warning "⚠️ VPN network connectivity: FAILED"
        fi
    fi
    
    # Test Shinobi service
    sleep 5  # Wait for services to start
    if curl -s http://localhost:8080 >/dev/null 2>&1; then
        print_success "✅ Shinobi NVR: OK"
    else
        print_warning "⚠️ Shinobi NVR: Not responding"
    fi
    
    # Test Django services
    if curl -s http://localhost:8000 >/dev/null 2>&1; then
        print_success "✅ Django Web: OK"
    else
        print_warning "⚠️ Django Web: Not responding"
    fi
    
    if curl -s http://localhost:5000 >/dev/null 2>&1; then
        print_success "✅ Shinobi CCTV Django: OK"
    else
        print_warning "⚠️ Shinobi CCTV Django: Not responding"
    fi
}

# Function to show current status
show_status() {
    echo ""
    echo "🎖️ CCTV System Network Status"
    echo "================================"
    
    detect_network_interfaces
    echo ""
    
    # Show current mode from env files
    if [ -f "django_web/.env" ]; then
        CURRENT_MODE=$(grep "NETWORK_MODE=" django_web/.env | cut -d'=' -f2)
        VPN_ENABLED=$(grep "ORGANIZATION_VPN_ENABLED=" django_web/.env | cut -d'=' -f2)
        echo "📋 Current Configuration:"
        echo "  - Network Mode: $CURRENT_MODE"
        echo "  - VPN Enabled: $VPN_ENABLED"
    fi
    
    echo ""
    check_vpn_connection
    echo ""
    
    # Show service status
    echo "🐳 Docker Services:"
    docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}" | grep -E "(shinobi|django)"
}

# Main script logic
case "$1" in
    "standalone")
        echo "🏠 Switching to Standalone Mode..."
        echo "=================================="
        detect_network_interfaces
        update_env_files "standalone"
        restart_services
        test_network_connectivity
        print_success "🎉 Successfully switched to Standalone Mode"
        ;;
    "organization")
        echo "🏢 Switching to Organization VPN Mode..."
        echo "========================================"
        detect_network_interfaces
        
        if ! check_vpn_connection; then
            print_error "❌ No VPN connection detected. Please connect to organization VPN first."
            exit 1
        fi
        
        update_env_files "organization_vpn"
        restart_services
        test_network_connectivity
        print_success "🎉 Successfully switched to Organization VPN Mode"
        ;;
    "hybrid")
        echo "🔄 Switching to Hybrid Mode..."
        echo "============================="
        detect_network_interfaces
        
        if ! check_vpn_connection; then
            print_warning "⚠️ No VPN connection detected. Hybrid mode will work with local network only."
        fi
        
        update_env_files "hybrid"
        restart_services
        test_network_connectivity
        print_success "🎉 Successfully switched to Hybrid Mode"
        ;;
    "detect")
        echo "🔍 Auto-detecting Network Mode..."
        echo "================================="
        detect_network_interfaces
        
        if check_vpn_connection; then
            print_status "🎯 VPN detected - switching to Hybrid Mode"
            update_env_files "hybrid"
        else
            print_status "🎯 No VPN detected - switching to Standalone Mode"
            update_env_files "standalone"
        fi
        
        restart_services
        test_network_connectivity
        print_success "🎉 Auto-detection and configuration completed"
        ;;
    "status")
        show_status
        ;;
    "test")
        echo "🧪 Testing Network Connectivity..."
        echo "================================="
        test_network_connectivity
        ;;
    *)
        echo "🎖️ CCTV System Network Mode Switcher"
        echo "===================================="
        echo ""
        echo "Usage: $0 {standalone|organization|hybrid|detect|status|test}"
        echo ""
        echo "🏠 standalone    - Use only local network (192.168.x.x)"
        echo "🏢 organization  - Use only organization VPN (172.16.x.x)"
        echo "🔄 hybrid        - Use both local and VPN networks"
        echo "🔍 detect        - Auto-detect and configure best mode"
        echo "📊 status        - Show current network status"
        echo "🧪 test          - Test network connectivity"
        echo ""
        echo "Examples:"
        echo "  $0 detect                    # Auto-detect and configure"
        echo "  $0 organization              # Switch to VPN mode"
        echo "  $0 hybrid                    # Enable dual-mode"
        echo "  $0 status                    # Show current status"
        echo ""
        exit 1
        ;;
esac
