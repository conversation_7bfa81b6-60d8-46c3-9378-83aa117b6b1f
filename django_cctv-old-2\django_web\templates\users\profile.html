{% extends 'base.html' %}

{% block content %}
  <div class="max-w-xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">Profile</h1>
    <div class="flex items-center mb-6">
      {% if user.profile_image %}
        <img src="{{ user.profile_image.url }}" alt="{{ user.username }}" class="w-20 h-20 rounded-full mr-4">
      {% else %}
        <div class="w-20 h-20 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-3xl font-bold text-gray-700 dark:text-gray-300 mr-4">
          {{ user.username|first|upper }}
        </div>
      {% endif %}
      <div>
        <p class="text-lg font-semibold text-blue-700 dark:text-blue-200">{{ user.get_full_name|default:user.username }}</p>
        <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</p>
        <p class="text-sm text-gray-500 dark:text-gray-400">Role: {{ user.get_role_display }}</p>
      </div>
    </div>
    <div class="space-y-2">
      <div><span class="font-medium text-gray-700 dark:text-gray-300">Username:</span> <span class="text-blue-700 dark:text-blue-200">{{ user.username }}</span></div>
      <div><span class="font-medium text-gray-700 dark:text-gray-300">Phone:</span> <span class="text-green-700 dark:text-green-300">{{ user.phone_number|default:'-' }}</span></div>
      <div><span class="font-medium text-gray-700 dark:text-gray-300">Last Login:</span> <span class="text-purple-700 dark:text-purple-300">{{ user.last_login|date:'Y-m-d H:i' }}</span></div>
      <div><span class="font-medium text-gray-700 dark:text-gray-300">Last Login IP:</span> {{ user.last_login_ip|default:'-' }}</div>
    </div>
    <div class="mt-6">
      <a href="{% url 'users:profile_edit' %}" class="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition">Edit Profile</a>
    </div>
  </div>
{% endblock %}
