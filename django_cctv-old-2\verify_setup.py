#!/usr/bin/env python3
"""
Verification script to test the shared database configuration
for django_web and shinobi_cctv_django services.
"""

import requests
import time
import sys

def test_service_availability():
    """Test if both services are accessible on their respective ports."""
    services = {
        'django_web': 'http://localhost:8000',
        'shinobi_cctv_django': 'http://localhost:5000'
    }
    
    print("Testing service availability...")
    
    for service_name, url in services.items():
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {service_name} is accessible at {url}")
            else:
                print(f"⚠️  {service_name} returned status code {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {service_name} is not accessible at {url}: {e}")

def test_session_isolation():
    """Test that sessions are isolated between services."""
    print("\nTesting session isolation...")
    
    # Create session objects for both services
    session_web = requests.Session()
    session_cctv = requests.Session()
    
    try:
        # Make requests to both services
        response_web = session_web.get('http://localhost:8000', timeout=10)
        response_cctv = session_cctv.get('http://localhost:5000', timeout=10)
        
        # Check if different session cookies are set
        web_cookies = session_web.cookies.get_dict()
        cctv_cookies = session_cctv.cookies.get_dict()
        
        print(f"Django Web cookies: {list(web_cookies.keys())}")
        print(f"Shinobi CCTV cookies: {list(cctv_cookies.keys())}")
        
        # Check for our custom session cookie names
        if 'django_web_sessionid' in str(web_cookies) or 'sessionid' in web_cookies:
            print("✅ Django Web service is setting session cookies")
        else:
            print("⚠️  Django Web service session cookies not detected")
            
        if 'shinobi_cctv_sessionid' in str(cctv_cookies) or 'sessionid' in cctv_cookies:
            print("✅ Shinobi CCTV service is setting session cookies")
        else:
            print("⚠️  Shinobi CCTV service session cookies not detected")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error testing session isolation: {e}")

def test_database_connection():
    """Test database connectivity by checking admin pages."""
    print("\nTesting database connectivity...")
    
    admin_urls = {
        'django_web': 'http://localhost:8000/admin/',
        'shinobi_cctv_django': 'http://localhost:5000/admin/'
    }
    
    for service_name, url in admin_urls.items():
        try:
            response = requests.get(url, timeout=10)
            if response.status_code in [200, 302]:  # 302 is redirect to login
                print(f"✅ {service_name} admin page accessible (database connected)")
            else:
                print(f"⚠️  {service_name} admin page returned status {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {service_name} admin page not accessible: {e}")

def main():
    """Main verification function."""
    print("🔍 Verifying Django Services Configuration")
    print("=" * 50)
    
    # Wait a moment for services to be ready
    print("Waiting for services to start...")
    time.sleep(5)
    
    test_service_availability()
    test_session_isolation()
    test_database_connection()
    
    print("\n" + "=" * 50)
    print("✅ Verification complete!")
    print("\nNext steps:")
    print("1. Run migrations: docker-compose exec web python manage.py migrate")
    print("2. Run migrations: docker-compose exec shinobi_cctv_django python manage.py migrate")
    print("3. Create superusers for both services if needed")
    print("4. Access services:")
    print("   - Django Web: http://localhost:8000")
    print("   - Shinobi CCTV: http://localhost:5000")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
