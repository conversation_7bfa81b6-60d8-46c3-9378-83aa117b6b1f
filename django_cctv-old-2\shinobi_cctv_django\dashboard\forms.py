from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from .models import CustomUser, Location, Camera, Incident, Role

class LoginForm(forms.Form):
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter username', 'id': 'username'}),
        required=True
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Enter password', 'id': 'password'}),
        required=True
    )
    remember = forms.BooleanField( # Renamed from remember_me to match login.html
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input', 'id': 'remember'}),
        label="Remember me"
    )


class CustomUserCreationForm(UserCreationForm):
    role = forms.ModelChoiceField(
        queryset=Role.objects.all(),
        required=False, # Make it optional or set a default
        widget=forms.Select(attrs={'class': 'form-select', 'id': 'role_id'})
    )
    accessible_locations = forms.ModelMultipleChoiceField(
        queryset=Location.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '5', 'id': 'locations'}),
        required=False
    )
    class Meta(UserCreationForm.Meta):
        model = CustomUser
        fields = UserCreationForm.Meta.fields + ('email', 'first_name', 'last_name', 'role', 'is_active', 'accessible_locations')
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'first_name'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'last_name'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'id': 'email'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input', 'id': 'is_active'}),
        }
    
    def save(self, commit=True):
        user = super().save(commit=False)
        # Password is set by UserCreationForm
        if commit:
            user.save()
            # ManyToMany fields need to be saved after the instance is saved
            if self.cleaned_data.get('accessible_locations'):
                user.accessible_locations.set(self.cleaned_data['accessible_locations'])
        return user


class CustomUserChangeForm(UserChangeForm):
    role = forms.ModelChoiceField(
        queryset=Role.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select', 'id': 'role_id'})
    )
    accessible_locations = forms.ModelMultipleChoiceField(
        queryset=Location.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '5', 'id': 'locations'}),
        required=False
    )
    # Remove password from here, handle separately if needed
    password = None

    class Meta(UserChangeForm.Meta):
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name', 'role', 'is_active', 'accessible_locations')
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'first_name'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control', 'id': 'last_name'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'id': 'email'}),
            'username': forms.TextInput(attrs={'class': 'form-control', 'id': 'username'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input', 'id': 'is_active'}),
        }

    def save(self, commit=True):
        user = super().save(commit=False)
        if commit:
            user.save()
            if self.cleaned_data.get('accessible_locations'):
                 user.accessible_locations.set(self.cleaned_data['accessible_locations'])
            else: # If nothing selected, clear existing relations
                user.accessible_locations.clear()

        return user


class LocationForm(forms.ModelForm):
    class Meta:
        model = Location
        fields = ['name', 'address', 'city', 'state', 'country', 'zipcode']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter location name', 'id': 'name'}),
            'address': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter street address', 'id': 'address'}),
            'city': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter city', 'id': 'city'}),
            'state': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter state or province', 'id': 'state'}),
            'country': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter country', 'id': 'country'}),
            'zipcode': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter ZIP/postal code', 'id': 'zipcode'}),
        }

class CameraForm(forms.ModelForm):
    location = forms.ModelChoiceField(
        queryset=Location.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Location"
    )
    class Meta:
        model = Camera
        fields = ['name', 'rtsp_url', 'shinobi_id', 'shinobi_api_key', 'shinobi_group_key', 'location', 'is_ptz', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'rtsp_url': forms.URLInput(attrs={'class': 'form-control'}),
            'shinobi_id': forms.TextInput(attrs={'class': 'form-control'}),
            'shinobi_api_key': forms.TextInput(attrs={'class': 'form-control'}),
            'shinobi_group_key': forms.TextInput(attrs={'class': 'form-control'}),
            'is_ptz': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

class IncidentForm(forms.ModelForm):
    location = forms.ModelChoiceField(
        queryset=Location.objects.all(),
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Location",
        required=True
    )
    camera = forms.ModelChoiceField(
        queryset=Camera.objects.all(), # Consider filtering by selected location using JS or separate step
        widget=forms.Select(attrs={'class': 'form-select'}),
        label="Camera",
        required=False
    )
    class Meta:
        model = Incident
        fields = ['title', 'description', 'camera', 'location', 'severity', 'status']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'severity': forms.Select(attrs={'class': 'form-select'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }
