#!/bin/bash
# Master Initialization Script
# This script orchestrates the complete system initialization

set -e

echo "🚀 Starting Complete System Initialization..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root (needed for some operations)
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. This is normal for Docker containers."
fi

# Step 1: Create necessary directories
print_status "Creating necessary directories..."
mkdir -p ./openvpn_data/pki/{private,issued,reqs,certs_by_serial,renewed,revoked}
mkdir -p ./shinobi_config
mkdir -p ./shinobi_videos
mkdir -p ./logs

# Step 2: Set proper permissions
print_status "Setting directory permissions..."
chmod 755 ./openvpn_data
chmod 700 ./openvpn_data/pki/private
chmod 755 ./shinobi_config
chmod 755 ./shinobi_videos

# Step 3: Create environment files if they don't exist
print_status "Creating environment files..."

# Django Web .env
if [ ! -f "./django_web/.env" ]; then
    cat > ./django_web/.env << 'EOF'
# Django Web Service Environment
DEBUG=1
SECRET_KEY=django-insecure-l0p@+4)1_k73%9k!j4nw(i5yb-g5n=s4t+rx)zr8q5y_@!m2vf
DATABASE_URL=*******************************/warehouse_shinobi
SHINOBI_API_URL=http://shinobi-nvr:8080
SHINOBI_API_KEY=vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx
SHINOBI_GROUP_KEY=VqJe1awj1m
OPENVPN_SERVER_HOST=abinetalemuvpn.duckdns.org
EOF
    print_success "Created django_web/.env"
else
    print_status "django_web/.env already exists"
fi

# Shinobi CCTV Django .env
if [ ! -f "./shinobi_cctv_django/.env" ]; then
    cat > ./shinobi_cctv_django/.env << 'EOF'
# Shinobi CCTV Django Service Environment
DEBUG=1
SECRET_KEY=shinobi-django-secret-key-change-in-production
DATABASE_URL=*******************************/warehouse_shinobi
SHINOBI_API_KEY=vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx
SHINOBI_GROUP_KEY=VqJe1awj1m
SHINOBI_HOST=http://shinobi-nvr:8080
SHINOBI_CLIENT_URL=http://localhost:8080
REDIS_URL=redis://redis:6379/0
EOF
    print_success "Created shinobi_cctv_django/.env"
else
    print_status "shinobi_cctv_django/.env already exists"
fi

# Step 4: Create Shinobi configuration files
print_status "Creating Shinobi configuration files..."

# Shinobi conf.json
if [ ! -f "./shinobi_config/conf.json" ]; then
    cat > ./shinobi_config/conf.json << 'EOF'
{
    "port": 8080,
    "addStorage": [
        {
            "name": "second",
            "path": "/home/<USER>/videos2"
        }
    ],
    "db": {
        "host": "shinobi_db",
        "user": "shinobi",
        "password": "shinobi_password",
        "database": "shinobi_db",
        "port": 3306
    },
    "mail": {
        "service": "gmail",
        "auth": {
            "user": "<EMAIL>",
            "pass": "your_password"
        }
    },
    "cron": {
        "key": "fd6c7849-904d-47ea-922b-5143358ba0de"
    },
    "pluginKeys": {
        "Motion": "b7502fd9-506c-4dda-9aa5-8a9c1bd7a52d",
        "OpenCV": "f078bcfe-c39a-4eb5-bd52-9382ca828e8a",
        "OpenALPR": "dbff574e-9d4a-44c1-b578-3dc27f1fcb93"
    },
    "passwordType": "sha256",
    "streamDir": "/dev/shm/streams",
    "videosDir": "/home/<USER>/videos",
    "addons": [
        "/home/<USER>/plugins/motion/shinobi-motion.js",
        "/home/<USER>/plugins/opencv/shinobi-opencv.js",
        "/home/<USER>/plugins/openalpr/shinobi-openalpr.js"
    ]
}
EOF
    print_success "Created shinobi_config/conf.json"
else
    print_status "shinobi_config/conf.json already exists"
fi

# Shinobi pm2.yml
if [ ! -f "./shinobi_config/pm2.yml" ]; then
    cat > ./shinobi_config/pm2.yml << 'EOF'
apps:
  - script: camera.js
    name: camera
    cwd: /home/<USER>
    args: ''
    instances: 1
    exec_mode: fork
    env:
      NODE_ENV: production
  - script: cron.js
    name: cron
    cwd: /home/<USER>
    args: ''
    instances: 1
    exec_mode: fork
    env:
      NODE_ENV: production
EOF
    print_success "Created shinobi_config/pm2.yml"
else
    print_status "shinobi_config/pm2.yml already exists"
fi

# Shinobi super.json
if [ ! -f "./shinobi_config/super.json" ]; then
    cat > ./shinobi_config/super.json << 'EOF'
[
    {
        "mail": "<EMAIL>",
        "pass": "sU5EjCH63wRMSo048y1tOdvm3B6xGk",
        "details": {
            "factorAuth": "0",
            "sub": {
                "max": 10,
                "used": 0
            },
            "allmonitors": {
                "max": 10,
                "used": 0
            },
            "monitors": {
                "max": 10,
                "used": 0
            },
            "diskUsed": 0,
            "diskMax": "10000",
            "log_retention": 30,
            "size": "0",
            "days": "10"
        }
    }
]
EOF
    print_success "Created shinobi_config/super.json"
else
    print_status "shinobi_config/super.json already exists"
fi

# Step 5: Make scripts executable
print_status "Making scripts executable..."
chmod +x ./scripts/*.sh

print_success "=================================================="
print_success "🎉 System initialization preparation complete!"
print_success "=================================================="
print_status "Next steps:"
print_status "1. Run: docker-compose up -d --build"
print_status "2. Services will auto-initialize on first run"
print_status "3. Access points:"
print_status "   - Django Web: http://localhost:8000"
print_status "   - Shinobi CCTV: http://localhost:5000"
print_status "   - Shinobi NVR: http://localhost:8080"
print_status "   - pgAdmin: http://localhost:5050"
print_success "=================================================="
