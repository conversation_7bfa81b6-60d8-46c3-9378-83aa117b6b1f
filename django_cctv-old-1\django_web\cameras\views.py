from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from .models import Camera, CameraGroup, CameraEvent
from .forms import CameraForm, CameraGroupForm, CameraGridViewForm
from .shinobi_client import shinobi_client
from users.views import is_admin
import json
import logging

logger = logging.getLogger(__name__)

@login_required
def camera_list(request):
    """View for listing all cameras."""
    if request.user.is_admin:
        cameras = Camera.objects.all()
    else:
        # Filter cameras based on user's permissions
        camera_groups = request.user.camera_groups.all()
        cameras = Camera.objects.filter(groups__in=camera_groups).distinct()
    
    return render(request, 'cameras/camera_list.html', {
        'cameras': cameras
    })

@login_required
def camera_detail(request, camera_id):
    """View for viewing a specific camera."""
    camera = get_object_or_404(Camera, id=camera_id)

    # Check permissions
    if not request.user.is_admin and not request.user.has_camera_access(camera):
        messages.error(request, "You don't have permission to view this camera.")
        return redirect('cameras:camera_list')

    # Get recent events for this camera
    events = CameraEvent.objects.filter(camera=camera).order_by('-timestamp')[:10]

    # Get Shinobi monitor data
    shinobi_data = camera.get_shinobi_monitor_data()

    # Update camera status from Shinobi if possible
    if shinobi_data:
        camera.update_status_from_shinobi()

    return render(request, 'cameras/camera_detail.html', {
        'camera': camera,
        'events': events,
        'shinobi_data': shinobi_data,
        'live_stream_url': camera.live_stream_url,
        'shinobi_embed_url': camera.shinobi_embed_url
    })

@login_required
@user_passes_test(is_admin)
def camera_create(request):
    """View for creating a new camera."""
    if request.method == 'POST':
        form = CameraForm(request.POST)
        if form.is_valid():
            camera = form.save()
            
            # TODO: Integrate with Shinobi API to create the monitor
            # This would be implemented in a separate service/function
            
            messages.success(request, f'Camera "{camera.name}" created successfully!')
            return redirect('cameras:camera_detail', camera_id=camera.id)
    else:
        form = CameraForm()
    
    return render(request, 'cameras/camera_form.html', {
        'form': form,
        'title': 'Add New Camera'
    })

@login_required
@user_passes_test(is_admin)
def camera_edit(request, camera_id):
    """View for editing an existing camera."""
    camera = get_object_or_404(Camera, id=camera_id)
    
    if request.method == 'POST':
        form = CameraForm(request.POST, instance=camera)
        if form.is_valid():
            camera = form.save()
            
            # TODO: Integrate with Shinobi API to update the monitor
            # This would be implemented in a separate service/function
            
            messages.success(request, f'Camera "{camera.name}" updated successfully!')
            return redirect('cameras:camera_detail', camera_id=camera.id)
    else:
        form = CameraForm(instance=camera)
    
    return render(request, 'cameras/camera_form.html', {
        'form': form,
        'camera': camera,
        'title': f'Edit Camera: {camera.name}'
    })

@login_required
@user_passes_test(is_admin)
def camera_delete(request, camera_id):
    """View for deleting a camera."""
    camera = get_object_or_404(Camera, id=camera_id)
    
    if request.method == 'POST':
        camera_name = camera.name
        
        # TODO: Integrate with Shinobi API to delete the monitor
        # This would be implemented in a separate service/function
        
        camera.delete()
        messages.success(request, f'Camera "{camera_name}" deleted successfully!')
        return redirect('cameras:camera_list')
    
    return render(request, 'cameras/camera_delete.html', {'camera': camera})

@login_required
def camera_grid(request):
    """View for displaying a grid of camera feeds."""
    if request.user.is_admin:
        # Admins can see all cameras
        available_cameras = Camera.objects.all()
    else:
        # Filter cameras based on user's permissions
        camera_groups = request.user.camera_groups.all()
        available_cameras = Camera.objects.filter(
            groups__in=camera_groups
        ).distinct()

    # Get user's grid preferences or use defaults
    layout = request.GET.get('layout', '2x2')

    # If specific cameras are requested, filter to only those
    camera_ids = request.GET.getlist('cameras')
    if camera_ids:
        grid_cameras = available_cameras.filter(id__in=camera_ids)
    else:
        # Otherwise just use available cameras (limit to cameras with Shinobi IDs)
        grid_cameras = available_cameras.filter(shinobi_monitor_id__isnull=False)

    # Update camera statuses from Shinobi
    for camera in grid_cameras:
        camera.update_status_from_shinobi()

    # Get Shinobi monitors data for context
    success, monitors_data = shinobi_client.get_monitors()
    shinobi_monitors = monitors_data if success else []

    return render(request, 'cameras/camera_grid.html', {
        'cameras': grid_cameras,
        'layout': layout,
        'available_cameras': available_cameras,
        'shinobi_monitors': shinobi_monitors
    })

@login_required
def configure_grid(request):
    """View for configuring the camera grid layout."""
    if request.method == 'POST':
        form = CameraGridViewForm(request.user, request.POST)
        if form.is_valid():
            # Redirect to grid view with selected cameras and layout
            cameras = form.cleaned_data['cameras']
            layout = form.cleaned_data['layout']
            
            camera_ids = [str(camera.id) for camera in cameras]
            camera_query = '&'.join([f'cameras={camera_id}' for camera_id in camera_ids])
            
            return redirect(f"/cameras/grid/?layout={layout}&{camera_query}")
    else:
        form = CameraGridViewForm(request.user)
    
    return render(request, 'cameras/configure_grid.html', {'form': form})

@login_required
def event_list(request):
    """View for listing camera events."""
    if request.user.is_admin:
        # Admins can see all events
        events = CameraEvent.objects.all().order_by('-timestamp')
    else:
        # Filter events based on user's camera permissions
        camera_groups = request.user.camera_groups.all()
        accessible_cameras = Camera.objects.filter(groups__in=camera_groups).distinct()
        events = CameraEvent.objects.filter(camera__in=accessible_cameras).order_by('-timestamp')
    
    # Filter by event type if specified
    event_type = request.GET.get('type')
    if event_type:
        events = events.filter(event_type=event_type)
    
    # Filter by camera if specified
    camera_id = request.GET.get('camera')
    if camera_id:
        events = events.filter(camera_id=camera_id)
    
    return render(request, 'cameras/event_list.html', {'events': events})

@login_required
def group_list(request):
    """View for listing camera groups."""
    if request.user.is_admin:
        groups = CameraGroup.objects.all()
    else:
        groups = request.user.camera_groups.all()
    
    return render(request, 'cameras/group_list.html', {'groups': groups})

@login_required
def group_detail(request, group_id):
    """View for viewing a specific camera group."""
    group = get_object_or_404(CameraGroup, id=group_id)
    
    # Check permissions
    if not request.user.is_admin and not request.user.camera_groups.filter(id=group.id).exists():
        messages.error(request, "You don't have permission to view this group.")
        return redirect('cameras:group_list')
    
    cameras = group.cameras.all()
    
    return render(request, 'cameras/group_detail.html', {
        'group': group,
        'cameras': cameras
    })

@login_required
@user_passes_test(is_admin)
def group_create(request):
    """View for creating a new camera group."""
    if request.method == 'POST':
        form = CameraGroupForm(request.POST)
        if form.is_valid():
            group = form.save()
            messages.success(request, f'Camera group "{group.name}" created successfully!')
            return redirect('cameras:group_detail', group_id=group.id)
    else:
        form = CameraGroupForm()
    
    return render(request, 'cameras/group_form.html', {
        'form': form,
        'title': 'Create Camera Group'
    })

@login_required
@user_passes_test(is_admin)
def group_edit(request, group_id):
    """View for editing an existing camera group."""
    group = get_object_or_404(CameraGroup, id=group_id)
    
    if request.method == 'POST':
        form = CameraGroupForm(request.POST, instance=group)
        if form.is_valid():
            group = form.save()
            messages.success(request, f'Camera group "{group.name}" updated successfully!')
            return redirect('cameras:group_detail', group_id=group.id)
    else:
        form = CameraGroupForm(instance=group)
    
    return render(request, 'cameras/group_form.html', {
        'form': form,
        'group': group,
        'title': f'Edit Group: {group.name}'
    })

@login_required
@user_passes_test(is_admin)
def group_delete(request, group_id):
    """View for deleting a camera group."""
    group = get_object_or_404(CameraGroup, id=group_id)
    
    if request.method == 'POST':
        group_name = group.name
        group.delete()
        messages.success(request, f'Camera group "{group_name}" deleted successfully!')
        return redirect('cameras:group_list')
    
    return render(request, 'cameras/group_delete.html', {'group': group})

# API endpoints for integration with Shinobi

@csrf_exempt
def event_webhook(request):
    """Webhook endpoint for receiving events from Shinobi."""
    if request.method != 'POST':
        return HttpResponse(status=405)
    
    try:
        data = json.loads(request.body)
        
        # Extract relevant information from Shinobi webhook
        monitor_id = data.get('mid')
        event_type = data.get('details', {}).get('reason')
        
        # Map Shinobi event type to our model's event types
        event_mapping = {
            'motion': CameraEvent.EventType.MOTION,
            'object': CameraEvent.EventType.OBJECT,
            'face': CameraEvent.EventType.FACE,
        }
        
        mapped_event_type = event_mapping.get(event_type, CameraEvent.EventType.CUSTOM)
        
        # Find the camera associated with this monitor ID
        try:
            camera = Camera.objects.get(shinobi_monitor_id=monitor_id)
        except Camera.DoesNotExist:
            logger.error(f"Received event for unknown monitor ID: {monitor_id}")
            return HttpResponse(status=404)
        
        # Create the event
        snapshot_url = data.get('details', {}).get('imgPath')
        
        CameraEvent.objects.create(
            camera=camera,
            event_type=mapped_event_type,
            snapshot_url=snapshot_url,
            details=data
        )
        
        return JsonResponse({'status': 'success'})
        
    except json.JSONDecodeError:
        logger.error("Invalid JSON received in webhook")
        return HttpResponse(status=400)
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        return HttpResponse(status=500)

@login_required
def api_monitors(request):
    """API endpoint to get Shinobi monitors data."""
    try:
        success, data = shinobi_client.get_monitors()
        if success:
            return JsonResponse({
                'success': True,
                'monitors': data
            })
        else:
            return JsonResponse({
                'success': False,
                'error': data
            }, status=500)
    except Exception as e:
        logger.error(f"Error fetching monitors: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@login_required
def api_camera_stream(request, camera_id):
    """API endpoint to get camera stream URL."""
    try:
        camera = get_object_or_404(Camera, id=camera_id)

        # Check permissions
        if not request.user.is_admin and not request.user.has_camera_access(camera):
            return JsonResponse({'error': 'Permission denied'}, status=403)

        return JsonResponse({
            'success': True,
            'stream_url': camera.live_stream_url,
            'embed_url': camera.shinobi_embed_url,
            'snapshot_url': camera.thumbnail_url,
            'monitor_id': camera.shinobi_monitor_id
        })
    except Exception as e:
        logger.error(f"Error getting camera stream: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

@csrf_exempt
def update_camera_status(request):
    """API endpoint to update camera status."""
    if request.method != 'POST':
        return HttpResponse(status=405)
    
    try:
        data = json.loads(request.body)
        
        camera_id = data.get('camera_id')
        status = data.get('status')
        
        if not camera_id or not status:
            return JsonResponse({'error': 'Missing camera_id or status'}, status=400)
        
        try:
            camera = Camera.objects.get(id=camera_id)
        except Camera.DoesNotExist:
            return JsonResponse({'error': f'Camera with ID {camera_id} not found'}, status=404)
        
        # Update camera status
        if status in dict(Camera.CameraStatus.choices).keys():
            camera.status = status
            camera.save(update_fields=['status'])
            
            # Create event for status change if it's an online/offline event
            if status in [Camera.CameraStatus.ONLINE, Camera.CameraStatus.OFFLINE]:
                event_type = CameraEvent.EventType.ONLINE if status == Camera.CameraStatus.ONLINE else CameraEvent.EventType.OFFLINE
                
                CameraEvent.objects.create(
                    camera=camera,
                    event_type=event_type
                )
                
            return JsonResponse({'status': 'success'})
        else:
            return JsonResponse({'error': f'Invalid status: {status}'}, status=400)
        
    except json.JSONDecodeError:
        return HttpResponse(status=400)
    except Exception as e:
        logger.error(f"Error updating camera status: {str(e)}")
        return HttpResponse(status=500)