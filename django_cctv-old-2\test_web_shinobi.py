#!/usr/bin/env python3
"""
Test script to verify Shinobi integration in django_web service
"""

import os
import sys
import django

# Add Django project to path
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cctv_project.settings')

django.setup()

from cameras.shinobi_client import shinobi_client
from cameras.models import Camera

def test_shinobi_integration():
    """Test Shinobi integration in django_web"""
    
    print("🔍 Testing Shinobi Integration in django_web")
    print("=" * 50)
    
    # Test 1: ShinobiClient configuration
    print("📡 Test 1: ShinobiClient Configuration")
    print(f"Host: {shinobi_client.host}")
    print(f"API Key: {shinobi_client.api_key}")
    print(f"Group Key: {shinobi_client.group_key}")
    print()
    
    # Test 2: Get monitors from Shinobi
    print("📡 Test 2: Get Monitors from Shinobi")
    success, monitors = shinobi_client.get_monitors()
    
    if success:
        print(f"✅ Successfully retrieved {len(monitors)} monitors")
        for monitor in monitors:
            print(f"  - {monitor.get('name', 'Unknown')} (ID: {monitor.get('mid', 'Unknown')})")
    else:
        print(f"❌ Failed to get monitors: {monitors}")
    print()
    
    # Test 3: Test Camera model integration
    print("📡 Test 3: Camera Model Integration")
    cameras = Camera.objects.all()
    print(f"Found {cameras.count()} cameras in database:")
    
    for camera in cameras:
        print(f"\n🎥 Camera: {camera.name}")
        print(f"   Shinobi Monitor ID: {camera.shinobi_monitor_id}")
        print(f"   Live Stream URL: {camera.live_stream_url}")
        print(f"   Embed URL: {camera.shinobi_embed_url}")
        print(f"   Thumbnail URL: {camera.thumbnail_url}")
        
        # Test getting monitor data
        monitor_data = camera.get_shinobi_monitor_data()
        if monitor_data:
            print(f"   ✅ Monitor data retrieved: {monitor_data.get('name', 'Unknown')}")
            print(f"      Mode: {monitor_data.get('mode', 'Unknown')}")
            print(f"      Resolution: {monitor_data.get('width', '?')}x{monitor_data.get('height', '?')}")
        else:
            print(f"   ❌ No monitor data found")
    
    print("\n" + "=" * 50)
    print("🎉 Test completed!")

if __name__ == "__main__":
    test_shinobi_integration()
