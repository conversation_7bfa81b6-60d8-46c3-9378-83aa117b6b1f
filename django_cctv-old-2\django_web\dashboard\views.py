from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from cameras.models import Camera, CameraEvent
from cameras.shinobi_client import ShinobiClient
import requests
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

@login_required
def index(request):
    """Main dashboard view."""
    # Get camera statistics
    if request.user.is_admin:
        cameras = Camera.objects.all()
    else:
        # Filter cameras based on user's permissions
        camera_groups = request.user.camera_groups.all()
        cameras = Camera.objects.filter(groups__in=camera_groups).distinct()
    
    total_cameras = cameras.count()
    online_cameras = cameras.filter(status=Camera.CameraStatus.ONLINE).count()
    offline_cameras = cameras.filter(status=Camera.CameraStatus.OFFLINE).count()
    
    # Get recent events
    if request.user.is_admin:
        recent_events = CameraEvent.objects.order_by('-timestamp')[:10]
    else:
        recent_events = CameraEvent.objects.filter(
            camera__in=cameras
        ).order_by('-timestamp')[:10]
    
    # System status checks
    # This could be expanded to include more detailed health checks
    try:
        openvpn_status = check_openvpn_status()
    except Exception as e:
        logger.error(f"Error checking OpenVPN status: {str(e)}")
        openvpn_status = False
    
    try:
        shinobi_status = check_shinobi_status()
    except Exception as e:
        logger.error(f"Error checking Shinobi status: {str(e)}")
        shinobi_status = False

    # Get cameras with HLS URLs for Quick Access (same approach as live_monitors)
    quick_access_cameras = cameras.filter(shinobi_monitor_id__isnull=False).order_by('name')[:5]

    # Add HLS URLs to cameras for Quick Access
    shinobi_client = ShinobiClient()
    shinobi_client_url = "http://localhost:8080"
    api_key = shinobi_client.api_key
    group_key = shinobi_client.group_key

    cameras_with_streams = []
    for camera in quick_access_cameras:
        if camera.shinobi_monitor_id:
            hls_url = f"{shinobi_client_url}/{api_key}/hls/{group_key}/{camera.shinobi_monitor_id}/s.m3u8"
            preview_url = f"{shinobi_client_url}/{api_key}/jpeg/{group_key}/{camera.shinobi_monitor_id}/s.jpg"

            # Add HLS URLs to camera object for template use
            camera.hls_url = hls_url
            camera.preview_url = preview_url

            # Get live status from Shinobi and update camera status
            try:
                success, monitor_data = shinobi_client.get_monitor(camera.shinobi_monitor_id)
                if success:
                    shinobi_mode = monitor_data.get('mode', 'stop')
                    shinobi_status = monitor_data.get('status', 'stop')

                    # Map Shinobi status to our status (same logic as live_monitors)
                    if shinobi_mode == 'start' or shinobi_status == 'start':
                        camera.status = 'online'
                    elif shinobi_mode == 'record' or shinobi_status == 'record':
                        camera.status = 'recording'
                    else:
                        camera.status = 'offline'

                    # Update database status
                    Camera.objects.filter(id=camera.id).update(status=camera.status)
                else:
                    camera.status = 'offline'
            except Exception as e:
                logger.error(f"Error getting live status for camera {camera.id}: {str(e)}")
                camera.status = 'offline'

        cameras_with_streams.append(camera)

    context = {
        'total_cameras': total_cameras,
        'online_cameras': online_cameras,
        'offline_cameras': offline_cameras,
        'recent_events': recent_events,
        'openvpn_status': openvpn_status,
        'shinobi_status': shinobi_status,
        'cameras': cameras_with_streams  # Cameras with HLS URLs for quick access
    }
    
    return render(request, 'dashboard/index.html', context)

@login_required
def system_status(request):
    """API endpoint for getting system status (for AJAX updates)."""
    # Get camera statistics
    if request.user.is_admin:
        cameras = Camera.objects.all()
    else:
        # Filter cameras based on user's permissions
        camera_groups = request.user.camera_groups.all()
        cameras = Camera.objects.filter(groups__in=camera_groups).distinct()
    
    total_cameras = cameras.count()
    online_cameras = cameras.filter(status=Camera.CameraStatus.ONLINE).count()
    offline_cameras = cameras.filter(status=Camera.CameraStatus.OFFLINE).count()
    
    # System status checks
    try:
        openvpn_status = check_openvpn_status()
    except Exception:
        openvpn_status = False
    
    try:
        shinobi_status = check_shinobi_status()
    except Exception:
        shinobi_status = False
    
    return JsonResponse({
        'total_cameras': total_cameras,
        'online_cameras': online_cameras,
        'offline_cameras': offline_cameras,
        'openvpn_status': openvpn_status,
        'shinobi_status': shinobi_status,
    })

def check_openvpn_status():
    """Check if OpenVPN service is running."""
    try:
        response = requests.get(
            f"{settings.OPENVPN_API_URL}/api/status", 
            auth=(settings.OPENVPN_USERNAME, settings.OPENVPN_PASSWORD),
            timeout=5
        )
        return response.status_code == 200
    except requests.RequestException:
        return False

def check_shinobi_status():
    """Check if Shinobi NVR service is running."""
    try:
        response = requests.get(f"{settings.SHINOBI_API_URL}/", timeout=5)
        return response.status_code == 200
    except requests.RequestException:
        return False