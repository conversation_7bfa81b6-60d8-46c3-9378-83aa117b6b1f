# CCTV Warehouse Monitoring System - Deployment Makefile
# One-command deployment for production environments

.PHONY: help init deploy clean logs status test

# Default target
help:
	@echo "🎖️  CCTV Warehouse Monitoring System - Deployment Commands"
	@echo "=================================================================="
	@echo "📋 Available commands:"
	@echo ""
	@echo "  make init     - Initialize system (create directories, configs)"
	@echo "  make deploy   - Deploy complete system (one-command deployment)"
	@echo "  make clean    - Clean up system (stop and remove containers)"
	@echo "  make logs     - Show logs from all services"
	@echo "  make status   - Show status of all services"
	@echo "  make test     - Test system functionality"
	@echo "  make restart  - Restart all services"
	@echo ""
	@echo "🚀 Quick start: make deploy"
	@echo "=================================================================="

# Initialize system
init:
	@echo "🔧 Initializing CCTV Warehouse Monitoring System..."
	@chmod +x scripts/*.sh
	@./scripts/init-all.sh
	@echo "✅ System initialization complete!"

# Deploy complete system
deploy: init
	@echo "🚀 Deploying CCTV Warehouse Monitoring System..."
	@echo "📦 Building and starting all services..."
	@docker-compose up -d --build
	@echo "⏳ Waiting for services to initialize..."
	@sleep 30
	@echo "🔍 Checking service status..."
	@docker-compose ps
	@echo ""
	@echo "🎉 Deployment complete!"
	@echo "=================================================================="
	@echo "🌐 Access points:"
	@echo "  - Django Web:     http://localhost:8000"
	@echo "  - Shinobi CCTV:   http://localhost:5000"
	@echo "  - Shinobi NVR:    http://localhost:8080"
	@echo "  - pgAdmin:        http://localhost:5050"
	@echo ""
	@echo "🔐 Default credentials:"
	@echo "  - Shinobi Admin:  <EMAIL> / sU5EjCH63wRMSo048y1tOdvm3B6xGk"
	@echo "  - pgAdmin:        <EMAIL> / admin123"
	@echo "=================================================================="

# Clean up system
clean:
	@echo "🧹 Cleaning up CCTV Warehouse Monitoring System..."
	@docker-compose down -v
	@docker system prune -f
	@echo "✅ Cleanup complete!"

# Show logs
logs:
	@echo "📋 Showing logs from all services..."
	@docker-compose logs -f

# Show status
status:
	@echo "📊 Service Status:"
	@docker-compose ps
	@echo ""
	@echo "🔍 Health Check:"
	@echo "Django Web:     $(shell curl -s -o /dev/null -w "%{http_code}" http://localhost:8000 || echo "DOWN")"
	@echo "Shinobi CCTV:   $(shell curl -s -o /dev/null -w "%{http_code}" http://localhost:5000 || echo "DOWN")"
	@echo "Shinobi NVR:    $(shell curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 || echo "DOWN")"
	@echo "pgAdmin:        $(shell curl -s -o /dev/null -w "%{http_code}" http://localhost:5050 || echo "DOWN")"

# Test system
test:
	@echo "🧪 Testing CCTV Warehouse Monitoring System..."
	@echo "Testing Django Web service..."
	@curl -f http://localhost:8000 > /dev/null && echo "✅ Django Web: OK" || echo "❌ Django Web: FAIL"
	@echo "Testing Shinobi CCTV service..."
	@curl -f http://localhost:5000 > /dev/null && echo "✅ Shinobi CCTV: OK" || echo "❌ Shinobi CCTV: FAIL"
	@echo "Testing Shinobi NVR service..."
	@curl -f http://localhost:8080 > /dev/null && echo "✅ Shinobi NVR: OK" || echo "❌ Shinobi NVR: FAIL"
	@echo "Testing pgAdmin service..."
	@curl -f http://localhost:5050 > /dev/null && echo "✅ pgAdmin: OK" || echo "❌ pgAdmin: FAIL"
	@echo "🎉 System test complete!"

# Restart services
restart:
	@echo "🔄 Restarting all services..."
	@docker-compose restart
	@echo "✅ All services restarted!"

# Quick deployment for production
production: clean deploy test
	@echo "🏭 Production deployment complete!"
