# =============================================================================
# 🎖️ SHINOBI CCTV DJANGO MODELS - UNMANAGED SHARED + SERVICE SPECIFIC
# =============================================================================
# Unmanaged shared models reference tables managed by shared_models microservice
# Service-specific models are managed by this service
# =============================================================================

from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.conf import settings


# =============================================================================
# 🎖️ UNMANAGED SHARED MODELS (Reference shared_models microservice tables)
# =============================================================================

class Role(models.Model):
    """👤 User roles - UNMANAGED (managed by shared_models microservice)"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    permissions = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        managed = False  # 🎯 Unmanaged - table created by shared_models microservice
        db_table = 'shared_roles'


class CameraGroup(models.Model):
    """🏷️ Camera groups - UNMANAGED (managed by shared_models microservice)"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        managed = False  # 🎯 Unmanaged - table created by shared_models microservice
        db_table = 'shared_camera_groups'
        ordering = ['name']


class CustomUser(AbstractUser):
    """👤 User model - UNMANAGED (managed by shared_models microservice)"""

    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True, related_name='users')

    # Additional fields for compatibility
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    profile_image = models.ImageField(upload_to='profile_images/', blank=True, null=True)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)

    # Track camera access permissions (shared)
    camera_groups = models.ManyToManyField(
        CameraGroup,
        related_name='users',
        blank=True
    )

    # For audit purposes
    date_modified = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False  # 🎯 Unmanaged - table created by shared_models microservice
        db_table = 'shared_users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    @property
    def is_admin(self):
        return (self.role and self.role.name == "Administrator") or self.is_superuser

    @property
    def is_operator(self):
        return self.role and self.role.name == "Operator"

    def __str__(self):
        return self.username


class Camera(models.Model):
    """🎥 Camera model - UNMANAGED (managed by shared_models microservice)"""

    class CameraType(models.TextChoices):
        IP_CAMERA = 'ip', _('IP Camera')
        RTSP = 'rtsp', _('RTSP Stream')
        HTTP = 'http', _('HTTP Stream')
        HLS = 'hls', _('HLS Stream')

    class CameraStatus(models.TextChoices):
        ONLINE = 'online', _('Online')
        OFFLINE = 'offline', _('Offline')
        DISABLED = 'disabled', _('Disabled')
        MAINTENANCE = 'maintenance', _('Maintenance')

    # Basic information
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    location_name = models.CharField(max_length=200, blank=True)
    type = models.CharField(
        max_length=20,
        choices=CameraType.choices,
        default=CameraType.IP_CAMERA
    )

    # Connection details
    stream_url = models.CharField(max_length=255)
    rtsp_url = models.CharField(max_length=255, blank=True, null=True)
    username = models.CharField(max_length=100, blank=True, null=True)
    password = models.CharField(max_length=100, blank=True, null=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    port = models.IntegerField(default=80)

    # Status and metadata
    status = models.CharField(
        max_length=20,
        choices=CameraStatus.choices,
        default=CameraStatus.OFFLINE
    )
    last_online = models.DateTimeField(blank=True, null=True)

    # Shinobi integration
    shinobi_monitor_id = models.CharField(max_length=100, blank=True, null=True)
    shinobi_id = models.CharField(max_length=100, blank=True, null=True)
    shinobi_api_key = models.CharField(max_length=100, blank=True, null=True)
    shinobi_group_key = models.CharField(max_length=100, blank=True, null=True)

    # Additional features
    is_recording = models.BooleanField(default=False)
    is_ptz = models.BooleanField(default=False)

    # Camera grouping
    groups = models.ManyToManyField(CameraGroup, related_name='cameras', blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.location_name})"

    @property
    def shinobi_monitor_id_compat(self):
        """Compatibility property - returns shinobi_monitor_id or shinobi_id"""
        return self.shinobi_monitor_id or self.shinobi_id

    # CRITICAL: Add URL properties from shared models for consistency
    @property
    def shinobi_hls_url(self):
        """Return the Shinobi HLS stream URL for video.js or similar players."""
        if not self.shinobi_monitor_id:
            return None

        from django.conf import settings
        group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)
        api_key = getattr(settings, 'SHINOBI_API_KEY', None)
        if not group_key or not api_key:
            return None

        browser_url = getattr(settings, 'SHINOBI_CLIENT_URL', 'http://localhost:8080')
        return f"{browser_url}/{group_key}/hls/{self.shinobi_monitor_id}/index.m3u8?api={api_key}"

    @property
    def shinobi_embed_url_with_api(self):
        """Return a working MJPEG stream URL."""
        # Use individual camera fields if available, otherwise fall back to global settings
        monitor_id = self.shinobi_id or self.shinobi_monitor_id
        api_key = self.shinobi_api_key
        group_key = self.shinobi_group_key

        if not monitor_id:
            return None

        # Fall back to global settings if individual fields are not set
        from django.conf import settings
        if not api_key:
            api_key = getattr(settings, 'SHINOBI_API_KEY', None)
        if not group_key:
            group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)

        if not api_key or not group_key:
            return None

        browser_url = getattr(settings, 'SHINOBI_CLIENT_URL', 'http://localhost:8080')
        return f"{browser_url}/{group_key}/mjpeg/{monitor_id}?api={api_key}"

    @property
    def live_stream_url(self):
        """Return the URL for the live stream."""
        if not self.shinobi_monitor_id:
            return None

        # Try HLS first, fallback to MJPEG
        return self.shinobi_hls_url or self.shinobi_embed_url_with_api

    @property
    def thumbnail_url(self):
        """Return the URL for the camera thumbnail."""
        if not self.shinobi_monitor_id:
            return None

        from django.conf import settings
        group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)
        api_key = getattr(settings, 'SHINOBI_API_KEY', None)
        if not group_key or not api_key:
            return None

        browser_url = getattr(settings, 'SHINOBI_CLIENT_URL', 'http://localhost:8080')
        return f"{browser_url}/{api_key}/jpeg/{group_key}/{self.shinobi_monitor_id}/s.jpg"

    class Meta:
        managed = False  # 🎯 Unmanaged - table created by shared_models microservice
        db_table = 'shared_cameras'
        ordering = ['name']


class Person(models.Model):
    """👤 Person model for face recognition - UNMANAGED (managed by shared_models microservice)"""

    # Link to Face Recognition service
    face_recognition_id = models.IntegerField(unique=True, help_text="ID from Face Recognition service")

    # Basic information
    name = models.CharField(max_length=255)
    employee_id = models.CharField(max_length=100, blank=True, null=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    role = models.CharField(max_length=100, blank=True, null=True)
    email = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=50, blank=True, null=True)
    status = models.CharField(max_length=50, default="active")

    # Integration metadata
    last_sync = models.DateTimeField(auto_now=True)
    sync_status = models.CharField(max_length=20, default="synced")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False  # 🎯 Unmanaged - table created by shared_models microservice
        db_table = 'shared_persons'
        verbose_name = 'Person'
        verbose_name_plural = 'Persons'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.employee_id or 'No ID'})"


class CameraRecognitionEvent(models.Model):
    """🚨 Camera recognition events - UNMANAGED (managed by shared_models microservice)"""

    class EventType(models.TextChoices):
        FACE_DETECTED = 'face_detected', _('Face Detected')
        PERSON_RECOGNIZED = 'person_recognized', _('Person Recognized')
        UNKNOWN_FACE = 'unknown_face', _('Unknown Face')
        RECOGNITION_ERROR = 'recognition_error', _('Recognition Error')

    # Links to existing models
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='recognition_events')
    person = models.ForeignKey(Person, on_delete=models.SET_NULL, null=True, blank=True)

    # Event details
    event_type = models.CharField(max_length=20, choices=EventType.choices)
    confidence_score = models.FloatField(help_text="Recognition confidence (0.0 - 1.0)")

    # Face Recognition service data
    recognition_log_id = models.IntegerField(help_text="ID from Face Recognition service log")

    # Image data
    snapshot_url = models.URLField(blank=True, null=True)
    face_crop_path = models.CharField(max_length=500, blank=True, null=True)

    # Detection data
    bounding_box = models.JSONField(blank=True, null=True, help_text="Face bounding box [x1, y1, x2, y2]")

    # Metadata
    processing_time_ms = models.FloatField(blank=True, null=True)
    extra_data = models.JSONField(blank=True, null=True)

    # Timestamps
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False  # 🎯 Unmanaged - table created by shared_models microservice
        db_table = 'shared_camera_recognition_events'
        verbose_name = 'Camera Recognition Event'
        verbose_name_plural = 'Camera Recognition Events'
        ordering = ['-timestamp']

    def __str__(self):
        person_name = self.person.name if self.person else "Unknown"
        return f"{self.camera.name} - {person_name} at {self.timestamp}"


# Create aliases for compatibility
User = CustomUser

# =============================================================================
# 🎖️ SHINOBI CCTV DJANGO SPECIFIC MODELS
# =============================================================================

class Location(models.Model):
    """📍 Location model - Shinobi CCTV Django specific"""
    name = models.CharField(max_length=100, null=False)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    zipcode = models.CharField(max_length=20, blank=True, null=True)
    vpn_status = models.BooleanField(default=False)
    last_online = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    users_with_access = models.ManyToManyField(
        CustomUser,
        through='LocationAccess',
        related_name='accessible_locations',
        blank=True
    )
    # VPN config fields for real-time config
    vpn_enabled = models.BooleanField(default=False)
    vpn_server = models.CharField(max_length=255, blank=True, null=True)
    vpn_client_config = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name


class LocationAccess(models.Model):
    """🔐 Location access control - Shinobi CCTV Django specific"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    granted_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = ('user', 'location')

    def __str__(self):
        return f"{self.user.username} - {self.location.name}"


class SystemHealth(models.Model):
    """📊 System health monitoring - Shinobi CCTV Django specific"""
    cpu_usage = models.FloatField(null=True, blank=True)
    memory_usage = models.FloatField(null=True, blank=True)
    storage_usage = models.FloatField(null=True, blank=True)
    vpn_connections = models.IntegerField(null=True, blank=True)
    online_cameras = models.IntegerField(null=True, blank=True)
    offline_cameras = models.IntegerField(null=True, blank=True)
    last_updated = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Health Check @ {self.last_updated.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        verbose_name_plural = "System Health Records"


class Incident(models.Model):
    """🚨 Incident management - Shinobi CCTV Django specific"""
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('closed', 'Closed'),
        ('investigating', 'Investigating'),
    ]
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    title = models.CharField(max_length=100, null=False)
    description = models.TextField(blank=True, null=True)
    camera = models.ForeignKey(Camera, on_delete=models.SET_NULL, null=True, blank=True, related_name='incidents')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='incidents')
    reporter = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='reported_incidents')
    snapshot_url = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="open")
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default="medium")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title


class VpnClient(models.Model):
    """🔐 VPN client management - Shinobi CCTV Django specific"""
    client_name = models.CharField(max_length=128)
    cert_cn = models.CharField(max_length=128, unique=True)
    ovpn_path = models.CharField(max_length=256)
    created_at = models.DateTimeField(auto_now_add=True)
    revoked = models.BooleanField(default=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    location = models.ForeignKey('Location', on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"{self.client_name} ({'revoked' if self.revoked else 'active'})"


class RecognitionAlert(models.Model):
    """🚨 Recognition alerts for security monitoring - Shinobi Django specific"""

    class AlertType(models.TextChoices):
        AUTHORIZED_ENTRY = 'authorized_entry', _('Authorized Entry')
        UNAUTHORIZED_ENTRY = 'unauthorized_entry', _('Unauthorized Entry')
        UNKNOWN_PERSON = 'unknown_person', _('Unknown Person')
        RECOGNITION_ERROR = 'recognition_error', _('Recognition Error')
        SUSPICIOUS_ACTIVITY = 'suspicious_activity', _('Suspicious Activity')

    class AlertStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        ACKNOWLEDGED = 'acknowledged', _('Acknowledged')
        RESOLVED = 'resolved', _('Resolved')
        FALSE_POSITIVE = 'false_positive', _('False Positive')

    # Links to existing models
    camera = models.ForeignKey(Camera, on_delete=models.CASCADE, related_name='recognition_alerts')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='recognition_alerts')
    person = models.ForeignKey(Person, on_delete=models.SET_NULL, null=True, blank=True)

    # Alert details
    alert_type = models.CharField(max_length=25, choices=AlertType.choices)
    alert_status = models.CharField(max_length=15, choices=AlertStatus.choices, default=AlertStatus.ACTIVE)
    confidence_score = models.FloatField(help_text="Recognition confidence (0.0 - 1.0)")

    # Face Recognition service data
    recognition_log_id = models.IntegerField(help_text="ID from Face Recognition service log")

    # Alert management
    acknowledged_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True, null=True)

    # Image data
    snapshot_url = models.URLField(blank=True, null=True)
    face_crop_path = models.CharField(max_length=500, blank=True, null=True)

    # Detection data
    bounding_box = models.JSONField(blank=True, null=True, help_text="Face bounding box [x1, y1, x2, y2]")

    # Metadata
    processing_time_ms = models.FloatField(blank=True, null=True)
    extra_data = models.JSONField(blank=True, null=True)

    # Timestamps
    timestamp = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'dashboard_recognition_alert'
        verbose_name = 'Recognition Alert'
        verbose_name_plural = 'Recognition Alerts'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['location', '-timestamp']),
            models.Index(fields=['camera', '-timestamp']),
            models.Index(fields=['alert_type', 'alert_status']),
            models.Index(fields=['person', '-timestamp']),
        ]

    def __str__(self):
        person_name = self.person.name if self.person else "Unknown"
        return f"{self.location.name} - {person_name} - {self.get_alert_type_display()}"

    def acknowledge(self, user, notes=None):
        """Acknowledge the alert"""
        from django.utils import timezone
        self.alert_status = self.AlertStatus.ACKNOWLEDGED
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        if notes:
            self.resolution_notes = notes
        self.save()


# =============================================================================
# 🎖️ SHINOBI CCTV DJANGO SPECIFIC EXTENSIONS
# =============================================================================
# Additional methods specific to Shinobi CCTV Django service

def has_access_to_location(self, location_id):
    """Check if user has access to a specific location"""
    if self.is_superuser or (self.role and self.role.name == "Administrator"):
        return True
    return self.accessible_locations.filter(id=location_id).exists()

def has_camera_access(self, camera):
    """Check if user has access to a specific camera."""
    if self.is_superuser or (self.role and self.role.name == "Administrator"):
        return True
    return self.camera_groups.filter(cameras=camera).exists()

# Add Shinobi CCTV Django specific methods to CustomUser
CustomUser.has_access_to_location = has_access_to_location
CustomUser.has_camera_access = has_camera_access

# Add location-specific access to Person model
def is_authorized_for_location(self, location):
    """Check if person is authorized for a specific location"""
    return self.authorized_locations.filter(id=location.id).exists()

# Add location relationship to Person model
Person.add_to_class('authorized_locations', models.ManyToManyField(Location, blank=True, related_name='authorized_persons'))
Person.is_authorized_for_location = is_authorized_for_location


# =============================================================================
# 🎯 OBJECT DETECTION SPECIFIC MODELS - MANAGED BY THIS SERVICE
# =============================================================================
# These models are MANAGED (managed=True) and create separate tables
# They link to existing shared models via foreign keys (Strategy 1)

class ObjectDetectionConfig(models.Model):
    """🎯 Object detection configuration per camera - MANAGED"""

    # Link to existing shared camera (UNMANAGED)
    camera = models.OneToOneField(
        Camera,
        on_delete=models.CASCADE,
        related_name='od_config',
        help_text="Link to shared camera"
    )

    # Detection configuration
    detection_config = models.JSONField(
        default=dict,
        blank=True,
        help_text="Object detection settings: enabled objects, thresholds, etc."
    )

    # Polygon zones for counting and speed detection
    polygon_zones = models.JSONField(
        default=dict,
        blank=True,
        help_text="Polygon zones for directional counting and speed detection"
    )

    # Speed detection settings
    speed_settings = models.JSONField(
        default=dict,
        blank=True,
        help_text="Speed detection configuration: distance, limits, etc."
    )

    # Detection mode settings
    detection_mode = models.CharField(
        max_length=20,
        choices=[
            ('horizontal', 'Horizontal Lines'),
            ('vertical', 'Vertical Lines'),
            ('both', 'Both Directions'),
            ('polygon', 'Polygon Zones')
        ],
        default='horizontal'
    )

    # Object filter settings
    enabled_objects = models.JSONField(
        default=list,
        blank=True,
        help_text="List of enabled object types: ['person', 'car', 'bus', 'truck', etc.]"
    )

    # Status and metadata
    is_active = models.BooleanField(default=True)
    last_detection = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True  # ✅ This service manages this table
        db_table = 'od_camera_configs'
        verbose_name = 'Object Detection Config'
        verbose_name_plural = 'Object Detection Configs'

    def __str__(self):
        return f"OD Config for {self.camera.name}"

    def get_default_config(self):
        """Get default detection configuration"""
        return {
            'confidence_threshold': 0.5,
            'iou_threshold': 0.45,
            'max_detections': 100,
            'track_objects': True,
            'save_crops': False
        }

    def get_default_speed_settings(self):
        """Get default speed detection settings"""
        return {
            'distance_meters': 30,
            'speed_limit_kmh': 50,
            'enable_speed_alerts': True,
            'horizontal_lines': {},
            'vertical_lines': {}
        }


class ObjectDetectionEvent(models.Model):
    """📊 Object detection events - MANAGED"""

    # Link to existing shared camera
    camera = models.ForeignKey(
        Camera,
        on_delete=models.CASCADE,
        related_name='od_events'
    )

    # Detection details
    object_type = models.CharField(
        max_length=50,
        help_text="Detected object type: person, car, bus, truck, bicycle, etc."
    )

    # Tracking information
    object_id = models.IntegerField(
        help_text="Tracking ID for multi-object tracking"
    )

    # Movement direction
    direction = models.CharField(
        max_length=10,
        choices=[
            ('in', 'In'),
            ('out', 'Out'),
            ('left', 'Left'),
            ('right', 'Right'),
            ('up', 'Up'),
            ('down', 'Down')
        ]
    )

    # Speed information (for vehicles)
    speed_kmh = models.FloatField(
        null=True,
        blank=True,
        help_text="Calculated speed in km/h"
    )

    # Detection confidence
    confidence = models.FloatField(
        help_text="Detection confidence (0.0 - 1.0)"
    )

    # Bounding box information
    bounding_box = models.JSONField(
        help_text="Bounding box coordinates [x1, y1, x2, y2]"
    )

    # Zone information
    zone_name = models.CharField(
        max_length=100,
        blank=True,
        help_text="Name of the detection zone"
    )

    # Processing metadata
    processing_time_ms = models.FloatField(
        null=True,
        blank=True,
        help_text="Processing time in milliseconds"
    )

    # Timestamp
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = True  # ✅ This service manages this table
        db_table = 'od_detection_events'
        verbose_name = 'Object Detection Event'
        verbose_name_plural = 'Object Detection Events'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['camera', '-timestamp']),
            models.Index(fields=['object_type', '-timestamp']),
            models.Index(fields=['direction', '-timestamp']),
        ]

    def __str__(self):
        speed_info = f" ({self.speed_kmh:.1f} km/h)" if self.speed_kmh else ""
        return f"{self.camera.name} - {self.object_type} {self.direction}{speed_info}"


class ObjectDetectionStatistics(models.Model):
    """📈 Object detection statistics - MANAGED"""

    # Link to existing shared camera
    camera = models.ForeignKey(
        Camera,
        on_delete=models.CASCADE,
        related_name='od_statistics'
    )

    # Time period
    date = models.DateField(default=timezone.now)
    hour = models.IntegerField(
        help_text="Hour of the day (0-23) for hourly statistics"
    )

    # Object counts by type and direction
    object_counts = models.JSONField(
        default=dict,
        help_text="Object counts: {'person': {'in': 10, 'out': 8}, 'car': {'in': 25, 'out': 23}}"
    )

    # Speed statistics
    speed_stats = models.JSONField(
        default=dict,
        help_text="Speed statistics: {'avg_speed': 45.2, 'max_speed': 78.5, 'violations': 3}"
    )

    # Total counts
    total_detections = models.IntegerField(default=0)
    total_violations = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True  # ✅ This service manages this table
        db_table = 'od_statistics'
        verbose_name = 'Object Detection Statistics'
        verbose_name_plural = 'Object Detection Statistics'
        unique_together = ['camera', 'date', 'hour']
        ordering = ['-date', '-hour']

    def __str__(self):
        return f"{self.camera.name} - {self.date} {self.hour:02d}:00"


# =============================================================================
# 🎯 OBJECT DETECTION EXTENSIONS TO EXISTING MODELS
# =============================================================================
# Add object detection methods to existing Camera model

def get_od_config(self):
    """Get object detection configuration for this camera"""
    config, created = ObjectDetectionConfig.objects.get_or_create(
        camera=self,
        defaults={
            'detection_config': {
                'confidence_threshold': 0.5,
                'iou_threshold': 0.45,
                'max_detections': 100,
                'track_objects': True
            },
            'speed_settings': {
                'distance_meters': 30,
                'speed_limit_kmh': 50,
                'enable_speed_alerts': True
            },
            'enabled_objects': ['person', 'car', 'bus', 'truck', 'bicycle', 'motorcycle']
        }
    )
    return config

def set_polygon_zones(self, zones):
    """Set polygon zones for object detection"""
    config = self.get_od_config()
    config.polygon_zones = zones
    config.save()
    return config

def get_detection_stats(self, date=None, hour=None):
    """Get detection statistics for this camera"""
    if date is None:
        date = timezone.now().date()

    if hour is not None:
        return ObjectDetectionStatistics.objects.filter(
            camera=self,
            date=date,
            hour=hour
        ).first()
    else:
        return ObjectDetectionStatistics.objects.filter(
            camera=self,
            date=date
        )

def is_od_enabled(self):
    """Check if object detection is enabled for this camera"""
    try:
        config = self.od_config
        return config.is_active
    except ObjectDetectionConfig.DoesNotExist:
        return False

# Add object detection methods to existing Camera model
Camera.get_od_config = get_od_config
Camera.set_polygon_zones = set_polygon_zones
Camera.get_detection_stats = get_detection_stats
Camera.is_od_enabled = is_od_enabled
