# PowerShell script to clean and rebuild Docker services
# This script will remove all existing containers, images, volumes, and networks
# Then rebuild everything from scratch

Write-Host "🧹 Starting Docker Environment Cleanup and Rebuild" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is running
Write-Status "Checking if Docker is running..."
try {
    docker version | Out-Null
    Write-Success "Docker is running!"
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop first."
    exit 1
}

# Step 1: Stop and remove all containers from this project
Write-Status "Stopping all containers for this project..."
docker-compose down --remove-orphans

Write-Status "Removing all containers related to this project..."
$containers = docker ps -a --filter "name=django_web" --filter "name=shinobi_cctv_django" --filter "name=postgres_db_django" --filter "name=redis_cache" --filter "name=shinobi-nvr" --filter "name=shinobi_mariadb" --filter "name=openvpn" --filter "name=pgadmin" -q
if ($containers) {
    docker rm -f $containers
    Write-Success "Removed project containers"
} else {
    Write-Warning "No project containers found to remove"
}

# Step 2: Remove project-specific images
Write-Status "Removing project-specific images..."
$images = docker images --filter "reference=django_cctv*" --filter "reference=*django_web*" --filter "reference=*shinobi_cctv_django*" -q
if ($images) {
    docker rmi -f $images
    Write-Success "Removed project images"
} else {
    Write-Warning "No project images found to remove"
}

# Step 3: Remove project volumes
Write-Status "Removing project volumes..."
$volumes = docker volume ls --filter "name=django_cctv" -q
if ($volumes) {
    docker volume rm $volumes
    Write-Success "Removed project volumes"
} else {
    Write-Warning "No project volumes found to remove"
}

# Step 4: Remove project networks
Write-Status "Removing project networks..."
$networks = docker network ls --filter "name=django_cctv" --filter "name=cctv_net" -q
if ($networks) {
    docker network rm $networks
    Write-Success "Removed project networks"
} else {
    Write-Warning "No project networks found to remove"
}

# Step 5: Clean up dangling images and build cache
Write-Status "Cleaning up dangling images and build cache..."
docker image prune -f
docker builder prune -f
Write-Success "Cleaned up dangling resources"

# Step 6: Show current Docker status
Write-Status "Current Docker status after cleanup:"
Write-Host "Containers:" -ForegroundColor Yellow
docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
Write-Host "`nImages:" -ForegroundColor Yellow
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
Write-Host "`nVolumes:" -ForegroundColor Yellow
docker volume ls
Write-Host "`nNetworks:" -ForegroundColor Yellow
docker network ls

Write-Host "`n" -ForegroundColor Green
Write-Success "Docker environment cleanup completed!"
Write-Host "=================================================" -ForegroundColor Cyan

# Step 7: Rebuild services
Write-Status "Starting fresh build of all services..."
Write-Host "This may take several minutes..." -ForegroundColor Yellow

try {
    # Build and start services
    Write-Status "Building and starting services with docker-compose..."
    docker-compose up -d --build --force-recreate
    
    Write-Status "Waiting for services to initialize (60 seconds)..."
    Start-Sleep -Seconds 60
    
    # Check if database is ready
    Write-Status "Checking database connectivity..."
    $dbReady = $false
    for ($i = 1; $i -le 10; $i++) {
        try {
            docker-compose exec -T db pg_isready -U user -d warehouse_shinobi
            if ($LASTEXITCODE -eq 0) {
                $dbReady = $true
                break
            }
        } catch {
            Write-Warning "Database not ready yet, attempt $i/10..."
            Start-Sleep -Seconds 10
        }
    }
    
    if ($dbReady) {
        Write-Success "Database is ready!"
        
        # Run migrations
        Write-Status "Running migrations for django_web..."
        docker-compose exec -T web python manage.py migrate
        
        Write-Status "Running migrations for shinobi_cctv_django..."
        docker-compose exec -T shinobi_cctv_django python manage.py migrate
        
        # Collect static files
        Write-Status "Collecting static files for django_web..."
        docker-compose exec -T web python manage.py collectstatic --noinput
        
        Write-Status "Collecting static files for shinobi_cctv_django..."
        docker-compose exec -T shinobi_cctv_django python manage.py collectstatic --noinput --clear
        
        Write-Success "All services built and configured successfully!"
        
    } else {
        Write-Error "Database failed to become ready. Check the logs with: docker-compose logs db"
    }
    
} catch {
    Write-Error "Failed to build services: $_"
    Write-Host "Check the logs with: docker-compose logs" -ForegroundColor Yellow
}

# Step 8: Show final status
Write-Host "`n=================================================" -ForegroundColor Cyan
Write-Status "Final service status:"
docker-compose ps

Write-Host "`n🎉 Rebuild Process Complete!" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host "Services are now available at:" -ForegroundColor Yellow
Write-Host "  • django_web: http://localhost:8000" -ForegroundColor White
Write-Host "  • shinobi_cctv_django: http://localhost:5000" -ForegroundColor White
Write-Host "  • pgAdmin: http://localhost:5050 (<EMAIL> / admin123)" -ForegroundColor White
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "  1. Create superusers: docker-compose exec web python manage.py createsuperuser" -ForegroundColor White
Write-Host "  2. Create superusers: docker-compose exec shinobi_cctv_django python manage.py createsuperuser" -ForegroundColor White
Write-Host "  3. Test the services: python verify_setup.py" -ForegroundColor White
