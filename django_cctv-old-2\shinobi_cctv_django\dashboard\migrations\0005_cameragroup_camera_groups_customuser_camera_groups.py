# Generated by Django 5.2 on 2025-06-09 19:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0004_alter_vpnclient_client_name_alter_vpnclient_location_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CameraGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='camera',
            name='groups',
            field=models.ManyToManyField(blank=True, related_name='cameras', to='dashboard.cameragroup'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='camera_groups',
            field=models.ManyToManyField(blank=True, related_name='users', to='dashboard.cameragroup'),
        ),
    ]
