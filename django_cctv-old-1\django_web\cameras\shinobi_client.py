"""
Shinobi NVR API Client for django_web service
Provides integration with Shinobi NVR for camera management and live streaming
"""

import requests
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


class ShinobiClient:
    """Client for interacting with Shinobi NVR API"""
    
    def __init__(self):
        self.host = getattr(settings, 'SHINOBI_API_URL', 'http://shinobi-nvr:8080')
        self.api_key = getattr(settings, 'SHINOBI_API_KEY', None)
        self.group_key = getattr(settings, 'SHINOBI_GROUP_KEY', None)
        
        if not self.api_key or not self.group_key:
            logger.warning("Shinobi API credentials not configured properly")
    
    def get_monitors(self):
        """Get all monitors from Shinobi"""
        url = f"{self.host}/{self.group_key}/monitor"
        try:
            response = requests.get(
                url,
                params={"api": self.api_key},
                timeout=10
            )
            if response.status_code == 200:
                return True, response.json()
            else:
                logger.warning(f"Shinobi API not available (status {response.status_code}). Using database fallback.")
                return self._get_monitors_from_database()
        except requests.exceptions.Timeout:
            logger.warning("Shinobi API timeout. Using database fallback.")
            return self._get_monitors_from_database()
        except Exception as e:
            logger.warning(f"Shinobi API error: {str(e)}. Using database fallback.")
            return self._get_monitors_from_database()
    
    def get_monitor(self, monitor_id):
        """Get a specific monitor from Shinobi"""
        success, monitors = self.get_monitors()
        if success and isinstance(monitors, list):
            for monitor in monitors:
                if monitor.get('mid') == monitor_id:
                    return True, monitor
        return False, "Monitor not found"
    
    def get_stream_url(self, monitor_id):
        """Get the stream URL for a monitor"""
        if not monitor_id:
            return None
        
        # Return the embed URL for the monitor
        return f"{self.host}/{self.group_key}/embed/{monitor_id}"
    
    def get_snapshot_url(self, monitor_id):
        """Get the snapshot URL for a monitor"""
        if not monitor_id:
            return None
        
        return f"{self.host}/{self.group_key}/jpeg/{monitor_id}/s.jpg"
    
    def _get_monitors_from_database(self):
        """Fallback method to get monitors directly from Shinobi database"""
        try:
            import pymysql
            
            # Connect to Shinobi database
            connection = pymysql.connect(
                host='shinobi_mariadb_for_shinobi',
                database='shinobi_db',
                user='shinobi',
                password='shinobi_password',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            
            cursor = connection.cursor()
            cursor.execute("""
                SELECT mid, ke, name, host, path, port, protocol, mode, type, ext, fps, width, height, details
                FROM Monitors 
                WHERE ke = %s
            """, (self.group_key,))
            
            monitors = cursor.fetchall()
            cursor.close()
            connection.close()
            
            # Convert to format similar to API response
            formatted_monitors = []
            for monitor in monitors:
                formatted_monitor = {
                    'mid': monitor['mid'],
                    'ke': monitor['ke'],
                    'name': monitor['name'],
                    'host': monitor['host'],
                    'path': monitor['path'],
                    'port': monitor['port'],
                    'protocol': monitor['protocol'],
                    'mode': monitor['mode'] or 'stop',
                    'type': monitor['type'],
                    'ext': monitor['ext'],
                    'fps': monitor['fps'],
                    'width': monitor['width'],
                    'height': monitor['height'],
                    'details': monitor['details'],
                    'status': 'stop',  # Default status since we can't get real-time status
                    'stream_url': f"{self.host}/{monitor['ke']}/embed/{monitor['mid']}"
                }
                formatted_monitors.append(formatted_monitor)
            
            logger.info(f"Retrieved {len(formatted_monitors)} monitors from database fallback")
            return True, formatted_monitors
            
        except Exception as e:
            logger.error(f"Database fallback failed: {str(e)}")
            return False, f"Database fallback failed: {str(e)}"
    
    def start_monitor(self, monitor_id):
        """Start recording for a monitor"""
        url = f"{self.host}/{self.group_key}/monitor/{monitor_id}/start"
        try:
            response = requests.get(
                url,
                params={"api": self.api_key},
                timeout=10
            )
            if response.status_code == 200:
                return True, "Monitor started successfully"
            else:
                return False, f"Failed to start monitor: {response.text}"
        except Exception as e:
            logger.error(f"Error starting monitor: {str(e)}")
            return False, str(e)
    
    def stop_monitor(self, monitor_id):
        """Stop recording for a monitor"""
        url = f"{self.host}/{self.group_key}/monitor/{monitor_id}/stop"
        try:
            response = requests.get(
                url,
                params={"api": self.api_key},
                timeout=10
            )
            if response.status_code == 200:
                return True, "Monitor stopped successfully"
            else:
                return False, f"Failed to stop monitor: {response.text}"
        except Exception as e:
            logger.error(f"Error stopping monitor: {str(e)}")
            return False, str(e)


# Global instance for easy access
shinobi_client = ShinobiClient()
