"""
🎖️ FACE RECOGNITION VIEWS - SHIN<PERSON>BI CCTV DJANGO - UPDATED FOR SHARED MODELS
🎯 OPERATION: FACE RECOGNITION EMPIRE - PHASE 3
⚔️ TACTICAL SHINOBI VIEWS FOR FACE RECOGNITION WITH UNIFIED DATABASE
"""

import logging
import json
import time
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods, require_POST, require_GET
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.utils import timezone
from django.conf import settings
from datetime import timedelta, date

from .models import Location, RecognitionA<PERSON>t, Camera, Person, CameraRecognitionEvent

# Create aliases for compatibility
RecognizedPerson = Person

# Placeholder for missing models and functions
class FaceRecognitionConfig:
    """Placeholder for face recognition configuration"""
    objects = None

def get_shinobi_face_recognition_client():
    """Get the real face recognition client"""
    from .face_recognition_client import ShinobiFaceRecognitionClient
    return ShinobiFaceRecognitionClient()

logger = logging.getLogger(__name__)

# Note: Face Recognition Client integration will be implemented in Phase 4
# For now, these views provide the UI structure for the shared models

def is_admin_check(user):
    """Check if user is admin in Shinobi system"""
    return user.is_superuser or (user.role and user.role.name == 'Administrator')

# 👥 Shared Persons Views (Updated for Unified Database)

@login_required
def recognized_persons_list(request):
    """📋 List recognized persons in Shinobi system - SHARED MODEL"""

    user = request.user

    # Filter by user's accessible locations
    if is_admin_check(user):
        accessible_locations = Location.objects.all()
        persons = Person.objects.all()
    else:
        accessible_locations = user.accessible_locations.all()
        # Filter persons by authorized locations
        persons = Person.objects.filter(authorized_locations__in=accessible_locations).distinct()

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        persons = persons.filter(
            Q(name__icontains=search_query) |
            Q(employee_id__icontains=search_query)
        )

    # Filter by location
    location_filter = request.GET.get('location', '')
    if location_filter:
        persons = persons.filter(authorized_locations__id=location_filter)

    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter:
        persons = persons.filter(status=status_filter)

    # Order by last sync
    persons = persons.order_by('-last_sync')

    # Pagination
    paginator = Paginator(persons, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'location_filter': location_filter,
        'status_filter': status_filter,
        'accessible_locations': accessible_locations,
        'total_persons': persons.count(),
    }

    return render(request, 'dashboard/face_recognition/persons_list.html', context)

@login_required
def recognized_person_detail(request, person_id):
    """👤 Recognized person detail view"""
    
    person = get_object_or_404(RecognizedPerson, id=person_id)
    user = request.user
    
    # Check permissions
    if not is_admin_check(user):
        if person.location not in user.accessible_locations.all():
            messages.error(request, "You don't have permission to view this person.")
            return redirect('dashboard:recognized_persons_list')
    
    # Get recent recognition events
    recent_events = person.recognition_events.select_related('camera', 'location').order_by('-timestamp')[:20]
    
    # Get statistics
    total_recognitions = person.recognition_events.count()
    last_recognition = person.recognition_events.first()
    
    # Get daily activity for last 30 days
    thirty_days_ago = timezone.now() - timedelta(days=30)
    daily_activity = person.recognition_events.filter(
        timestamp__gte=thirty_days_ago
    ).extra(
        select={'day': 'date(timestamp)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    context = {
        'person': person,
        'recent_events': recent_events,
        'total_recognitions': total_recognitions,
        'last_recognition': last_recognition,
        'daily_activity': daily_activity,
    }
    
    return render(request, 'dashboard/face_recognition/person_detail.html', context)

# 📊 Recognition Analytics Views

@login_required
def face_recognition_dashboard(request):
    """📊 Face recognition analytics dashboard for Shinobi"""
    
    user = request.user
    
    # Get accessible locations
    if is_admin_check(user):
        accessible_locations = Location.objects.all()
    else:
        accessible_locations = user.accessible_locations.all()
    
    # Get face recognition service status
    face_client = get_shinobi_face_recognition_client()
    success, health_data = face_client.health_check()
    service_healthy = success
    
    # Get statistics for accessible locations
    total_persons = RecognizedPerson.objects.filter(location__in=accessible_locations).count()
    total_events = CameraRecognitionEvent.objects.filter(location__in=accessible_locations).count()
    pending_alerts = RecognitionAlert.objects.filter(
        location__in=accessible_locations,
        status='pending'
    ).count()
    
    # Recent events
    recent_events = CameraRecognitionEvent.objects.filter(
        location__in=accessible_locations
    ).select_related('recognized_person', 'camera', 'location').order_by('-timestamp')[:10]
    
    # Top recognized persons (last 7 days)
    week_ago = timezone.now() - timedelta(days=7)
    top_persons = RecognizedPerson.objects.filter(
        location__in=accessible_locations,
        recognition_events__timestamp__gte=week_ago
    ).annotate(
        event_count=Count('recognition_events')
    ).order_by('-event_count')[:5]
    
    # Location activity
    location_activity = accessible_locations.filter(
        recognition_events__timestamp__gte=week_ago
    ).annotate(
        event_count=Count('recognition_events')
    ).order_by('-event_count')[:5]
    
    # Get analytics from microservice
    microservice_analytics = {}
    if service_healthy:
        for location in accessible_locations:
            analytics = face_client.get_recognition_analytics(location_id=location.id)
            if analytics.get('recognition_ready'):
                microservice_analytics[location.id] = analytics
    
    context = {
        'total_persons': total_persons,
        'total_events': total_events,
        'pending_alerts': pending_alerts,
        'service_healthy': service_healthy,
        'health_data': health_data if service_healthy else None,
        'recent_events': recent_events,
        'top_persons': top_persons,
        'location_activity': location_activity,
        'microservice_analytics': microservice_analytics,
        'accessible_locations': accessible_locations,
    }
    
    return render(request, 'dashboard/face_recognition/dashboard.html', context)

@login_required
def recognition_events_list(request):
    """📋 List recognition events"""
    
    user = request.user
    
    # Filter events based on user permissions
    if is_admin_check(user):
        events = CameraRecognitionEvent.objects.all()
        accessible_locations = Location.objects.all()
    else:
        accessible_locations = user.accessible_locations.all()
        events = CameraRecognitionEvent.objects.filter(location__in=accessible_locations)
    
    # Filters
    event_type = request.GET.get('event_type', '')
    if event_type:
        events = events.filter(event_type=event_type)
    
    location_id = request.GET.get('location', '')
    if location_id:
        events = events.filter(location_id=location_id)
    
    camera_id = request.GET.get('camera', '')
    if camera_id:
        events = events.filter(camera_id=camera_id)
    
    person_id = request.GET.get('person', '')
    if person_id:
        events = events.filter(recognized_person_id=person_id)
    
    # Date range
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    if date_from:
        events = events.filter(timestamp__date__gte=date_from)
    if date_to:
        events = events.filter(timestamp__date__lte=date_to)
    
    # Order and paginate
    events = events.select_related('recognized_person', 'camera', 'location').order_by('-timestamp')
    
    paginator = Paginator(events, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    cameras = Camera.objects.filter(location__in=accessible_locations)
    persons = RecognizedPerson.objects.filter(location__in=accessible_locations)
    
    context = {
        'page_obj': page_obj,
        'event_types': CameraRecognitionEvent.EventType.choices,
        'accessible_locations': accessible_locations,
        'cameras': cameras,
        'persons': persons,
        'filters': {
            'event_type': event_type,
            'location': location_id,
            'camera': camera_id,
            'person': person_id,
            'date_from': date_from,
            'date_to': date_to,
        }
    }
    
    return render(request, 'dashboard/face_recognition/events_list.html', context)

@login_required
def recognition_alerts_list(request):
    """🚨 List recognition alerts"""
    
    user = request.user
    
    # Filter alerts based on user permissions
    if is_admin_check(user):
        alerts = RecognitionAlert.objects.all()
        accessible_locations = Location.objects.all()
    else:
        accessible_locations = user.accessible_locations.all()
        alerts = RecognitionAlert.objects.filter(location__in=accessible_locations)
    
    # Filter by status
    status_filter = request.GET.get('status', 'pending')
    if status_filter:
        alerts = alerts.filter(status=status_filter)
    
    # Filter by alert type
    alert_type = request.GET.get('alert_type', '')
    if alert_type:
        alerts = alerts.filter(alert_type=alert_type)
    
    # Filter by location
    location_id = request.GET.get('location', '')
    if location_id:
        alerts = alerts.filter(location_id=location_id)
    
    # Order and paginate
    alerts = alerts.select_related('location', 'camera', 'recognition_event').order_by('-created_at')
    
    paginator = Paginator(alerts, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'alert_statuses': RecognitionAlert.AlertStatus.choices,
        'alert_types': RecognitionAlert.AlertType.choices,
        'accessible_locations': accessible_locations,
        'filters': {
            'status': status_filter,
            'alert_type': alert_type,
            'location': location_id,
        }
    }
    
    return render(request, 'dashboard/face_recognition/alerts_list.html', context)

# ⚙️ Configuration Views

@login_required
def face_recognition_config(request, location_id):
    """⚙️ Face recognition configuration for location"""
    
    location = get_object_or_404(Location, id=location_id)
    user = request.user
    
    # Check permissions
    if not is_admin_check(user):
        if location not in user.accessible_locations.all():
            messages.error(request, "You don't have permission to configure this location.")
            return redirect('dashboard:face_recognition_dashboard')
    
    # Get or create config
    config, created = FaceRecognitionConfig.objects.get_or_create(location=location)
    
    if request.method == 'POST':
        try:
            # Update configuration
            config.enabled = request.POST.get('enabled') == 'on'
            config.detection_confidence = float(request.POST.get('detection_confidence', 0.25))
            config.recognition_confidence = float(request.POST.get('recognition_confidence', 0.5))
            config.processing_interval = int(request.POST.get('processing_interval', 10))
            config.max_faces_per_frame = int(request.POST.get('max_faces_per_frame', 5))
            config.store_unknown_faces = request.POST.get('store_unknown_faces') == 'on'
            config.retention_days = int(request.POST.get('retention_days', 30))
            config.alert_on_unknown = request.POST.get('alert_on_unknown') == 'on'
            config.alert_on_frequent_visitor = request.POST.get('alert_on_frequent_visitor') == 'on'
            
            config.save()
            messages.success(request, f"Face recognition configuration updated for {location.name}")
            
        except (ValueError, TypeError) as e:
            messages.error(request, f"Invalid configuration values: {str(e)}")
    
    context = {
        'location': location,
        'config': config,
    }
    
    return render(request, 'dashboard/face_recognition/config.html', context)

# 🎯 API Endpoints

@login_required
@require_GET
def api_face_recognition_status(request):
    """🩺 Get face recognition service status"""
    
    face_client = get_shinobi_face_recognition_client()
    success, health_data = face_client.health_check()
    
    return JsonResponse({
        'service_available': success,
        'health_data': health_data if success else None,
        'timestamp': time.time()
    })

@login_required
@require_POST
@csrf_exempt
def api_acknowledge_alert(request, alert_id):
    """✅ Acknowledge recognition alert"""
    
    try:
        alert = get_object_or_404(RecognitionAlert, id=alert_id)
        user = request.user
        
        # Check permissions
        if not is_admin_check(user):
            if alert.location not in user.accessible_locations.all():
                return JsonResponse({'error': 'Permission denied'}, status=403)
        
        alert.acknowledge(user)
        
        return JsonResponse({
            'success': True,
            'message': 'Alert acknowledged successfully'
        })
        
    except Exception as e:
        logger.error(f"Error acknowledging alert {alert_id}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@login_required
@require_POST
@csrf_exempt
def api_process_shinobi_frame(request):
    """📹 Process Shinobi camera frame for face recognition"""
    
    try:
        camera_id = request.POST.get('camera_id')
        monitor_id = request.POST.get('monitor_id')
        
        if not camera_id or not monitor_id:
            return JsonResponse({'error': 'Camera ID and Monitor ID required'}, status=400)
        
        camera = get_object_or_404(Camera, id=camera_id)
        user = request.user
        
        # Check permissions
        if not is_admin_check(user):
            if camera.location not in user.accessible_locations.all():
                return JsonResponse({'error': 'Permission denied'}, status=403)
        
        # Get image data
        if 'image' not in request.FILES:
            return JsonResponse({'error': 'Image file required'}, status=400)
        
        image_file = request.FILES['image']
        image_data = image_file.read()
        
        # Process with face recognition service
        face_client = get_shinobi_face_recognition_client()
        results = face_client.process_shinobi_frame(
            camera_id=camera.id,
            monitor_id=monitor_id,
            image_data=image_data
        )
        
        # Store recognition events
        if results['success'] and results['faces_recognized'] > 0:
            for recognition in results.get('recognitions', []):
                if recognition.get('recognition', {}).get('is_match'):
                    # Find or create recognized person
                    person_name = recognition['recognition'].get('person_name')
                    if person_name and person_name != 'Unknown':
                        person, created = RecognizedPerson.objects.get_or_create(
                            name=person_name,
                            location=camera.location,
                            defaults={
                                'status': RecognizedPerson.PersonStatus.VISITOR,
                                'recognition_count': 0
                            }
                        )
                        
                        # Update recognition count
                        person.recognition_count += 1
                        person.last_seen = timezone.now()
                        person.save()
                        
                        # Create recognition event
                        event = CameraRecognitionEvent.objects.create(
                            event_type='recognition',
                            camera=camera,
                            location=camera.location,
                            recognized_person=person,
                            confidence_score=recognition['recognition'].get('confidence', 0),
                            bounding_box=recognition.get('bbox'),
                            shinobi_monitor_id=monitor_id,
                            processing_time_ms=results.get('processing_time_ms'),
                            metadata={'microservice_results': results}
                        )
                        
                        # Check for alerts
                        config = FaceRecognitionConfig.objects.filter(location=camera.location).first()
                        if config and config.alert_on_frequent_visitor and person.is_frequent_visitor:
                            RecognitionAlert.objects.create(
                                alert_type='frequent_visitor',
                                location=camera.location,
                                camera=camera,
                                recognition_event=event,
                                title=f"Frequent Visitor Detected",
                                message=f"{person.name} has been recognized {person.recognition_count} times",
                                severity='medium'
                            )
        
        return JsonResponse(results)
        
    except Exception as e:
        logger.error(f"Error processing Shinobi frame: {e}")
        return JsonResponse({'error': str(e)}, status=500)

logger.info("🎭 Face Recognition Views loaded for Shinobi CCTV Django")
logger.info("👥 Recognized persons views ready")
logger.info("📊 Recognition analytics views ready")
logger.info("🚨 Alert management views ready")
logger.info("🎯 Shinobi API endpoints ready")
