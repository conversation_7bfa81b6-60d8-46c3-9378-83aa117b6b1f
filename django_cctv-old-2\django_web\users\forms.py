from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm, AuthenticationForm
from .models import User

class UserCreationForm(UserCreationForm):
    """Custom form for creating new users."""
    
    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'role')
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['email'].required = True

class UserChangeForm(UserChangeForm):
    """Custom form for updating users."""
    
    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'role', 'phone_number', 'profile_image', 'camera_groups')

class LoginForm(AuthenticationForm):
    """Custom login form with enhanced styling."""
    
    username = forms.CharField(widget=forms.TextInput(attrs={
        'class': 'w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600',
        'placeholder': 'Username',
    }))
    password = forms.CharField(widget=forms.PasswordInput(attrs={
        'class': 'w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-600',
        'placeholder': 'Password',
    }))

class ProfileUpdateForm(forms.ModelForm):
    """Form for users to update their profile information."""
    
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'phone_number', 'profile_image']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'last_name': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'email': forms.EmailInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'phone_number': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'profile_image': forms.FileInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'})
        }