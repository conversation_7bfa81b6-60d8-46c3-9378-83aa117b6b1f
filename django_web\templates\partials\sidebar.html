{% load static %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'index' %}active{% endif %}" href="{% url 'dashboard:index' %}">
                    <i class="bi bi-speedometer2"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'cameras/live' in request.path %}active{% endif %}" href="{% url 'cameras:live_monitors' %}">
                    <i class="bi bi-camera-video"></i>
                    Live Camera Monitors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'cameras/events' in request.path %}active{% endif %}" href="{% url 'cameras:event_list' %}">
                    <i class="bi bi-exclamation-triangle"></i>
                    Events
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'cameras/' in request.path and 'live' not in request.path and 'events' not in request.path %}active{% endif %}" href="{% url 'cameras:camera_list' %}">
                    <i class="bi bi-camera-video-fill"></i>
                    Cameras
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'cameras/create' in request.path %}active{% endif %}" href="{% url 'cameras:camera_create' %}">
                    <i class="bi bi-plus-circle"></i>
                    Add Camera
                </a>
            </li>

            {% if request.user.is_admin %}
            <li class="nav-item">
                <a class="nav-link {% if 'cameras/groups' in request.path %}active{% endif %}" href="{% url 'cameras:group_list' %}">
                    <i class="bi bi-collection"></i>
                    Camera Groups
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if 'cameras/groups/create' in request.path %}active{% endif %}" href="{% url 'cameras:group_create' %}">
                    <i class="bi bi-plus-circle"></i>
                    Add Group
                </a>
            </li>
            {% endif %}

            <li class="nav-item">
                <a class="nav-link {% if 'vpn' in request.path %}active{% endif %}" href="{% url 'vpn:client_list' %}">
                    <i class="bi bi-shield-lock"></i>
                    VPN Clients
                </a>
            </li>

            {% if request.user.is_admin %}
            <li class="nav-item">
                <a class="nav-link {% if 'users' in request.path %}active{% endif %}" href="{% url 'users:user_list' %}">
                    <i class="bi bi-people"></i>
                    Users
                </a>
            </li>
            {% endif %}

            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'system_status' %}active{% endif %}" href="{% url 'dashboard:system_status' %}">
                    <i class="bi bi-heart-pulse"></i>
                    System Status
                </a>
            </li>
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Quick Access</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'cameras:live_monitors' %}">
                    <i class="bi bi-play-circle"></i>
                    Live Monitors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'cameras:event_list' %}">
                    <i class="bi bi-clock-history"></i>
                    Recent Events
                </a>
            </li>
        </ul>
        
        <div class="p-3">
            <div class="mb-2">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-0 mt-2 mb-1 text-muted">
                    <span>System Status</span>
                </h6>
                
                <div class="d-flex justify-content-between align-items-center px-1 py-1 small">
                    <span>Cameras Online:</span>
                    <span class="badge bg-success">2/2</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center px-1 py-1 small">
                    <span>Server Load:</span>
                    <div class="progress" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center px-1 py-1 small">
                    <span>Storage:</span>
                    <div class="progress" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 70%" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="p-3 mt-auto">
            <div class="d-grid">
                <a href="#" class="btn btn-sm btn-outline-info">
                    <i class="bi bi-question-circle"></i> Help & Support
                </a>
            </div>
        </div>
    </div>
</nav>
