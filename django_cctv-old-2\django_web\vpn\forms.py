from django import forms
from .models import VpnClient

class VpnClientForm(forms.ModelForm):
    """Form for creating and updating VPN clients."""
    
    class Meta:
        model = VpnClient
        fields = ['name', 'description', 'client_id', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'description': forms.Textarea(attrs={'class': 'w-full px-4 py-2 border rounded-md', 'rows': 3}),
            'client_id': forms.TextInput(attrs={'class': 'w-full px-4 py-2 border rounded-md'}),
            'status': forms.Select(attrs={'class': 'w-full px-4 py-2 border rounded-md'})
        }