#!/bin/bash
# Shinobi Auto-Initialization Script
# This script automatically sets up Shinobi database and super admin

set -e

echo "📹 Starting Shinobi Auto-Initialization..."

# Configuration variables
DB_HOST="${DB_HOST:-shinobi_db}"
DB_USER="${DB_USER:-shinobi}"
DB_PASSWORD="${DB_PASSWORD:-shinobi_password}"
DB_DATABASE="${DB_DATABASE:-shinobi_db}"
SHINOBI_HOME="/home/<USER>"

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
until mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1" >/dev/null 2>&1; do
    echo "Database not ready, waiting..."
    sleep 5
done

echo "✅ Database connection established!"

# Check if already initialized
if mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_DATABASE" -e "SHOW TABLES LIKE 'Users';" 2>/dev/null | grep -q Users; then
    echo "✅ Shinobi already initialized (database tables exist). Skipping..."
    echo "📊 Existing database detected with tables:"
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_DATABASE" -e "SHOW TABLES;" 2>/dev/null | grep -E "(Users|Monitors|API)" || echo "  - Core tables present"
    exit 0
fi

echo "🏗️ Initializing Shinobi database..."

# Create database tables
mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_DATABASE" << 'EOF'
-- Shinobi Database Schema
CREATE TABLE IF NOT EXISTS `API` (
  `ke` varchar(50) NOT NULL,
  `uid` varchar(30) NOT NULL,
  `gid` varchar(30) NOT NULL,
  `permissions` text,
  `details` text,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `Cloud Timelapse Frames` (
  `ke` varchar(100) NOT NULL,
  `mid` varchar(50) NOT NULL,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `details` text,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `Cloud Videos` (
  `ke` varchar(100) NOT NULL,
  `mid` varchar(50) NOT NULL,
  `uid` varchar(30) NOT NULL,
  `gid` varchar(30) NOT NULL,
  `details` text,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `Events` (
  `ke` varchar(100) NOT NULL,
  `mid` varchar(50) NOT NULL,
  `uid` varchar(30) NOT NULL,
  `gid` varchar(30) NOT NULL,
  `details` text,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `Files` (
  `ke` varchar(100) NOT NULL,
  `mid` varchar(50) NOT NULL,
  `uid` varchar(30) NOT NULL,
  `gid` varchar(30) NOT NULL,
  `details` text,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `Monitors` (
  `ke` varchar(50) NOT NULL,
  `mid` varchar(50) NOT NULL,
  `uid` varchar(30) NOT NULL,
  `gid` varchar(30) NOT NULL,
  `details` text,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `Users` (
  `ke` varchar(50) NOT NULL,
  `uid` varchar(30) NOT NULL,
  `gid` varchar(30) NOT NULL,
  `details` text,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `Videos` (
  `ke` varchar(100) NOT NULL,
  `mid` varchar(50) NOT NULL,
  `uid` varchar(30) NOT NULL,
  `gid` varchar(30) NOT NULL,
  `details` text,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ke`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
EOF

echo "✅ Database tables created!"

# Create super admin user
echo "👤 Creating super admin user..."
SUPER_ADMIN_DETAILS='{
    "mail": "<EMAIL>",
    "pass": "sU5EjCH63wRMSo048y1tOdvm3B6xGk",
    "details": {
        "factorAuth": "0",
        "sub": {
            "max": 10,
            "used": 0
        },
        "allmonitors": {
            "max": 10,
            "used": 0
        },
        "monitors": {
            "max": 10,
            "used": 0
        },
        "diskUsed": 0,
        "diskMax": "10000",
        "log_retention": 30,
        "size": "0",
        "days": "10"
    }
}'

mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_DATABASE" << EOF
INSERT INTO Users (ke, uid, gid, details) VALUES 
('<EMAIL>', 'admin', 'admin', '$SUPER_ADMIN_DETAILS')
ON DUPLICATE KEY UPDATE details = '$SUPER_ADMIN_DETAILS';
EOF

# Create API key
echo "🔑 Creating API key..."
API_DETAILS='{
    "auth_socket": "1",
    "get_monitors": "1",
    "control_monitors": "1",
    "get_logs": "1",
    "watch_stream": "1",
    "watch_snapshot": "1",
    "watch_videos": "1",
    "delete_videos": "1"
}'

mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_DATABASE" << EOF
INSERT INTO API (ke, uid, gid, permissions, details) VALUES 
('vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx', 'admin', 'VqJe1awj1m', '$API_DETAILS', '{}')
ON DUPLICATE KEY UPDATE permissions = '$API_DETAILS';
EOF

echo "✅ Shinobi initialization complete!"
echo "👤 Super Admin: <EMAIL>"
echo "🔐 Password: sU5EjCH63wRMSo048y1tOdvm3B6xGk"
echo "🔑 API Key: vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx"
echo "🏷️ Group Key: VqJe1awj1m"
