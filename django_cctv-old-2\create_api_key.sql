-- Create API key for shinobi_cctv_django integration
USE shinobi_db;

-- Insert API key with full permissions for the user
INSERT INTO API (ke, uid, details) VALUES 
('vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx', '0pVtL79png', '{"auth_socket":"1","get_monitors":"1","control_monitors":"1","get_logs":"1","watch_stream":"1","watch_snapshot":"1","watch_videos":"1","delete_videos":"1"}')
ON DUPLICATE KEY UPDATE 
details = '{"auth_socket":"1","get_monitors":"1","control_monitors":"1","get_logs":"1","watch_stream":"1","watch_snapshot":"1","watch_videos":"1","delete_videos":"1"}';

-- Verify the API key was created
SELECT ke, uid, details FROM API WHERE ke = 'vP8DOaT8a8FPPCQUS5n2xGMEmRCsnx';
