{% extends 'base.html' %}

{% block content %}
  <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-300">Add / Edit Camera</h1>
    <form method="post" enctype="multipart/form-data">
      {% csrf_token %}
      {{ form.non_field_errors }}
      <div class="space-y-4">
        {% for field in form.visible_fields %}
          <div>
            <label class="block text-gray-700 dark:text-gray-300 mb-2" for="{{ field.id_for_label }}">{{ field.label }}</label>
            {{ field }}
            {% if field.help_text %}
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ field.help_text }}</p>
            {% endif %}
            {% for error in field.errors %}
              <p class="text-xs text-red-600 dark:text-red-400">{{ error }}</p>
            {% endfor %}
          </div>
        {% endfor %}
      </div>
      <button type="submit" class="mt-6 px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition">Save</button>
    </form>
  </div>
{% endblock %}
