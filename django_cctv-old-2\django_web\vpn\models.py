from django.db import models
from django.utils.translation import gettext_lazy as _

class VpnClient(models.Model):
    """Model to track VPN clients (typically cameras or locations)."""
    
    class ClientStatus(models.TextChoices):
        ACTIVE = 'active', _('Active')
        INACTIVE = 'inactive', _('Inactive')
        REVOKED = 'revoked', _('Revoked')
    
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    client_id = models.CharField(max_length=100, unique=True)
    virtual_ip = models.GenericIPAddressField(blank=True, null=True)
    real_ip = models.GenericIPAddressField(blank=True, null=True)
    connected_since = models.DateTimeField(blank=True, null=True)
    last_seen = models.DateTimeField(blank=True, null=True)
    status = models.CharField(
        max_length=20,
        choices=ClientStatus.choices,
        default=ClientStatus.INACTIVE
    )
    config_file = models.FileField(upload_to='vpn_configs/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.client_id})"
    
    class Meta:
        ordering = ['name']

class VpnLog(models.Model):
    """Log of VPN connection events."""
    
    class EventType(models.TextChoices):
        CONNECT = 'connect', _('Connected')
        DISCONNECT = 'disconnect', _('Disconnected')
        AUTH_FAILURE = 'auth_failure', _('Authentication Failure')
        ERROR = 'error', _('Error')
        INFO = 'info', _('Information')
    
    client = models.ForeignKey(VpnClient, on_delete=models.CASCADE, related_name='logs', null=True, blank=True)
    event_type = models.CharField(
        max_length=20,
        choices=EventType.choices,
        default=EventType.INFO
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    message = models.TextField()
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    additional_data = models.JSONField(blank=True, null=True)
    
    def __str__(self):
        client_name = self.client.name if self.client else "Unknown"
        return f"{client_name} - {self.get_event_type_display()} at {self.timestamp}"
    
    class Meta:
        ordering = ['-timestamp']