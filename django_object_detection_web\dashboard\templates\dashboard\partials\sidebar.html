{% load static %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {% if page_name == 'dashboard' %}active{% endif %}" href="{% url 'dashboard:dashboard' %}">
                    <i class="bi bi-speedometer2"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if page_name == 'cameras' %}active{% endif %}" href="{% url 'dashboard:cameras' %}">
                    <i class="bi bi-camera-video"></i>
                    Cameras
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if page_name == 'locations' %}active{% endif %}" href="{% url 'dashboard:locations' %}">
                    <i class="bi bi-buildings"></i>
                    Locations
                </a>
            </li>
             <li class="nav-item">
                <a class="nav-link {% if page_name == 'incidents' %}active{% endif %}" href="{% url 'dashboard:incidents_list' %}">
                    <i class="bi bi-exclamation-triangle"></i>
                    Incidents
                </a>
            </li>

            {# 🎯 Object Detection Navigation #}
            <li class="nav-item">
                <a class="nav-link {% if page_name == 'object_detection' %}active{% endif %}" href="{% url 'dashboard:object_detection_dashboard' %}">
                    <i class="bi bi-eye"></i>
                    Object Detection
                </a>
            </li>

            {# Use Django's 'and/or' logic without parentheses, as Django templates do not support parentheses in if tags #}
            {% if request.user.is_authenticated and request.user.is_superuser or request.user.is_authenticated and request.user.role and request.user.role.name == "Administrator" %}
            <li class="nav-item">
                <a class="nav-link {% if page_name == 'users' %}active{% endif %}" href="{% url 'dashboard:users' %}">
                    <i class="bi bi-people"></i>
                    Users
                </a>
            </li>
            {% endif %}
            
            <li class="nav-item">
                <a class="nav-link {% if page_name == 'system_health' %}active{% endif %}" href="{% url 'dashboard:system_health' %}">
                    <i class="bi bi-heart-pulse"></i>
                    System Health
                </a>
            </li>
            
            {% if request.user.is_authenticated and request.user.is_superuser or request.user.is_authenticated and request.user.role and request.user.role.name == "Administrator" %}
            <li class="nav-item">
                <a class="nav-link {% if page_name == 'settings' %}active{% endif %}" href="{% url 'dashboard:settings' %}">
                    <i class="bi bi-gear"></i>
                    Settings
                </a>
            </li>
            {% endif %}
        </ul>
        
        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>Quick Access (Dummy)</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link" href="#">
                    <i class="bi bi-clipboard-data"></i>
                    Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">
                    <i class="bi bi-alarm"></i>
                    Scheduled Tasks
                </a>
            </li>
        </ul>
        
        <div class="p-3">
            <div class="mb-2">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-0 mt-2 mb-1 text-muted">
                    <span>System Status</span>
                </h6>
                
                <div class="d-flex justify-content-between align-items-center px-1 py-1 small">
                    <span>VPN Connections:</span>
                    <span class="badge bg-success">Active</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center px-1 py-1 small">
                    <span>Server Load:</span>
                    <div class="progress" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center px-1 py-1 small">
                    <span>Storage:</span>
                    <div class="progress" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 70%" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="p-3 mt-auto"> <div class="d-grid">
                <a href="#" class="btn btn-sm btn-outline-info">
                    <i class="bi bi-question-circle"></i> Help & Support
                </a>
            </div>
        </div>
    </div>
</nav>