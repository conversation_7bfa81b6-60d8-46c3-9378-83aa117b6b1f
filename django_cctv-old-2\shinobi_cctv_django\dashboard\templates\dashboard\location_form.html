{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}{{ title }} - ABC CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ title }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'dashboard:locations' %}" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Locations
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Location Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Location Name *</label>
                        {{ form.name }} {# Assuming widget attrs like class="form-control" are set in forms.py #}
                        {% if form.name.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.name.errors %}<small>{{ error }}</small>{% endfor %}
                            </div>
                        {% endif %}
                        {% if form.name.help_text %}<div class="form-text">{{ form.name.help_text }}</div>{% endif %}
                        <div class="form-text">
                            Enter a descriptive name for this location (e.g., "North Warehouse", "Downtown Office")
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">Street Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.address.errors %}<small>{{ error }}</small>{% endfor %}
                            </div>
                        {% endif %}
                        {% if form.address.help_text %}<div class="form-text">{{ form.address.help_text }}</div>{% endif %}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.city.id_for_label }}" class="form-label">City</label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.city.errors %}<small>{{ error }}</small>{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.city.help_text %}<div class="form-text">{{ form.city.help_text }}</div>{% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.state.id_for_label }}" class="form-label">State/Province</label>
                            {{ form.state }}
                            {% if form.state.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.state.errors %}<small>{{ error }}</small>{% endfor %}
                                </div>
                            {% endif %}
                             {% if form.state.help_text %}<div class="form-text">{{ form.state.help_text }}</div>{% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.country.id_for_label }}" class="form-label">Country</label>
                            {{ form.country }}
                            {% if form.country.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.country.errors %}<small>{{ error }}</small>{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.country.help_text %}<div class="form-text">{{ form.country.help_text }}</div>{% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.zipcode.id_for_label }}" class="form-label">ZIP/Postal Code</label>
                            {{ form.zipcode }}
                            {% if form.zipcode.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.zipcode.errors %}<small>{{ error }}</small>{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.zipcode.help_text %}<div class="form-text">{{ form.zipcode.help_text }}</div>{% endif %}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'dashboard:locations' %}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                           {% if instance %}Update{% else %}Add{% endif %} Location
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        {% if instance %} {# Changed from title.startswith('Edit') to check for instance #}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Advanced Options</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>VPN Configuration</h6>
                    <p class="text-muted">Configure VPN connection settings for this location.</p>
                    <div class="d-grid">
                        <button class="btn btn-outline-primary" disabled>
                            <i class="bi bi-shield-lock"></i> Configure VPN (Feature not implemented)
                        </button>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6 class="text-danger">Danger Zone</h6>
                    <p class="text-muted">This action cannot be undone. Please be certain.</p>
                    <div class="d-grid">
                         <form method="POST" action="{% url 'dashboard:location_delete' location_id=instance.id %}" onsubmit="return confirm('Are you sure you want to delete this location and all its cameras?');">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="bi bi-trash"></i> Delete Location
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}