{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}System Health - ABC CCTV System{% endblock %}

{% block extra_css %}
{# system-health.html specific CSS if any, though custom.css covers gauge-container #}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">System Health</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-stats"> {# ID used by system-health.js #}
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<div class="alert alert-info">
    <i class="bi bi-info-circle-fill"></i> Last updated: <span id="refresh-timestamp">{{ health.last_updated|date:"H:i:s"|default:"N/A" }}</span>
</div>

<div class="row mb-4">
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header"><h5 class="mb-0">CPU Usage</h5></div>
            <div class="card-body text-center">
                <div class="gauge-container">
                    <canvas id="cpu-gauge" data-usage="{{ health.cpu_usage|default:0 }}"></canvas>
                </div>
                <h3 id="cpu-usage-text" class="mt-2">{{ health.cpu_usage|default:0|floatformat:1 }}%</h3>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header"><h5 class="mb-0">Memory Usage</h5></div>
            <div class="card-body text-center">
                <div class="gauge-container">
                    <canvas id="memory-gauge" data-usage="{{ health.memory_usage|default:0 }}"></canvas>
                </div>
                <h3 id="memory-usage-text" class="mt-2">{{ health.memory_usage|default:0|floatformat:1 }}%</h3>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header"><h5 class="mb-0">Storage Usage</h5></div>
            <div class="card-body text-center">
                <div class="gauge-container">
                    <canvas id="storage-gauge" data-usage="{{ health.storage_usage|default:0 }}"></canvas>
                </div>
                <h3 id="storage-usage-text" class="mt-2">{{ health.storage_usage|default:0|floatformat:1 }}%</h3>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header"><h5 class="mb-0">VPN Connections</h5></div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="display-3 me-3 text-primary"><i class="bi bi-shield-lock"></i></div>
                    <div>
                        <h1 id="vpn-connections">{{ health.vpn_connections|default:0 }}</h1>
                        <p class="mb-0 text-muted">Active connections</p>
                    </div>
                </div>
                <hr>
                <h6>Location Status</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead><tr><th>Location</th><th>Status</th><th>Last Online</th></tr></thead>
                        <tbody>
                            {% for location in locations %}
                            <tr>
                                <td>{{ location.name }}</td>
                                <td>
                                    <span class="vpn-status {% if location.vpn_status %}vpn-connected{% else %}vpn-disconnected{% endif %}"
                                          data-vpn-status-loc="{{ location.id }}"> {# Used by JS if API provided individual statuses #}
                                        <i class="bi {% if location.vpn_status %}bi-shield-check{% else %}bi-shield-x{% endif %}"></i>
                                        {% if location.vpn_status %}Connected{% else %}Disconnected{% endif %}
                                    </span>
                                </td>
                                <td>{{ location.last_online|date:"Y-m-d H:i"|default:"Never" }}</td>
                            </tr>
                            {% empty %}
                            <tr><td colspan="3" class="text-center text-muted py-3">No locations.</td></tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header"><h5 class="mb-0">Camera Status</h5></div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-4"><h3 id="total-cameras">{{ total_cameras|default:0 }}</h3><p class="mb-0 text-muted small">Total</p></div>
                    <div class="col-4"><h3 class="text-success" id="online-cameras">{{ online_cameras|default:0 }}</h3><p class="mb-0 text-muted small">Online</p></div>
                    <div class="col-4"><h3 class="text-danger" id="offline-cameras">{{ offline_cameras|default:0 }}</h3><p class="mb-0 text-muted small">Offline</p></div>
                </div>
                {% if total_cameras > 0 %}
                    {% widthratio online_cameras total_cameras 100 as online_percentage %}
                    {% widthratio offline_cameras total_cameras 100 as offline_percentage %}
                {% else %}
                    {% with 0 as online_percentage %}
                        {% with 0 as offline_percentage %}
                            <div class="progress mb-2" style="height: 20px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ online_percentage }}%" aria-valuenow="{{ online_cameras }}" aria-valuemin="0" aria-valuemax="{{ total_cameras }}">{{ online_cameras }}</div>
                                <div class="progress-bar bg-danger" role="progressbar" style="width: {{ offline_percentage }}%" aria-valuenow="{{ offline_cameras }}" aria-valuemin="0" aria-valuemax="{{ total_cameras }}">{{ offline_cameras }}</div>
                            </div>
                            <p class="text-center mb-3 small"><span id="online-cameras-percentage-text" class="badge bg-success">{{ online_percentage|floatformat:1 }}%</span> online</p>
                        {% endwith %}
                    {% endwith %}
                {% endif %}
                <div class="text-center">
                    <a href="{% url 'dashboard:cameras' %}" class="btn btn-outline-primary"><i class="bi bi-camera-video"></i> Manage Cameras</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header"><h5 class="mb-0">Network Infrastructure (Illustrative)</h5></div>
            <div class="card-body text-center">
                <img src="{% static 'dashboard/img/network_placeholder.png' %}" alt="Network Infrastructure" class="img-fluid rounded" style="max-height: 300px;">
                <p class="mt-2 text-muted small">Illustrative diagram of system connectivity.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script> {# Chart.js Library #}
<script src="{% static 'dashboard/js/system-health.js' %}"></script>
{% endblock %}