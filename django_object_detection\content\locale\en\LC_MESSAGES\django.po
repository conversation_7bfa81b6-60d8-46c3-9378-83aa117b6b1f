# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-08 16:35+0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
#: .\cameras\forms.py:19
msgid "Password"
msgstr ""

#: .\cameras\forms.py:27
msgid "Remember me"
msgstr ""

#: .\cameras\forms.py:37
msgid "You entered an invalid password."
msgstr ""

#: .\cameras\forms.py:43
msgid "Username"
msgstr ""

#: .\cameras\forms.py:56
msgid "You entered an invalid username."
msgstr ""

#: .\cameras\forms.py:59 .\cameras\forms.py:77 .\cameras\forms.py:107
msgid "This account is not active."
msgstr ""

#: .\cameras\forms.py:67 .\cameras\forms.py:128 .\cameras\forms.py:176
#: .\cameras\forms.py:219
msgid "Email"
msgstr ""

#: .\cameras\forms.py:74 .\cameras\forms.py:183
msgid "You entered an invalid email address."
msgstr ""

#: .\cameras\forms.py:93 .\cameras\forms.py:142
msgid "Email or Username"
msgstr ""

#: .\cameras\forms.py:103 .\cameras\forms.py:152
msgid "You entered an invalid email address or username."
msgstr ""

#: .\cameras\forms.py:128
msgid "Required. Enter an existing email address."
msgstr ""

#: .\cameras\forms.py:136
msgid "You can not use this email address."
msgstr ""

#: .\cameras\forms.py:156 .\cameras\forms.py:186
msgid "This account has already been activated."
msgstr ""

#: .\cameras\forms.py:160 .\cameras\forms.py:190
msgid "Activation code not found."
msgstr ""

#: .\cameras\forms.py:166 .\cameras\forms.py:196
msgid ""
"Activation code has already been sent. You can request a new code in 24 "
"hours."
msgstr ""

#: .\cameras\forms.py:214
msgid "First name"
msgstr ""

#: .\cameras\forms.py:215
msgid "Last name"
msgstr ""

#: .\cameras\forms.py:229
msgid "Please enter another email."
msgstr ""

#: .\cameras\forms.py:235
msgid "You can not use this mail."
msgstr ""

#: .\cameras\templates\cameras\emails\activate_profile.html:15
#: .\cameras\templates\cameras\emails\activate_profile.txt:3
msgid "To activate your profile, please follow this link:"
msgstr ""

#: .\cameras\templates\cameras\emails\change_email.html:15
#: .\cameras\templates\cameras\emails\change_email.txt:3
msgid "To change your current email address, please follow the link:"
msgstr ""

#: .\cameras\templates\cameras\emails\forgotten_username.html:15
#: .\cameras\templates\cameras\emails\forgotten_username.txt:3
msgid "Your username is:"
msgstr ""

#: .\cameras\templates\cameras\emails\restore_password_email.html:15
#: .\cameras\templates\cameras\emails\restore_password_email.txt:3
msgid ""
"You received this email because you requested a password reset for your user "
"account."
msgstr ""

#: .\cameras\templates\cameras\emails\restore_password_email.html:19
#: .\cameras\templates\cameras\emails\restore_password_email.txt:5
msgid "Please, go to the following page and choose a new password:"
msgstr ""

#: .\cameras\templates\cameras\log_in.html:7
#: .\cameras\templates\cameras\log_in.html:14
#: .\cameras\templates\cameras\remind_username.html:22
#: .\cameras\templates\cameras\resend_activation_code.html:22
#: .\cameras\templates\cameras\restore_password.html:22
#: .\content\templates\layouts\default\page.html:56
msgid "Log in"
msgstr ""

#: .\cameras\templates\cameras\log_in.html:22
msgid "Forgot your password?"
msgstr ""

#: .\cameras\templates\cameras\log_in.html:25
msgid "Forgot your username?"
msgstr ""

#: .\cameras\templates\cameras\log_in.html:28
#: .\cameras\templates\cameras\resend_activation_code.html:7
msgid "Resend an activation code"
msgstr ""

#: .\cameras\templates\cameras\log_out.html:7
msgid "Logged out"
msgstr ""

#: .\cameras\templates\cameras\log_out.html:10
msgid "Thanks for spending some quality time with the site today."
msgstr ""

#: .\cameras\templates\cameras\log_out.html:14
msgid "Log in again"
msgstr ""

#: .\cameras\templates\cameras\log_out_confirm.html:7
#: .\cameras\templates\cameras\log_out_confirm.html:15
#: .\content\templates\layouts\default\page.html:52
msgid "Log out"
msgstr ""

#: .\cameras\templates\cameras\log_out_confirm.html:10
msgid "Confirm this action:"
msgstr ""

#: .\cameras\templates\cameras\profile\change_email.html:7
#: .\cameras\utils.py:32 .\content\templates\layouts\default\page.html:49
msgid "Change email"
msgstr ""

#: .\cameras\templates\cameras\profile\change_email.html:14
#: .\cameras\templates\cameras\profile\change_password.html:14
#: .\cameras\templates\cameras\profile\change_profile.html:14
#: .\cameras\templates\cameras\restore_password_confirm.html:20
#: .\content\templates\main\change_language.html:29
msgid "Change"
msgstr ""

#: .\cameras\templates\cameras\profile\change_password.html:7
#: .\content\templates\layouts\default\page.html:43
msgid "Change password"
msgstr ""

#: .\cameras\templates\cameras\profile\change_profile.html:7
#: .\content\templates\layouts\default\page.html:46
msgid "Change profile"
msgstr ""

#: .\cameras\templates\cameras\remind_username.html:7
msgid "Remind a username"
msgstr ""

#: .\cameras\templates\cameras\remind_username.html:14
#: .\cameras\templates\cameras\resend_activation_code.html:14
#: .\cameras\templates\cameras\restore_password.html:14
msgid "Next"
msgstr ""

#: .\cameras\templates\cameras\restore_password.html:7
#: .\cameras\templates\cameras\restore_password_confirm.html:7
#: .\cameras\templates\cameras\restore_password_done.html:7
#: .\cameras\utils.py:43
msgid "Restore password"
msgstr ""

#: .\cameras\templates\cameras\restore_password_confirm.html:12
msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""

#: .\cameras\templates\cameras\restore_password_confirm.html:27
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used. Please request a new password reset."
msgstr ""

#: .\cameras\templates\cameras\restore_password_done.html:10
msgid ""
"We've emailed you instructions for setting your password, if an account "
"exists with the email you entered."
msgstr ""

#: .\cameras\templates\cameras\restore_password_done.html:11
msgid "You should receive them shortly."
msgstr ""

#: .\cameras\templates\cameras\restore_password_done.html:15
msgid ""
"If you don't receive an email, please make sure you've entered the address "
"you signed up with, and check your spam folder."
msgstr ""

#: .\cameras\templates\cameras\sign_up.html:7
#: .\content\templates\layouts\default\page.html:59
msgid "Create an account"
msgstr ""

#: .\cameras\templates\cameras\sign_up.html:14
msgid "Create"
msgstr ""

#: .\cameras\utils.py:21
msgid "Profile activation"
msgstr ""

#: .\cameras\utils.py:57
msgid "Your username"
msgstr ""

#: .\cameras\views.py:152
msgid ""
"You are signed up. To activate the account, follow the link sent to the mail."
msgstr ""

#: .\cameras\views.py:161
msgid "You are successfully signed up!"
msgstr ""

#: .\cameras\views.py:179
msgid "You have successfully activated your account!"
msgstr ""

#: .\cameras\views.py:211
msgid "A new activation code has been sent to your email address."
msgstr ""

#: .\cameras\views.py:257
msgid "Profile data has been successfully updated."
msgstr ""

#: .\cameras\views.py:294
msgid "To complete the change of email address, click on the link sent to it."
msgstr ""

#: .\cameras\views.py:301
msgid "Email successfully changed."
msgstr ""

#: .\cameras\views.py:319
msgid "You have successfully changed your email!"
msgstr ""

#: .\cameras\views.py:333
msgid "Your username has been successfully sent to your email."
msgstr ""

#: .\cameras\views.py:349
msgid "Your password was changed."
msgstr ""

#: .\cameras\views.py:363
msgid "Your password has been set. You may go ahead and log in now."
msgstr ""

#: .\deepseek\settings.py:150
msgid "English"
msgstr ""

#: .\deepseek\settings.py:151
msgid "Amharic"
msgstr ""

#: .\deepseek\settings.py:152
msgid "French"
msgstr ""

#: .\deepseek\settings.py:153
msgid "Spanish"
msgstr ""

#: .\content\templates\layouts\default\page.html:28
msgid "Home"
msgstr ""

#: .\content\templates\layouts\default\page.html:39
msgid "Django administration"
msgstr ""

#: .\content\templates\layouts\default\page.html:66
#: .\content\templates\main\change_language.html:10
msgid "Change language"
msgstr ""

#: .\content\templates\main\change_language.html:19
msgid "Select the language"
msgstr ""

#: .\content\templates\main\index.html:9
msgid "Your username is"
msgstr ""

#: .\content\templates\main\index.html:13
msgid "You are a guest."
msgstr ""
