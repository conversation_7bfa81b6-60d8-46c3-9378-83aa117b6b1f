Easy-RSA 3 ChangeLog

3.2.0 (TBD)

3.1.7 (2023-10-13)
   * Rewrite vars-auto-detect, adhere to EasyRSA-Advanced.md (#1029)
     Under the hood, this is a considerable change but there are no user
     noticable differences. With the exception of:
     Caveat: The default '$PWD/pki/vars' file is forbidden to change either
             EASYRSA or EASYRSA_PKI, which are both implied by default.
   * EasyRSA-Advanced.md: Correct vars-auto-detect hierarchy (#1029)
     Commit: ecd65065e3303da78811278a154ef7a969c2777b
             EASYRSA/vars is moved to a higher priority than a default PKI.
             vars-auto-detect no longer searches 'easyrsa' program directory.
   * gen-crl: preserve existing crl.pem ownership+mode (#1020)
   * New command: make-vars - Print vars.example (here-doc) to stdout (#1024)
   * show-expire: Calculate cert. expire seconds from DB date (#1023)
   * Update OpenSSL to 3.1.2

3.1.6 (2023-07-18)
   * New commands: 'inline' and 'x509-eku' (#993)
     inline: Build an inline file for a commonName
     x509-eku: Extract X509v3 extended key usage from a certificate
   * Expose serial-check, display-dn, display-san and default-san to
     command line. (#980) (Debugging functions, which remain undocumented)
   * Expand default status to include vars-file and CA status (#973)
   * sign-req: Allow the CSR DN-field order to be preserved (#970)

3.1.5 (2023-06-10)
   * Build Update: script now supports signing and verifying
   * Automate support-file creation (Free packaging) (#964)
   * build-ca: New command option 'raw-ca', abbrevation: 'raw' (#963)

     This 'raw' method, is the most reliable way to build a CA,
     with a password, without writing the CA password to a temp-file.

   This option completely replaces both methods below:

   * build-ca: New option --ca-via-stdin, use SSL -pass* argument 'stdin' (#959)
     Option '--ca-via-stdin' offers no more security than standard method.
     Easy-RSA version 3.1.4 ONLY.

   * build-ca: Replace password temp-files with file-descriptors (#955)
     Using file-descriptors does not work in Windows.
     Easy-RSA version 3.1.3 ONLY.

3.1.4 (2023-05-23)
   * build-ca: New option --ca-via-stdin, use SSL -pass* argument 'stdin' (#959)
   * build-ca: Revert manual CA password method to temp-files (#959)
     Supersedes #955

     Release v3.1.3 was fatally flawed, it would fail to build a CA under Windows.
     Release v3.1.4 is specifically a bugfix ONLY, to resolve the Windows problem.

     See the following commits for further details:
     5d7ad1306d5ebf1588aef77eb3445e70cf5b4ebc
         build-ca: Revert manual CA password method to temp-files
     c11135d19b2e7e7385d28abb1132978c849dfa74
         build-ca: Use OpenSSL password I/O argument 'stdin'
     27870d695a324e278854146afdac5d6bdade9bba
         build-ca: Replace password temp-file method with file-descriptors
         Superseded by 5d7ad13 above.

3.1.3 (2023-05-19)
   * build-ca: Replace password temp-files with file-descriptors (#955)
     Superseded by #959
   * Replace --fix-offset with --startdate, --enddate (#918)
   * Introduce option -S|--silent-ssl: Silence SSL output (#913)
   * Only create a random serial number file when expected (#896)
   * Always verify SSL lib, for all commands (#877)
   * Option --fix-offset: Adjust off-by-one day (#847) Superseded (#918)
   * Update OpenSSL to v3.0.8

3.1.2 (2023-01-13)
   * build-full: Always enable inline file creation (#834)
   * Make default Edwards curve ED25519 (#828)
   * Allow --fix-offset to create post-dated certificates (#804) Superseded (#918)
   * Introduce command 'set-pass' (#756)
   * Introduce global option '--nopass|--no-pass' (#752)
   * Introduce global option '--notext|--no-text' (#745)
   * Command 'help': For unknown command, exit with error (#737)
   * Find data-files in the correct order (#727 - Reported #725)
   * Update OpenSSL to 3.0.7 for Windows distribution

3.1.1 (2022-10-13)
   * Remove command 'renewable' (#715)
   * Expand 'show-renew', include 'renewed/certs_by_serial' (#700)
   * Resolve long-standing issue with --subca-len=N (#691)
   *  ++ NOTICE: Add EasyRSA-Renew-and-Revoke.md (#690)
   * Require 'openssl-easyrsa.cnf' is up to date (#695}
   * Introduce 'renew' (version 3). Only renew cert (#688)
   * Always ensure X509-types files exist (#581 #696)
   * Expand alias '--days' to all suitable options with a period (#674)
   * Introduce --keep-tmp, keep temp files for debugging (#667)
   * Add serialNumber (OID 2.5.4.5) to DN 'org' mode (#606)
   * Support ampersand and dollar-sign in vars file (#590)
   * Introduce 'rewind-renew' (#579)
   * Expand status reports to include checking a single cert (#577)
   * Introduce 'revoke-renewed' (#547)
   * update OpenSSL for Windows to 3.0.5

3.1.0 (2022-05-18)
   * Introduce basic support for OpenSSL version 3 (#492)
   * Update regex in grep to be POSIX compliant (#556)
   * Introduce status reporting tools (#555 & #557)
   * Display certificates using UTF8 (#551)
   * Allow certificates to be created with fixed date offset (#550)
   * Add 'verify' to verify certificate against CA (#549)
   * Add PKCS#12 alias 'friendlyName' (#544)
   * Support multiple IP-Addresses in SAN (#564)
   * Add option '--renew-days=NN', custom renew grace period (#557)
   * Add 'nopass' option to the 'export-pkcs' functions (#411)
   * Add support for 'busybox' (#543)
   * Add option '--tmp-dir=DIR' to declare Temp-dir (Commit f503a22)   

3.0.9 (2022-05-17)
   * Upgrade OpenSSL from 1.1.0j to 1.1.1o (#405, #407) 
      - We are buliding this ourselves now.
   * Fix --version so it uses EASYRSA_OPENSSL (#416)
   * Use openssl rand instead of non-POSIX mktemp (#478)
   * Fix paths with spaces (#443)
   * Correct OpenSSL version from Homebrew on macOs (#416)
   * Fix revoking a renewed certificate (Original PR #394)
     Follow-up commit: ef22701878bb10df567d60f2ac50dce52a82c9ee
   * Introduce 'show-crl' (d1993892178c5219f4a38d50db3b53d1a972b36c)
   * Support Windows-Git 'version of bash' (#533)
   * Disallow use of single quote (') in vars file, Warning (#530)
   * Creating a CA uses x509-types/ca and COMMON (#526)
   * Prefer 'PKI/vars' over all other locations (#528)
   * Introduce 'init-pki soft'  option (#197)
   * Warnings are no longer silenced by --batch (#523)
   * Improve packaging options (#510)
   * Update regex for POSIX compliance (#556)
   * Correct date format for Darwin/BSD (#559)

3.0.8 (2020-09-09)
   * Provide --version option (#372)
   * Version information now within generated certificates like on *nix
   * Fixed issue where gen-dh overwrote existing files without warning (#373)
   * Fixed issue with ED/EC certificates were still signed by RSA (#374)
   * Added support for export-p8 (#339)
   * Clarified error message (#384)
   * 2->3 upgrade now errors and prints message when vars isn't found (#377)

3.0.7 (2020-03-30)
   * Include OpenSSL libs and binary for Windows 1.1.0j
   * Remove RANDFILE environment variable (#261)
   * Workaround for bug in win32 mktemp (#247, #305, PR #312)
   * Handle IP address in SAN and renewals (#317)
   * Workaround for ash and no set -o echo (#319)
   * Shore up windows testing framework (#314)
   * Provide upgrade mechanism for older versions of EasyRSA (#349)
   * Add support for KDC certificates (#322)
   * Add support for Edward Curves (#354, #350)
   * Add support for EASYRSA_PASSIN and EASYRSA_PASSOUT env vars (#368)
   * Add support for RID to SAN (#362)

3.0.6 (2019-02-01)
   * Certificates that are revoked now move to a revoked subdirectory (#63)
   * EasyRSA no longer clobbers non-EASYRSA environment variables (#277)
   * More sane string checking, allowing for commas in CN (#267)
   * Support for reasonCode in CRL (#280)
   * Better handling for capturing passphrases (#230, others)
   * Improved LibreSSL/MacOS support
   * Adds support to renew certificates up to 30 days before expiration (#286)
     - This changes previous behavior allowing for certificate creation using
       duplicate CNs.

3.0.5 (2018-09-15)
   * Fix #17 & #58: use AES256 for CA key
   * Also, don't use read -s, use stty -echo
   * Fix broken "nopass" option
   * Add -r to read to stop errors reported by shellcheck (and to behave)
   * Remove overzealous quotes around $pkcs_opts (more SC errors)
   * Support for LibreSSL
   * EasyRSA version will be reported in certificate comments
   * Client certificates now expire in 3 year (1080 days) by default

3.0.4 (2018-01-21)
    * Remove use of egrep (#154)
    * Integrate with Travis-CI (#165)
    * Remove "local" from variable assignment (#165)
    * Other changes related to Travis-CI fixes
	* Assign values to variables defined previously w/local
    * Finally(?) fix the subjectAltName issues I presented earlier (really
      fixes #168)

3.0.3 (2017-08-22)
    * Include mktemp windows binary
    * copy CSR extensions into signed certificate


3.0.2 (2017-08-21)
    * Add missing windows binaries


3.0.1 (2015-10-25)
    * Correct some packaging errors


3.0.0 (2015-09-07)

    * cab4a07 Fix typo: Hellman
        (ljani: Github)

    * 171834d Fix typo: Default
        (allo-: Github)

    * 8b42eea Make aes256 default, replacing 3des
        (keros: Github)

    * f2f4ac8 Make -utf8 default
        (roubert: Github)


3.0.0-rc2 (2014/07/27)

    * 1551e5f docs: fix typo
        (Josh Cepek <<EMAIL>>)

    * 7ae44b3 Add KNOWN_ISSUES to stage next -rc release
        (Josh Cepek <<EMAIL>>)

    * a0d58b2 Update documentation
        (Josh Cepek <<EMAIL>>)

    * 5758825 Fix vars.example with proper path to extensions.temp
        (Josh Cepek <<EMAIL>>)

    * 89f369c Add support to change private key passphrases
        (Josh Cepek <<EMAIL>>)

    * 49d7c10 Improve docs: add Upgrade-Notes; add online support refs
        (Josh Cepek <<EMAIL>>)

    * fcc4547 Add build-dist packaging script; update Building docs
        (Josh Cepek <<EMAIL>>)

    * f74d08e docs: update Hacking.md with layout & git conventions
        (Josh Cepek <<EMAIL>>)

    * 0754f23 Offload temp file removal to a clean_temp() function
        (Josh Cepek <<EMAIL>>)

    * 1c90df9 Fix incorrect handling of invalid --use-algo option
        (Josh Cepek <<EMAIL>>)

    * c86289b Fix batch-mode handling with changes in e75ad75
        (Josh Cepek <<EMAIL>>)

    * e75ad75 refine how booleans are evaluated
        (Eric F Crist <<EMAIL>>)

    * cc19823 Merge PKCS#7 feature from pull req #14
        (Author: Luiz Angelo Daros de Luca <<EMAIL>>)
        (Modified-By: Josh Cepek <<EMAIL>>)

    * 8b1fe01 Support OpenSSL-0.9.8 with the EXTRA_EXTS feature
        (Josh Cepek <<EMAIL>>)

    * d5516d5 Windows: make builds easier by using a matching dir structure
        (Josh Cepek <<EMAIL>>)

    * dc2e6dc Windows: improve external checks and env-var help
        (Josh Cepek <<EMAIL>>)

3.0.0-rc1 (2013/12/01)

    * The 3.x release is a nearly complete re-write of the 2.x codebase

    * Initial 3.x series code by Josh Cepek <<EMAIL>> -- continuing
    maintenance by the OpenVPN community development team and associated
    contributors

    * Add ECDSA (elliptic curve) support, thanks to Steffan Karger
    <<EMAIL>>
