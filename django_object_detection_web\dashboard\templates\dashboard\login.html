{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}Login - Eagle CCTV System{% endblock %}

{% block auth_content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-6 col-lg-5">
        <div class="card border-0 shadow-lg">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <h2 style="color: rgb(255, 255, 255);">Login</h2>
                    <p class="text-muted">Sign in to access the surveillance system</p>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                
                <form method="POST" action="{% url 'dashboard:login' %}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                        {{ form.username }} 
                        {% if form.username.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.username.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">Password</label>
                        {{ form.password }} 
                        {% if form.password.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.remember }} {# Assuming widget attrs set in forms.py for class="form-check-input" #}
                        <label class="form-check-label" for="{{ form.remember.id_for_label }}">Remember me</label>
                         {% if form.remember.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.remember.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Log In</button>
                    </div>
                </form>
                
                <div class="text-center mt-4 text-muted">
                    <p>Secure access to monitoring system</p>
                    </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}