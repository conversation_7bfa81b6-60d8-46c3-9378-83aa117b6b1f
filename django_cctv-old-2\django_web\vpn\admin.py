from django.contrib import admin
from .models import VpnClient, VpnLog

@admin.register(VpnClient)
class VpnClientAdmin(admin.ModelAdmin):
    list_display = ('name', 'client_id', 'virtual_ip', 'status', 'last_seen')
    list_filter = ('status',)
    search_fields = ('name', 'client_id', 'virtual_ip', 'real_ip')
    readonly_fields = ('connected_since', 'last_seen')

@admin.register(VpnLog)
class VpnLogAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'client', 'event_type', 'ip_address', 'short_message')
    list_filter = ('event_type', 'client')
    search_fields = ('message', 'client__name', 'ip_address')
    readonly_fields = ('timestamp', 'client', 'event_type', 'message', 'ip_address', 'additional_data')
    
    def short_message(self, obj):
        """Display a shortened version of the message for the list view."""
        if len(obj.message) > 50:
            return f"{obj.message[:50]}..."
        return obj.message
    short_message.short_description = 'Message'