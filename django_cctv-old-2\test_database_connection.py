#!/usr/bin/env python3
"""
Test script to verify database connections for both Django services.
Run this script inside each Django container to test database connectivity.
"""

import os
import sys
import django
from django.conf import settings
from django.db import connection
from django.core.management import execute_from_command_line

def test_database_connection():
    """Test the database connection."""
    try:
        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result and result[0] == 1:
                print("✅ Database connection successful!")
                return True
            else:
                print("❌ Database connection failed - unexpected result")
                return False
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def get_database_info():
    """Get database configuration information."""
    db_config = settings.DATABASES['default']
    print(f"Database Engine: {db_config['ENGINE']}")
    print(f"Database Name: {db_config['NAME']}")
    print(f"Database User: {db_config['USER']}")
    print(f"Database Host: {db_config['HOST']}")
    print(f"Database Port: {db_config['PORT']}")

def test_session_config():
    """Test session configuration."""
    print(f"Session Cookie Name: {getattr(settings, 'SESSION_COOKIE_NAME', 'sessionid')}")
    print(f"CSRF Cookie Name: {getattr(settings, 'CSRF_COOKIE_NAME', 'csrftoken')}")
    print(f"Session Cookie Age: {getattr(settings, 'SESSION_COOKIE_AGE', 1209600)} seconds")

def main():
    """Main test function."""
    print("🔍 Testing Django Database Configuration")
    print("=" * 50)
    
    # Get service name from environment or default
    service_name = os.environ.get('SERVICE_NAME', 'Unknown Service')
    print(f"Service: {service_name}")
    print()
    
    # Display database configuration
    print("📊 Database Configuration:")
    get_database_info()
    print()
    
    # Test database connection
    print("🔌 Testing Database Connection:")
    connection_success = test_database_connection()
    print()
    
    # Display session configuration
    print("🍪 Session Configuration:")
    test_session_config()
    print()
    
    if connection_success:
        print("✅ All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
