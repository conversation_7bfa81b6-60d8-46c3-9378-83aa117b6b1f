
Here's how it's supposed to work when everything is configured correctly (Updated after some fixing made):

1. VPN Tunnel Establishment:
    
- The OpenVPN client app and the IP Webcam application of my samsung A6 tablet starts.
- Using the remote openvpn 1194 udp directive in its .ovpn file (downloaded from OpenVPN and manually edited), OpenVPN client app resolves openvpn to the internal Docker IP of the openvpn container (e.g., 172.18.0.x).
- OpenVPN client app connects to the server over the cctv_net Docker network.
- OpenVPN client app and server perform a TLS handshake.
- If successful, the OpenVPN assigns a VPN IP address.
- The IP Webcam client creates a virtual network interface called tun0 (or similar) and assigns the IP to it.
- The client also adds routes to its internal routing table, typically sending traffic destined for networks specified by the server (including the server's internal VPN endpoint).

2. Shinobi Requesting the Stream:
- You configure a monitor in the Shinobi UI with the source URL rtsp://<IP Webcam_VPN_IP>:8554/stream1.
- The shinobi_nvr container (running on the Docker network) needs to send an RTSP connection request packet to the destination IP.
- Since this VPN IP is not on its local Docker network, shinobi_nvr sends the packet to its default gateway.

Crucially for this to work: 
- The routing on the Docker host and within the openvpn container must direct this packet correctly. 
- The openvpn needs to know that the IP ************* belongs to a connected client and route the packet from its eth0 (Docker network interface) to its eth0 or tun0 (VPN tunnel interface).

3. OpenVPN Routing:
- The openvpn container receives the packet from shinobi_nvr on its eth0 interface, destined for the client's VPN IP.
- With IP forwarding enabled (which is usually default within the OpenVPN container for VPN functionality) and its internal routing/NAT rules, the Access Server forwards the packet out through its VPN tunnel interface towards the correct client. 
- OpenVPN typically handles the necessary routing and NAT/firewall rules internally to allow communication to/from connected clients if the network settings are configured to permit it (e.g., "Should private subnets be accessible to clients by default?" set to yes, and potentially "Allow access from these private subnets to VPN clients" also configured if needed).

4. IP Webcam Receiving the Stream Request:
- The IP Webcam container receives the RTSP request packet on its tun0 interface.
- The MediaMTX process, listening on 0.0.0.0:8554 (which includes the tun0 IP address), accepts the connection.

5. RTSP Stream Flow:
- MediaMTX starts sending the RTSP video data back to shinobi_nvr.
The reply packets go from IP Webcam through its tun0 interface to the OpenVPN.
- The Access Server forwards the packets out through its eth0 towards shinobi_nvr (172.18.0.y).
- Shinobi receives the stream.

Moving Forward with OpenVPN:

- Confirm `IP Webcam` VPN IP: When IP Webcam connects, confirm its VPN IP from the openvpn Admin UI (Status -> Current Users) or from inside IP Webcam (ip addr show tun0).