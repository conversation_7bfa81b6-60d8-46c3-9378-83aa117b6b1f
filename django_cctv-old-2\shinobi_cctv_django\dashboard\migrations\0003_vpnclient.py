# Generated by Django 5.2 on 2025-05-31 22:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0002_location_vpn_client_config_location_vpn_enabled_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='VpnClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_name', models.Char<PERSON>ield(max_length=64)),
                ('cert_cn', models.Char<PERSON>ield(max_length=128, unique=True)),
                ('ovpn_path', models.CharField(max_length=256)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('revoked', models.BooleanField(default=False)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vpn_clients', to='dashboard.location')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vpn_clients', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
