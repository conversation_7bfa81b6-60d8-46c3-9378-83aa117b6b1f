{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}Object Detection Dashboard{% endblock %}

{% block extra_css %}
<style>
    .od-card {
        border-left: 4px solid #007bff;
        transition: transform 0.2s;
    }
    .od-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .od-status-enabled {
        color: #28a745;
        font-weight: bold;
    }
    .od-status-disabled {
        color: #dc3545;
        font-weight: bold;
    }
    .od-stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .od-detection-mode {
        font-size: 0.9em;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">🎯 Object Detection Dashboard</h1>
                    <p class="text-muted">Monitor and configure object detection across your cameras</p>
                </div>
                <div>
                    <a href="{% url 'dashboard:object_detection_events' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> View Events
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card od-stats-card">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ total_cameras }}</h3>
                    <p class="mb-0">Total Cameras</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ od_enabled_cameras }}</h3>
                    <p class="mb-0">OD Enabled</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ total_detections_today }}</h3>
                    <p class="mb-0">Detections Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3 class="mb-0">{{ total_violations_today }}</h3>
                    <p class="mb-0">Violations Today</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Camera Configuration -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">📹 Camera Object Detection Status</h5>
                </div>
                <div class="card-body">
                    {% if cameras_with_config %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Camera</th>
                                        <th>Status</th>
                                        <th>Detection Mode</th>
                                        <th>Enabled Objects</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in cameras_with_config %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.camera.name }}</strong><br>
                                            <small class="text-muted">{{ item.camera.branch.name }}</small>
                                        </td>
                                        <td>
                                            {% if item.enabled %}
                                                <span class="od-status-enabled">
                                                    <i class="fas fa-check-circle"></i> Enabled
                                                </span>
                                            {% else %}
                                                <span class="od-status-disabled">
                                                    <i class="fas fa-times-circle"></i> Disabled
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="od-detection-mode">{{ item.detection_mode|title }}</span>
                                        </td>
                                        <td>
                                            {% if item.enabled_objects %}
                                                {% for obj in item.enabled_objects %}
                                                    <span class="badge bg-secondary me-1">{{ obj|title }}</span>
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-muted">None configured</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'dashboard:object_detection_config' item.camera.id %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-cog"></i> Configure
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-video fa-3x text-muted mb-3"></i>
                            <h5>No cameras available</h5>
                            <p class="text-muted">Add cameras to start using object detection.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Events -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🚨 Recent Detection Events</h5>
                </div>
                <div class="card-body">
                    {% if recent_events %}
                        {% for event in recent_events %}
                        <div class="d-flex align-items-center mb-3 p-2 border rounded">
                            <div class="flex-shrink-0">
                                {% if event.object_type == 'person' %}
                                    <i class="fas fa-user text-primary"></i>
                                {% elif event.object_type == 'car' %}
                                    <i class="fas fa-car text-success"></i>
                                {% elif event.object_type == 'truck' %}
                                    <i class="fas fa-truck text-warning"></i>
                                {% else %}
                                    <i class="fas fa-eye text-info"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold">{{ event.object_type|title }} {{ event.direction|title }}</div>
                                <small class="text-muted">{{ event.camera.name }}</small>
                                {% if event.speed_kmh %}
                                    <br><small class="text-info">{{ event.speed_kmh|floatformat:1 }} km/h</small>
                                {% endif %}
                            </div>
                            <div class="flex-shrink-0">
                                <small class="text-muted">{{ event.timestamp|timesince }} ago</small>
                            </div>
                        </div>
                        {% endfor %}
                        <div class="text-center">
                            <a href="{% url 'dashboard:object_detection_events' %}" class="btn btn-sm btn-outline-primary">
                                View All Events
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                            <p class="text-muted">No recent detection events</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh page every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
{% endblock %}
