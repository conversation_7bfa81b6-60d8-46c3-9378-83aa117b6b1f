{% extends 'dashboard/layout.html' %}
{% load static %}

{% block title %}Locations - ABC CCTV System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Location Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-locations">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
        {% if request.user.role.name == "Administrator" or request.user.is_superuser %} {# #}
        <a href="{% url 'dashboard:location_add' %}" class="btn btn-sm btn-primary">
            <i class="bi bi-plus-lg"></i> Add Location
        </a>
        {% endif %}
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">Filter by Status</span>
            <select class="form-select" id="status-filter">
                <option value="all" selected>All Statuses</option>
                <option value="online">Connected</option>
                <option value="offline">Disconnected</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" placeholder="Search locations..." id="location-search">
        </div>
    </div>
</div>

<div class="row">
    {% for location in locations %}
    <div class="col-md-6 col-lg-4 mb-4 location-card-item" data-location-id="{{ location.id }}" data-status="{% if location.vpn_status %}online{% else %}offline{% endif %}">
        <div class="card location-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 location-name">{{ location.name }}</h5>
                <span class="badge {% if location.vpn_status %}bg-success{% else %}bg-danger{% endif %} location-status-badge">
                    {% if location.vpn_status %}Connected{% else %}Disconnected{% endif %}
                </span>
            </div>
            <div class="card-body location-details">
                <p>
                    <strong>Address:</strong><br>
                    {{ location.address|default_if_none:"" }}<br>
                    {{ location.city|default_if_none:"" }}{% if location.city and location.state %}, {% endif %}{{ location.state|default_if_none:"" }} {{ location.zipcode|default_if_none:"" }}<br>
                    {{ location.country|default_if_none:"" }}
                </p>
                
                <div class="vpn-status {% if location.vpn_status %}vpn-connected{% else %}vpn-disconnected{% endif %}" 
                     data-vpn-indicator-for="{{ location.id }}">
                    <i class="bi {% if location.vpn_status %}bi-shield-check{% else %}bi-shield-x{% endif %}"></i>
                    {% if location.vpn_status %}VPN Connected{% else %}VPN Disconnected{% endif %}
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between mb-3">
                    <div>
                        <strong>Cameras:</strong> 
                        <span class="badge bg-secondary">{{ location.camera_set.count }}</span> {# Or location.cameras.count if related_name is 'cameras' #}
                    </div>
                    <div>
                        <strong>Last Online:</strong>
                        <span class="text-muted">{{ location.last_online|date:"Y-m-d H:i"|default:"Never" }}</span>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="{% url 'dashboard:cameras' %}?location={{ location.id }}" class="btn btn-sm btn-primary">
                        <i class="bi bi-camera-video"></i> View Cameras
                    </a>
                    
                    {% if request.user.role.name == "Administrator" or request.user.is_superuser %} {# #}
                    <a href="{% url 'dashboard:location_edit' location_id=location.id %}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12 text-center py-5">
        <h4 class="text-muted">No locations found</h4>
        <p>There are no locations configured in the system.</p>
        {% if request.user.role.name == "Administrator" or request.user.is_superuser %} {# #}
        <a href="{% url 'dashboard:location_add' %}" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> Add Location
        </a>
        {% endif %}
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.getElementById('status-filter');
    const locationSearch = document.getElementById('location-search');
    const locationCards = document.querySelectorAll('.location-card-item');
    
    function filterLocations() {
        const statusValue = statusFilter.value;
        const searchValue = locationSearch.value.toLowerCase();
        
        locationCards.forEach(card => {
            const cardStatus = card.dataset.status;
            const cardName = card.querySelector('.location-name').textContent.toLowerCase();
            const cardDetails = card.querySelector('.location-details').textContent.toLowerCase();
            
            const matchesStatus = statusValue === 'all' || cardStatus === statusValue;
            const matchesSearch = searchValue === '' || 
                                  cardName.includes(searchValue) || 
                                  cardDetails.includes(searchValue);
            
            if (matchesStatus && matchesSearch) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });
    }

    if (statusFilter) statusFilter.addEventListener('change', filterLocations);
    if (locationSearch) locationSearch.addEventListener('input', filterLocations);
    
    const refreshButton = document.getElementById('refresh-locations');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
            
            // In a real app, you'd fetch fresh location data and re-render or update.
            // For now, we'll just simulate a delay and potentially re-run filters
            // if there was a mechanism to fetch updated status for each location.
            // The original JS had an updateVpnStatus() for demo purposes; real implementation should use backend API if available
            // A full page reload or an API call that returns all locations' new statuses would be better.
            
            // Simple approach: reload the page to get fresh data from server
            // window.location.reload();
            // Or, if you have an API endpoint to fetch all location statuses:
            // fetchLocationsDataAndUpdateDOM(); 

            setTimeout(() => {
                filterLocations(); // Re-apply filters in case some JS changed status visually
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
                 // For demonstration, let's assume a full page reload is acceptable for a full refresh.
                 // Or simply provide a message.
                 // alert("Refresh functionality would typically re-fetch data from the server.");
                 window.location.reload(); // Simplest way to get fresh data
            }, 1000);
        });
    }
});
</script>
{% endblock %}