#!/usr/bin/env python3
"""
Network Configuration Manager for Dual-Mode CCTV System
Handles both standalone and organization VPN modes
"""
import subprocess
import json
import ipaddress
import socket
from typing import Dict, List, Optional, Tuple

class NetworkConfigManager:
    """Manages network configurations for dual-mode operation"""
    
    def __init__(self):
        self.config_file = "/app/network_config.json"
        self.current_mode = "standalone"
        self.network_interfaces = {}
        
    def detect_network_interfaces(self) -> Dict[str, str]:
        """Detect all available network interfaces and their IPs"""
        interfaces = {}

        try:
            import platform
            if platform.system() == "Windows":
                # Windows-specific detection using socket and netifaces if available
                try:
                    import netifaces
                    for interface in netifaces.interfaces():
                        addrs = netifaces.ifaddresses(interface)
                        if netifaces.AF_INET in addrs:
                            for addr in addrs[netifaces.AF_INET]:
                                ip = addr['addr']
                                if not ip.startswith('127.') and not ip.startswith('169.254'):
                                    interfaces[interface] = ip
                except ImportError:
                    # Fallback for Windows without netifaces
                    hostname = socket.gethostname()
                    local_ip = socket.gethostbyname(hostname)
                    if not local_ip.startswith('127.'):
                        interfaces['default'] = local_ip

                    # Try to get more interfaces using socket
                    try:
                        # Connect to a remote address to find local IP
                        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                        s.connect(("*******", 80))
                        primary_ip = s.getsockname()[0]
                        s.close()
                        if primary_ip != local_ip:
                            interfaces['primary'] = primary_ip
                    except:
                        pass
            else:
                # Linux/Mac detection using ip command
                result = subprocess.run(['ip', 'addr', 'show'],
                                      capture_output=True, text=True)

                # Parse interface information
                current_interface = None
                for line in result.stdout.split('\n'):
                    if ': ' in line and 'inet ' not in line:
                        # Interface line
                        parts = line.split(': ')
                        if len(parts) > 1:
                            current_interface = parts[1].split('@')[0]
                    elif 'inet ' in line and current_interface:
                        # IP address line
                        ip_part = line.strip().split('inet ')[1].split('/')[0]
                        if not ip_part.startswith('127.'):  # Skip localhost
                            interfaces[current_interface] = ip_part

        except Exception as e:
            print(f"Error detecting interfaces: {e}")
            # Ultimate fallback to socket method
            try:
                hostname = socket.gethostname()
                interfaces['default'] = socket.gethostbyname(hostname)
            except:
                interfaces['default'] = '127.0.0.1'

        return interfaces
    
    def detect_network_mode(self) -> str:
        """Detect current network mode based on available interfaces"""
        interfaces = self.detect_network_interfaces()
        
        has_local_network = False
        has_vpn_network = False
        
        for interface, ip in interfaces.items():
            try:
                ip_obj = ipaddress.ip_address(ip)
                
                # Check for common local network ranges
                if (ip_obj in ipaddress.ip_network('***********/16') or
                    ip_obj in ipaddress.ip_network('10.0.0.0/8')):
                    has_local_network = True
                    
                # Check for common VPN ranges
                if (ip_obj in ipaddress.ip_network('**********/12') or
                    interface.startswith(('tun', 'tap', 'vpn'))):
                    has_vpn_network = True
                    
            except ValueError:
                continue
                
        if has_local_network and has_vpn_network:
            return "hybrid"
        elif has_vpn_network:
            return "organization_vpn"
        else:
            return "standalone"
    
    def get_shinobi_urls(self) -> Dict[str, str]:
        """Get appropriate Shinobi URLs based on network mode"""
        mode = self.detect_network_mode()
        interfaces = self.detect_network_interfaces()
        
        urls = {
            'backend_url': 'http://shinobi-nvr:8080',  # Internal Docker
            'frontend_url': 'http://localhost:8080',   # Default
            'api_url': 'http://shinobi-nvr:8080'       # Internal Docker
        }
        
        # In organization VPN mode, we might need different URLs
        if mode in ['organization_vpn', 'hybrid']:
            # Try to find VPN interface IP
            for interface, ip in interfaces.items():
                if (interface.startswith(('tun', 'tap', 'vpn')) or
                    self._is_vpn_ip(ip)):
                    urls['vpn_frontend_url'] = f'http://{ip}:8080'
                    break
                    
        return urls
    
    def _is_vpn_ip(self, ip: str) -> bool:
        """Check if IP is likely a VPN IP"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            return ip_obj in ipaddress.ip_network('**********/12')
        except ValueError:
            return False
    
    def get_camera_discovery_ranges(self) -> List[str]:
        """Get IP ranges to scan for cameras based on network mode"""
        mode = self.detect_network_mode()
        interfaces = self.detect_network_interfaces()
        
        ranges = []
        
        for interface, ip in interfaces.items():
            try:
                ip_obj = ipaddress.ip_address(ip)
                
                # Add network range for this interface
                if ip_obj in ipaddress.ip_network('***********/16'):
                    # Local network
                    network = ipaddress.ip_network(f'{ip}/24', strict=False)
                    ranges.append(str(network))
                elif ip_obj in ipaddress.ip_network('**********/12'):
                    # VPN network
                    network = ipaddress.ip_network(f'{ip}/24', strict=False)
                    ranges.append(str(network))
                elif ip_obj in ipaddress.ip_network('10.0.0.0/8'):
                    # Private network
                    network = ipaddress.ip_network(f'{ip}/24', strict=False)
                    ranges.append(str(network))
                    
            except ValueError:
                continue
                
        return ranges
    
    def save_config(self, config: Dict):
        """Save network configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def load_config(self) -> Dict:
        """Load network configuration from file"""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.get_default_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """Get default network configuration"""
        return {
            'mode': 'standalone',
            'interfaces': self.detect_network_interfaces(),
            'shinobi_urls': self.get_shinobi_urls(),
            'camera_ranges': self.get_camera_discovery_ranges(),
            'last_updated': None
        }
    
    def update_config(self):
        """Update configuration based on current network state"""
        import datetime
        import platform

        # Get current timestamp in a cross-platform way
        try:
            if platform.system() == "Windows":
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            else:
                timestamp = subprocess.run(['date'], capture_output=True, text=True).stdout.strip()
        except:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        config = {
            'mode': self.detect_network_mode(),
            'interfaces': self.detect_network_interfaces(),
            'shinobi_urls': self.get_shinobi_urls(),
            'camera_ranges': self.get_camera_discovery_ranges(),
            'last_updated': timestamp
        }

        self.save_config(config)
        return config

def main():
    """Test the network configuration manager"""
    manager = NetworkConfigManager()
    
    print("🔍 Network Configuration Analysis")
    print("=" * 50)
    
    # Detect interfaces
    interfaces = manager.detect_network_interfaces()
    print(f"📡 Network Interfaces: {interfaces}")
    
    # Detect mode
    mode = manager.detect_network_mode()
    print(f"🌐 Network Mode: {mode}")
    
    # Get URLs
    urls = manager.get_shinobi_urls()
    print(f"🔗 Shinobi URLs: {urls}")
    
    # Get camera ranges
    ranges = manager.get_camera_discovery_ranges()
    print(f"📹 Camera Discovery Ranges: {ranges}")
    
    # Update config
    config = manager.update_config()
    print(f"💾 Configuration Updated: {config}")

if __name__ == '__main__':
    main()
