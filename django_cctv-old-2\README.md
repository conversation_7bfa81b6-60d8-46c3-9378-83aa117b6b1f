# 📹 Dockerized CCTV Management System

A fully containerized CCTV management system built with **Docker Desktop on Windows 11**.  
This project integrates **Django**, **PostgreSQL**, **PgAdmin4**, **Nginx**, **Shinobi NVR**, and **OpenVPN** to securely stream, manage, and monitor CCTV footage from distributed sources over a VPN.

---

## 📦 Components

| Service      | Description                                                |
|:-------------|:------------------------------------------------------------|
| **Django**     | Backend management application with REST API support        |
| **PostgreSQL** | Robust relational database for storing CCTV metadata        |
| **PgAdmin4**   | Web-based GUI for managing the PostgreSQL database          |
| **Nginx**      | Reverse proxy for securing and managing web services        |
| **Shinobi**    | Open-source CCTV video management and recording platform    |
| **OpenVPN**    | Secure VPN tunnel for transmitting CCTV streams remotely    |

---

## 🚀 Getting Started

### 📥 Clone the repository

```bash
git clone https://github.com/yourusername/your-repo-name.git
cd your-repo-name

![CodeRabbit Pull Request Reviews](https://img.shields.io/coderabbit/prs/github/AbinetOlqabaa/django-cctv-system?utm_source=oss&utm_medium=github&utm_campaign=AbinetOlqabaa%2Fdjango-cctv-system&labelColor=171717&color=FF570A&link=https%3A%2F%2Fcoderabbit.ai&label=CodeRabbit+Reviews)